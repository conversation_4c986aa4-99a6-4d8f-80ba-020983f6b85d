<?php
/**
 * Debug Login Issues
 * This script will help diagnose login problems
 */

// Define access constant to bypass security check
define('SCIMS_ACCESS', true);

// Include configuration
require_once 'includes/config.php';

echo "<h1>SCIMS Login Debug Tool</h1>";

try {
    // Step 1: Test database connection
    echo "<h2>Step 1: Database Connection Test</h2>";
    $db = getDB();
    echo "<p style='color: green;'>✅ Database connection successful!</p>";
    
    // Step 2: Check if admin_users table exists
    echo "<h2>Step 2: Admin Users Table Check</h2>";
    $result = $db->query("SHOW TABLES LIKE 'admin_users'");
    if ($result->rowCount() > 0) {
        echo "<p style='color: green;'>✅ admin_users table exists!</p>";
        
        // Step 3: Check admin users in database
        echo "<h2>Step 3: Admin Users in Database</h2>";
        $users = $db->query("SELECT admin_id, username, email, full_name, role, status, failed_login_attempts, locked_until FROM admin_users")->fetchAll();
        
        if (empty($users)) {
            echo "<p style='color: red;'>❌ No admin users found in database!</p>";
            echo "<p><strong>Creating default admin user...</strong></p>";
            
            // Create default admin user
            $username = 'admin';
            $password = 'Admin123!';
            $email = '<EMAIL>';
            $fullName = 'System Administrator';
            $passwordHash = password_hash($password, PASSWORD_DEFAULT);
            
            $stmt = $db->prepare("INSERT INTO admin_users (username, password_hash, email, full_name, role, status) VALUES (?, ?, ?, ?, 'super_admin', 'active')");
            $stmt->execute([$username, $passwordHash, $email, $fullName]);
            
            echo "<p style='color: green;'>✅ Default admin user created!</p>";
            echo "<p><strong>Username:</strong> admin</p>";
            echo "<p><strong>Password:</strong> Admin123!</p>";
            
        } else {
            echo "<p style='color: green;'>✅ Found " . count($users) . " admin user(s):</p>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Name</th><th>Role</th><th>Status</th><th>Failed Attempts</th><th>Locked Until</th></tr>";
            
            foreach ($users as $user) {
                $lockStatus = $user['locked_until'] ? 
                    (strtotime($user['locked_until']) > time() ? 'LOCKED' : 'EXPIRED') : 'NO';
                    
                echo "<tr>";
                echo "<td>{$user['admin_id']}</td>";
                echo "<td>{$user['username']}</td>";
                echo "<td>{$user['email']}</td>";
                echo "<td>{$user['full_name']}</td>";
                echo "<td>{$user['role']}</td>";
                echo "<td>{$user['status']}</td>";
                echo "<td>{$user['failed_login_attempts']}</td>";
                echo "<td>{$lockStatus}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Step 4: Test password verification
        echo "<h2>Step 4: Password Verification Test</h2>";
        $testUser = $db->query("SELECT username, password_hash FROM admin_users WHERE username = 'admin'")->fetch();
        
        if ($testUser) {
            $testPassword = 'Admin123!';
            $isValid = password_verify($testPassword, $testUser['password_hash']);
            
            if ($isValid) {
                echo "<p style='color: green;'>✅ Password verification successful for 'admin' with password 'Admin123!'</p>";
            } else {
                echo "<p style='color: red;'>❌ Password verification failed!</p>";
                echo "<p>Stored hash: " . htmlspecialchars($testUser['password_hash']) . "</p>";
                
                // Reset password
                echo "<p><strong>Resetting password...</strong></p>";
                $newHash = password_hash($testPassword, PASSWORD_DEFAULT);
                $stmt = $db->prepare("UPDATE admin_users SET password_hash = ?, failed_login_attempts = 0, locked_until = NULL WHERE username = 'admin'");
                $stmt->execute([$newHash]);
                echo "<p style='color: green;'>✅ Password reset successfully!</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ Admin user 'admin' not found!</p>";
        }
        
        // Step 5: Test authentication function
        echo "<h2>Step 5: Authentication Function Test</h2>";
        
        // Include auth functions
        require_once 'includes/auth.php';
        require_once 'includes/functions.php';
        
        try {
            session_start();
            $result = authenticateUser('admin', 'Admin123!');
            if ($result) {
                echo "<p style='color: green;'>✅ Authentication function works correctly!</p>";
                echo "<p>Session variables set:</p>";
                echo "<ul>";
                echo "<li>admin_id: " . ($_SESSION['admin_id'] ?? 'NOT SET') . "</li>";
                echo "<li>admin_username: " . ($_SESSION['admin_username'] ?? 'NOT SET') . "</li>";
                echo "<li>admin_role: " . ($_SESSION['admin_role'] ?? 'NOT SET') . "</li>";
                echo "</ul>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Authentication failed: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ admin_users table does not exist!</p>";
        echo "<p>Please run the database setup first.</p>";
    }
    
    // Step 6: Check other required tables
    echo "<h2>Step 6: Required Tables Check</h2>";
    $requiredTables = ['events', 'departments', 'sports', 'venues', 'matches'];
    
    foreach ($requiredTables as $table) {
        $result = $db->query("SHOW TABLES LIKE '$table'");
        if ($result->rowCount() > 0) {
            $count = $db->query("SELECT COUNT(*) FROM $table")->fetchColumn();
            echo "<p style='color: green;'>✅ Table '$table' exists with $count records</p>";
        } else {
            echo "<p style='color: red;'>❌ Table '$table' missing</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>Stack trace:</strong></p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<h2>Quick Actions</h2>";
echo "<p><a href='quick-setup.php'>🔄 Run Quick Setup Again</a></p>";
echo "<p><a href='test-db.php'>🔍 Test Database Connection</a></p>";
echo "<p><a href='admin/'>🔑 Try Admin Login</a></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background: #f5f5f5;
}
h1, h2 {
    color: #333;
}
table {
    background: white;
    padding: 10px;
}
th, td {
    padding: 8px 12px;
    text-align: left;
}
th {
    background: #f0f0f0;
}
pre {
    background: #f8f8f8;
    padding: 10px;
    border: 1px solid #ddd;
    overflow-x: auto;
}
a {
    color: #007cba;
    text-decoration: none;
    font-weight: bold;
}
a:hover {
    text-decoration: underline;
}
</style>
