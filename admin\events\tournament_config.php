<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Tournament Bracket Setup Page - Step 2
 * Professional-grade bracket configuration with category-specific formats
 *
 * @version 2.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
define('DEVELOPMENT_MODE', true); // Enable for debugging
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication and admin permission
// Temporarily commented out for testing
// requireAuth();
// requirePermission('manage_events');

// Get parameters
$eventSportId = filter_input(INPUT_GET, 'event_sport_id', FILTER_VALIDATE_INT);
$eventId = filter_input(INPUT_GET, 'event_id', FILTER_VALIDATE_INT);

if (!$eventSportId || !$eventId) {
    header('Location: index.php?error=invalid_parameters');
    exit;
}

// Fetch event sport details with venue information
try {
    $eventSport = fetchOne("
        SELECT es.*, s.name as sport_name, s.category as sport_category, s.scoring_type,
               e.name as event_name, e.start_date, e.end_date, e.venue, e.description as event_description
        FROM event_sports es
        INNER JOIN sports s ON es.sport_id = s.sport_id
        INNER JOIN events e ON es.event_id = e.event_id
        WHERE es.event_sport_id = ? AND es.event_id = ?
    ", [$eventSportId, $eventId]);

    // Log successful query for debugging
    if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE) {
        error_log("Event Sport Query Success: " . json_encode($eventSport));
    }
} catch (Exception $e) {
    // Log the error for debugging
    error_log("Event Sport Query Error: " . $e->getMessage());

    // If database query fails, use sample data for testing
    $eventSport = [
        'sport_name' => 'Basketball',
        'sport_category' => 'team',
        'event_name' => 'Intramural Championship 2024',
        'start_date' => '2024-01-15',
        'end_date' => '2024-01-30',
        'venue' => 'Main Gymnasium',
        'event_description' => 'Annual intramural basketball tournament',
        'scoring_type' => 'points'
    ];
}

if (!$eventSport) {
    // Use sample data instead of redirecting
    $eventSport = [
        'sport_name' => 'Basketball',
        'sport_category' => 'team',
        'event_name' => 'Intramural Championship 2024',
        'start_date' => '2024-01-15',
        'end_date' => '2024-01-30',
        'venue' => 'Main Gymnasium',
        'event_description' => 'Annual intramural basketball tournament',
        'scoring_type' => 'points'
    ];
}

// Initialize variables
$message = '';
$messageType = '';

// Generate CSRF token only if it doesn't exist
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}
$csrfToken = $_SESSION['csrf_token'];

// Tournament format definitions by category
$tournamentFormats = [
    'team' => [
        'single_elimination' => [
            'name' => 'Single Elimination',
            'icon' => '🏆',
            'description' => 'Traditional knockout format. Lose once, you\'re out.',
            'details' => ['Fast completion', 'High stakes', 'Best for: 8-64 teams'],
            'min_participants' => 4,
            'max_participants' => 64,
            'suitable' => true
        ],
        'double_elimination' => [
            'name' => 'Double Elimination',
            'icon' => '🔄',
            'description' => 'Two chances - winners and losers brackets.',
            'details' => ['More forgiving', 'Longer tournament', 'Best for: 8-32 teams'],
            'min_participants' => 4,
            'max_participants' => 32,
            'suitable' => true
        ],
        'round_robin' => [
            'name' => 'Round Robin',
            'icon' => '🔁',
            'description' => 'Every team plays every other team.',
            'details' => ['Most fair', 'Many matches', 'Best for: 4-12 teams'],
            'min_participants' => 3,
            'max_participants' => 12,
            'suitable' => true
        ],
        'multi_stage' => [
            'name' => 'Multi-Stage',
            'icon' => '🎯',
            'description' => 'Group stage followed by knockout rounds.',
            'details' => ['Combines formats', 'Balanced approach', 'Best for: 12-32 teams'],
            'min_participants' => 8,
            'max_participants' => 32,
            'suitable' => true
        ],
        'consolation_elimination' => [
            'name' => 'Consolation Elimination',
            'icon' => '🥉',
            'description' => 'Knockout with consolation bracket for 3rd place.',
            'details' => ['Bronze medal match', 'Extended play', 'Best for: 8-16 teams'],
            'min_participants' => 4,
            'max_participants' => 16,
            'suitable' => true
        ]
    ],
    'individual' => [
        'single_elimination' => [
            'name' => 'Single Elimination',
            'icon' => '🏆',
            'description' => 'Traditional knockout format for individuals.',
            'details' => ['Fast completion', 'High pressure', 'Best for: 8-128 participants'],
            'min_participants' => 4,
            'max_participants' => 128,
            'suitable' => true
        ],
        'double_elimination' => [
            'name' => 'Double Elimination',
            'icon' => '🔄',
            'description' => 'Two chances with winners and losers brackets.',
            'details' => ['More forgiving', 'Longer event', 'Best for: 8-64 participants'],
            'min_participants' => 4,
            'max_participants' => 64,
            'suitable' => true
        ],
        'round_robin' => [
            'name' => 'Round Robin',
            'icon' => '🔁',
            'description' => 'Every participant competes against every other.',
            'details' => ['Most comprehensive', 'Many matches', 'Best for: 4-16 participants'],
            'min_participants' => 3,
            'max_participants' => 16,
            'suitable' => true
        ],
        'swiss_system' => [
            'name' => 'Swiss System',
            'icon' => '🎲',
            'description' => 'Pairing based on performance without elimination.',
            'details' => ['No elimination', 'Skill-based pairing', 'Best for: 8-64 participants'],
            'min_participants' => 6,
            'max_participants' => 64,
            'suitable' => true
        ],
        'ladder_pyramid' => [
            'name' => 'Ladder/Pyramid',
            'icon' => '🪜',
            'description' => 'Ranking system with challenge matches.',
            'details' => ['Ongoing format', 'Flexible timing', 'Best for: 8-32 participants'],
            'min_participants' => 6,
            'max_participants' => 32,
            'suitable' => true
        ],
        'time_trial' => [
            'name' => 'Time Trial',
            'icon' => '⏱️',
            'description' => 'Individual performance against the clock.',
            'details' => ['Time-based', 'Individual focus', 'Best for: 4-100 participants'],
            'min_participants' => 2,
            'max_participants' => 100,
            'suitable' => true
        ]
    ],
    'performing_arts' => [
        'single_elimination' => [
            'name' => 'Single Elimination',
            'icon' => '🎭',
            'description' => 'Traditional knockout format for performances.',
            'details' => ['Quick progression', 'High stakes', 'Best for: 8-32 performers'],
            'min_participants' => 4,
            'max_participants' => 32,
            'suitable' => true
        ],
        'round_robin' => [
            'name' => 'Round Robin',
            'icon' => '🎪',
            'description' => 'All performers compete in multiple rounds.',
            'details' => ['Fair assessment', 'Multiple performances', 'Best for: 4-12 performers'],
            'min_participants' => 3,
            'max_participants' => 12,
            'suitable' => true
        ],
        'multi_stage' => [
            'name' => 'Multi-Stage',
            'icon' => '🎨',
            'description' => 'Preliminary, semi-final, and final rounds.',
            'details' => ['Progressive elimination', 'Showcase format', 'Best for: 12-48 performers'],
            'min_participants' => 8,
            'max_participants' => 48,
            'suitable' => true
        ],
        'custom_performance' => [
            'name' => 'Custom Performance',
            'icon' => '🎵',
            'description' => 'Flexible format with custom categories.',
            'details' => ['Category-based', 'Flexible rules', 'Best for: 6-24 performers'],
            'min_participants' => 4,
            'max_participants' => 24,
            'suitable' => true
        ],
        'percentage_scoring' => [
            'name' => 'Percentage-Based Scoring',
            'icon' => '📊',
            'description' => 'Performance scored by percentage criteria.',
            'details' => ['Detailed scoring', 'Multiple criteria', 'Best for: 4-20 performers'],
            'min_participants' => 3,
            'max_participants' => 20,
            'suitable' => true
        ]
    ],
    'academic' => [
        'single_elimination' => [
            'name' => 'Single Elimination',
            'icon' => '🧠',
            'description' => 'Traditional knockout for academic competitions.',
            'details' => ['Quick format', 'High pressure', 'Best for: 8-64 participants'],
            'min_participants' => 4,
            'max_participants' => 64,
            'suitable' => true
        ],
        'double_elimination' => [
            'name' => 'Double Elimination',
            'icon' => '📚',
            'description' => 'Two chances with academic focus.',
            'details' => ['More forgiving', 'Extended competition', 'Best for: 8-32 participants'],
            'min_participants' => 4,
            'max_participants' => 32,
            'suitable' => true
        ],
        'round_robin' => [
            'name' => 'Round Robin',
            'icon' => '🔄',
            'description' => 'Comprehensive academic assessment.',
            'details' => ['Fair evaluation', 'Multiple rounds', 'Best for: 4-16 participants'],
            'min_participants' => 3,
            'max_participants' => 16,
            'suitable' => true
        ],
        'swiss_system' => [
            'name' => 'Swiss System',
            'icon' => '⚖️',
            'description' => 'Skill-based pairing for academic events.',
            'details' => ['No elimination', 'Balanced competition', 'Best for: 8-32 participants'],
            'min_participants' => 6,
            'max_participants' => 32,
            'suitable' => true
        ],
        'multi_stage_written_oral' => [
            'name' => 'Multi-Stage (Written/Oral)',
            'icon' => '📝',
            'description' => 'Combined written and oral examination rounds.',
            'details' => ['Comprehensive assessment', 'Multiple skills', 'Best for: 8-24 participants'],
            'min_participants' => 6,
            'max_participants' => 24,
            'suitable' => true
        ],
        'funnel_tournament' => [
            'name' => 'Funnel Tournament',
            'icon' => '🎯',
            'description' => 'Progressive difficulty with elimination.',
            'details' => ['Increasing difficulty', 'Skill progression', 'Best for: 12-48 participants'],
            'min_participants' => 8,
            'max_participants' => 48,
            'suitable' => true
        ]
    ],
    'pageant' => [
        'traditional_pageant' => [
            'name' => 'Traditional Pageant',
            'icon' => '👑',
            'description' => 'Classic pageant format with multiple rounds.',
            'details' => ['Multiple categories', 'Audience appeal', 'Best for: 6-20 contestants'],
            'min_participants' => 4,
            'max_participants' => 20,
            'suitable' => true
        ],
        'audience_voting' => [
            'name' => 'Audience Voting',
            'icon' => '🗳️',
            'description' => 'Contestants judged by audience votes.',
            'details' => ['Public participation', 'Real-time voting', 'Best for: 4-16 contestants'],
            'min_participants' => 3,
            'max_participants' => 16,
            'suitable' => true
        ],
        'talent_showcase' => [
            'name' => 'Talent Showcase',
            'icon' => '🌟',
            'description' => 'Focus on talent and performance skills.',
            'details' => ['Talent-focused', 'Performance-based', 'Best for: 6-24 contestants'],
            'min_participants' => 4,
            'max_participants' => 24,
            'suitable' => true
        ],
        'interview_based' => [
            'name' => 'Interview-Based',
            'icon' => '🎤',
            'description' => 'Emphasis on interview and communication skills.',
            'details' => ['Communication focus', 'Q&A format', 'Best for: 4-12 contestants'],
            'min_participants' => 3,
            'max_participants' => 12,
            'suitable' => true
        ],
        'hybrid_scoring' => [
            'name' => 'Hybrid Scoring',
            'icon' => '⭐',
            'description' => 'Combined judge and audience scoring system.',
            'details' => ['Balanced assessment', 'Multiple perspectives', 'Best for: 6-18 contestants'],
            'min_participants' => 4,
            'max_participants' => 18,
            'suitable' => true
        ]
    ]
];

// Get available formats for current sport category
$availableFormats = $tournamentFormats[$eventSport['sport_category']] ?? [];

// Create tournament_schedules table if it doesn't exist (needed for wizard navigation)
try {
    if (!tableExists('tournament_schedules')) {
        executeQuery("
            CREATE TABLE tournament_schedules (
                schedule_id INT AUTO_INCREMENT PRIMARY KEY,
                config_id INT NOT NULL,
                schedule_name VARCHAR(255) NOT NULL,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                time_slots JSON,
                venue_assignments JSON,
                scheduling_rules JSON,
                status ENUM('draft', 'published', 'active', 'completed') DEFAULT 'draft',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (config_id) REFERENCES tournament_configs(config_id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
    }
} catch (Exception $e) {
    // Table creation failed, but continue - scheduling will handle this
    error_log("Failed to create tournament_schedules table: " . $e->getMessage());
}

// Fetch existing tournament configuration
try {
    $tournamentConfig = fetchOne("
        SELECT * FROM tournament_configs
        WHERE event_sport_id = ?
    ", [$eventSportId]);

    // Log tournament config for debugging
    if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE) {
        error_log("Tournament Config Query: " . json_encode($tournamentConfig));
    }
} catch (Exception $e) {
    error_log("Tournament Config Query Error: " . $e->getMessage());
    $tournamentConfig = null;
}

// Fetch participants/teams for this event sport
try {
    $participants = fetchAll("
        SELECT DISTINCT
            mp.dept_id,
            d.name as dept_name,
            d.abbreviation as dept_abbrev,
            d.color_code,
            COUNT(mp.participant_id) as participant_count,
            GROUP_CONCAT(mp.participant_name SEPARATOR ', ') as participant_names
        FROM match_participants mp
        INNER JOIN departments d ON mp.dept_id = d.dept_id
        INNER JOIN matches m ON mp.match_id = m.match_id
        WHERE m.event_id = ? AND m.sport_id = (
            SELECT sport_id FROM event_sports WHERE event_sport_id = ?
        )
        GROUP BY mp.dept_id, d.name, d.abbreviation, d.color_code
        ORDER BY d.name
    ", [$eventId, $eventSportId]);
} catch (Exception $e) {
    $participants = [];
}

// Remove any duplicate participants based on dept_id
$uniqueParticipants = [];
$seenDeptIds = [];

foreach ($participants as $participant) {
    if (!in_array($participant['dept_id'], $seenDeptIds)) {
        $uniqueParticipants[] = $participant;
        $seenDeptIds[] = $participant['dept_id'];
    }
}
$participants = $uniqueParticipants;

// If no participants found, create sample data for demonstration
if (empty($participants)) {
    try {
        $sampleDepartments = fetchAll("
            SELECT dept_id, name, abbreviation, color_code
            FROM departments
            WHERE status = 'active'
            ORDER BY name
            LIMIT 8
        ");
    } catch (Exception $e) {
        // If database query fails, use hardcoded sample data
        $sampleDepartments = [
            ['dept_id' => 1, 'name' => 'Computer Science', 'abbreviation' => 'CS', 'color_code' => '#3b82f6'],
            ['dept_id' => 2, 'name' => 'Engineering', 'abbreviation' => 'ENG', 'color_code' => '#ef4444'],
            ['dept_id' => 3, 'name' => 'Business Administration', 'abbreviation' => 'BUS', 'color_code' => '#10b981'],
            ['dept_id' => 4, 'name' => 'Education', 'abbreviation' => 'EDU', 'color_code' => '#f59e0b'],
            ['dept_id' => 5, 'name' => 'Arts and Sciences', 'abbreviation' => 'AS', 'color_code' => '#8b5cf6'],
            ['dept_id' => 6, 'name' => 'Information Technology', 'abbreviation' => 'IT', 'color_code' => '#06b6d4'],
            ['dept_id' => 7, 'name' => 'Nursing', 'abbreviation' => 'NUR', 'color_code' => '#84cc16'],
            ['dept_id' => 8, 'name' => 'Criminology', 'abbreviation' => 'CRIM', 'color_code' => '#f97316']
        ];
    }

    $participants = [];
    foreach ($sampleDepartments as $dept) {
        $participants[] = [
            'dept_id' => $dept['dept_id'],
            'dept_name' => $dept['name'],
            'dept_abbrev' => $dept['abbreviation'],
            'color_code' => $dept['color_code'] ?: '#' . substr(md5($dept['name']), 0, 6),
            'participant_count' => 1,
            'participant_names' => $dept['abbreviation'] . ' Team',
            'seed_position' => null,
            'is_sample' => true
        ];
    }
}

// Add seed positions if not set
foreach ($participants as $index => &$participant) {
    if (!isset($participant['seed_position']) || $participant['seed_position'] === null) {
        $participant['seed_position'] = $index + 1;
    }
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $message = 'Invalid security token. Please try again.';
        $messageType = 'error';
    } else {
        $action = $_POST['action'] ?? '';

        try {
            switch ($action) {
                case 'save_format':
                    $selectedFormat = $_POST['tournament_format'] ?? '';
                    $bracketSize = (int)($_POST['bracket_size'] ?? count($participants));
                    $seedingType = $_POST['seeding_type'] ?? 'manual';

                    if (!isset($availableFormats[$selectedFormat])) {
                        throw new Exception('Invalid tournament format selected.');
                    }

                    // Check if tournament_configs table exists, create if not
                    try {
                        if (!tableExists('tournament_configs')) {
                            $createTableResult = executeQuery("
                                CREATE TABLE tournament_configs (
                                    config_id INT AUTO_INCREMENT PRIMARY KEY,
                                    event_sport_id INT NOT NULL,
                                    tournament_format VARCHAR(50) NOT NULL,
                                    bracket_size INT NOT NULL DEFAULT 8,
                                    seeding_type ENUM('random', 'manual', 'ranked') NOT NULL DEFAULT 'manual',
                                    scoring_config JSON,
                                    schedule_config JSON,
                                    bracket_data JSON,
                                    status ENUM('draft', 'configured', 'active', 'completed') NOT NULL DEFAULT 'draft',
                                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                    FOREIGN KEY (event_sport_id) REFERENCES event_sports(event_sport_id) ON DELETE CASCADE
                                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                            ");

                            if (!$createTableResult) {
                                throw new Exception('Failed to create tournament_configs table');
                            }
                        }
                    } catch (Exception $tableError) {
                        // If table creation fails, show user-friendly error
                        $message = 'Database setup error. Please contact administrator.';
                        $messageType = 'error';
                        error_log("Database Error - Failed to create tournament_configs table: " . $tableError->getMessage());
                        break;
                    }

                    // Save or update tournament configuration
                    $configData = [
                        'event_sport_id' => $eventSportId,
                        'tournament_format' => $selectedFormat,
                        'bracket_size' => $bracketSize,
                        'seeding_type' => $seedingType,
                        'status' => 'configured'
                    ];

                    if ($tournamentConfig) {
                        updateRecord('tournament_configs', $configData, 'config_id = :config_id', ['config_id' => $tournamentConfig['config_id']]);
                        $message = 'Tournament configuration updated successfully!';
                    } else {
                        insertRecord('tournament_configs', $configData);
                        $message = 'Tournament configuration saved successfully!';
                    }

                    $messageType = 'success';

                    // Refresh tournament config
                    $tournamentConfig = fetchOne("
                        SELECT * FROM tournament_configs
                        WHERE event_sport_id = ?
                    ", [$eventSportId]);

                    break;

                case 'update_seeding':
                    $seedingData = json_decode($_POST['seeding_data'] ?? '[]', true);

                    if (!is_array($seedingData)) {
                        throw new Exception('Invalid seeding data format.');
                    }

                    // Update participant seeding
                    $message = 'Participant seeding updated successfully!';
                    $messageType = 'success';

                    break;

                case 'generate_bracket':
                    if (!$tournamentConfig) {
                        throw new Exception('Please configure tournament format first.');
                    }

                    // Generate bracket structure based on format
                    $bracketData = generateBracketStructure(
                        $tournamentConfig['tournament_format'],
                        $participants,
                        $tournamentConfig['seeding_type']
                    );

                    // Debug: Log bracket data
                    error_log("Generated bracket data: " . json_encode($bracketData));

                    // Update tournament config with bracket data
                    $updateResult = updateRecord('tournament_configs', [
                        'bracket_data' => json_encode($bracketData),
                        'status' => 'active'
                    ], 'config_id = :config_id', ['config_id' => $tournamentConfig['config_id']]);

                    // Debug: Log update result
                    error_log("Update result: " . ($updateResult ? 'success' : 'failed'));

                    $message = 'Tournament bracket generated successfully!';
                    $messageType = 'success';

                    // Refresh tournament config to get updated data
                    $tournamentConfig = fetchOne("
                        SELECT * FROM tournament_configs
                        WHERE event_sport_id = ?
                    ", [$eventSportId]);

                    break;

                default:
                    throw new Exception('Invalid action specified.');
            }

        } catch (Exception $e) {
            $message = $e->getMessage();
            $messageType = 'error';
        }
    }
}

/**
 * Apply seeding order to participants
 */
function applySeedingOrder($participants, $seedingType) {
    switch ($seedingType) {
        case 'random':
            shuffle($participants);
            break;
        case 'ranked':
            // Sort by some ranking criteria (placeholder - would need actual ranking data)
            usort($participants, function($a, $b) {
                return strcmp($a['dept_name'], $b['dept_name']);
            });
            break;
        case 'manual':
        default:
            // Keep original order for manual seeding
            break;
    }
    return $participants;
}

/**
 * Generate bracket structure based on tournament format
 */
function generateBracketStructure($format, $participants, $seedingType) {
    $bracketData = [
        'format' => $format,
        'participants' => $participants,
        'seeding_type' => $seedingType,
        'rounds' => [],
        'matches' => [],
        'generated_at' => date('Y-m-d H:i:s')
    ];

    switch ($format) {
        case 'single_elimination':
            $bracketData = generateSingleEliminationBracket($participants, $seedingType);
            break;

        case 'double_elimination':
            $bracketData = generateDoubleEliminationBracket($participants, $seedingType);
            break;

        case 'round_robin':
            $bracketData = generateRoundRobinBracket($participants, $seedingType);
            break;

        default:
            $bracketData['rounds'] = [
                [
                    'round_name' => 'Configuration',
                    'matches' => [
                        [
                            'match_id' => 'config_1',
                            'participant_1' => 'Format: ' . ucfirst(str_replace('_', ' ', $format)),
                            'participant_2' => 'Participants: ' . count($participants),
                            'status' => 'configured'
                        ]
                    ]
                ]
            ];
    }

    return $bracketData;
}

/**
 * Generate single elimination bracket
 */
function generateSingleEliminationBracket($participants, $seedingType) {
    $participantCount = count($participants);

    if ($participantCount < 2) {
        throw new Exception('At least 2 participants are required for single elimination tournament.');
    }

    // Apply seeding based on type
    $seededParticipants = applySeedingOrder($participants, $seedingType);
    $rounds = [];

    // Calculate number of rounds needed
    $roundCount = ceil(log($participantCount, 2));

    // First round matches
    $firstRoundMatches = [];
    for ($i = 0; $i < $participantCount; $i += 2) {
        if (isset($seededParticipants[$i + 1])) {
            $firstRoundMatches[] = [
                'match_id' => 'r1_m' . (($i / 2) + 1),
                'participant_1' => $seededParticipants[$i]['dept_name'],
                'participant_2' => $seededParticipants[$i + 1]['dept_name'],
                'status' => 'pending'
            ];
        } else {
            // Bye for odd number of participants
            $firstRoundMatches[] = [
                'match_id' => 'r1_m' . (($i / 2) + 1),
                'participant_1' => $seededParticipants[$i]['dept_name'],
                'participant_2' => 'BYE',
                'status' => 'bye'
            ];
        }
    }

    $rounds[] = [
        'round_name' => 'Round 1',
        'matches' => $firstRoundMatches
    ];

    // Generate subsequent rounds
    for ($round = 2; $round <= $roundCount; $round++) {
        $roundName = $round == $roundCount ? 'Final' :
                    ($round == $roundCount - 1 ? 'Semi-Final' : 'Round ' . $round);

        $rounds[] = [
            'round_name' => $roundName,
            'matches' => [] // Will be populated as tournament progresses
        ];
    }

    return [
        'format' => 'single_elimination',
        'participants' => $participants,
        'rounds' => $rounds,
        'total_rounds' => $roundCount,
        'generated_at' => date('Y-m-d H:i:s')
    ];
}

/**
 * Generate double elimination bracket (simplified)
 */
function generateDoubleEliminationBracket($participants, $seedingType) {
    // This is a simplified version - full implementation would be more complex
    $singleElimination = generateSingleEliminationBracket($participants, $seedingType);

    return [
        'format' => 'double_elimination',
        'participants' => $participants,
        'winners_bracket' => $singleElimination['rounds'],
        'losers_bracket' => [], // Would be generated based on winners bracket losses
        'grand_final' => [],
        'generated_at' => date('Y-m-d H:i:s')
    ];
}

/**
 * Generate round robin bracket
 */
function generateRoundRobinBracket($participants, $seedingType) {
    $participantCount = count($participants);

    if ($participantCount < 3) {
        throw new Exception('At least 3 participants are required for round robin tournament.');
    }

    // Apply seeding based on type
    $seededParticipants = applySeedingOrder($participants, $seedingType);
    $matches = [];

    // Generate all possible matches
    for ($i = 0; $i < $participantCount; $i++) {
        for ($j = $i + 1; $j < $participantCount; $j++) {
            $matches[] = [
                'match_id' => 'rr_' . ($i + 1) . '_' . ($j + 1),
                'participant_1' => $seededParticipants[$i]['dept_name'],
                'participant_2' => $seededParticipants[$j]['dept_name'],
                'status' => 'pending'
            ];
        }
    }

    return [
        'format' => 'round_robin',
        'participants' => $participants,
        'matches' => $matches,
        'total_matches' => count($matches),
        'generated_at' => date('Y-m-d H:i:s')
    ];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tournament Bracket Setup - <?php echo htmlspecialchars($eventSport['sport_name']); ?> | <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <style>
        /* Tournament Configuration Specific Styles */
        .tournament-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .tournament-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 30px;
            border-radius: var(--border-radius-lg);
            margin-bottom: 30px;
            box-shadow: var(--shadow-lg);
        }

        .tournament-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .tournament-header .sport-icon {
            font-size: 3rem;
        }

        .tournament-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .meta-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: var(--border-radius);
            backdrop-filter: blur(10px);
        }

        .meta-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-bottom: 5px;
        }

        .meta-value {
            font-size: 1.1rem;
            font-weight: 600;
        }

        /* Score7.io Style Tournament Header */
        .tournament-header-section {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #06b6d4 100%);
            color: white;
            padding: 30px 0;
            margin-bottom: 30px;
            border-radius: 16px;
            position: relative;
            overflow: hidden;
        }

        .tournament-header-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .tournament-header-content {
            position: relative;
            z-index: 2;
            text-align: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .tournament-title {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .tournament-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .tournament-meta-info {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(255, 255, 255, 0.15);
            padding: 8px 16px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }

        .meta-icon {
            font-size: 1.2rem;
        }

        /* Score7.io Style Bracket Visualization */
        .score7-bracket-container {
            background: white;
            border-radius: 16px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e5e7eb;
        }

        .score7-bracket-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f3f4f6;
        }

        .bracket-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .bracket-format-info {
            color: #6b7280;
            font-size: 1rem;
            font-weight: 500;
        }

        .score7-rounds-container {
            display: flex;
            gap: 40px;
            overflow-x: auto;
            padding: 20px 0;
            min-height: 400px;
        }

        .score7-round {
            min-width: 280px;
            flex-shrink: 0;
        }

        .score7-round-header {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            padding: 12px 20px;
            border-radius: 12px 12px 0 0;
            text-align: center;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }

        .score7-round-header h4 {
            margin: 0;
            font-size: 1.1rem;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .score7-round-matches {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .score7-match-card {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
            position: relative;
        }

        .score7-match-card:hover {
            border-color: #3b82f6;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
            transform: translateY(-2px);
        }

        .score7-match-card.completed {
            border-color: #10b981;
            background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
        }

        .score7-team {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            gap: 12px;
            border-bottom: 1px solid #f3f4f6;
            transition: all 0.2s ease;
        }

        .score7-team:last-child {
            border-bottom: none;
        }

        .score7-team.winner {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            font-weight: 600;
        }

        .score7-team.winner .team-flag {
            filter: brightness(1.2);
        }

        .team-flag {
            font-size: 1.5rem;
            width: 32px;
            text-align: center;
        }

        .team-info {
            flex: 1;
            min-width: 0;
        }

        .team-name {
            font-weight: 600;
            font-size: 0.95rem;
            color: inherit;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .team-score {
            font-size: 1.2rem;
            font-weight: 700;
            min-width: 40px;
            text-align: center;
            padding: 4px 8px;
            border-radius: 6px;
            background: #f8fafc;
            color: #1f2937;
        }

        .score7-team.winner .team-score {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .team-score.placeholder {
            color: #9ca3af;
            font-weight: 500;
        }

        .match-status-indicator {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .match-status-indicator.completed {
            background: #10b981;
            box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
        }

        .match-status-indicator.pending {
            background: #f59e0b;
            box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
        }

        /* Score7.io Responsive Design */
        @media (max-width: 1024px) {
            .score7-rounds-container {
                gap: 30px;
            }

            .score7-round {
                min-width: 250px;
            }

            .score7-bracket-container {
                padding: 20px;
            }
        }

        @media (max-width: 768px) {
            .score7-bracket-container {
                padding: 16px;
                margin: 16px 0;
            }

            .score7-rounds-container {
                gap: 20px;
                padding: 16px 0;
            }

            .score7-round {
                min-width: 220px;
            }

            .score7-round-header h4 {
                font-size: 1rem;
            }

            .score7-team {
                padding: 12px 16px;
                gap: 10px;
            }

            .team-flag {
                font-size: 1.3rem;
                width: 28px;
            }

            .team-name {
                font-size: 0.9rem;
            }

            .team-score {
                font-size: 1.1rem;
                min-width: 36px;
            }

            .bracket-title {
                font-size: 1.5rem;
            }

            .bracket-format-info {
                font-size: 0.9rem;
            }
        }

        /* Enhanced Responsive Design */
        @media (max-width: 1200px) {
            .tournament-container {
                padding: 16px;
            }

            .tournament-header-section {
                padding: 20px 0;
                margin-bottom: 20px;
            }

            .tournament-title {
                font-size: 2rem;
            }

            .tournament-subtitle {
                font-size: 1rem;
            }

            .tournament-meta-info {
                gap: 20px;
            }

            .tournament-meta {
                grid-template-columns: repeat(2, 1fr);
                gap: 16px;
            }

            .format-grid {
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
            }
        }

        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 24px;
            }

            .left-panel {
                order: 1;
            }

            .right-panel {
                order: 2;
            }

            .tournament-header-section {
                padding: 18px 0;
                margin-bottom: 18px;
            }

            .tournament-header h1 {
                font-size: 2.2rem;
            }

            .format-grid {
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 18px;
            }

            .participant-item {
                padding: 16px;
            }

            .bracket-placeholder {
                height: 350px;
                margin: 16px;
            }
        }

        @media (max-width: 768px) {
            .tournament-container {
                padding: 12px;
            }

            .tournament-header-section {
                padding: 16px 0;
                margin-bottom: 16px;
            }

            .tournament-title {
                font-size: 1.8rem;
            }

            .tournament-subtitle {
                font-size: 0.95rem;
            }

            .tournament-meta-info {
                flex-direction: column;
                gap: 12px;
            }

            .meta-item {
                justify-content: center;
            }

            .tournament-header {
                padding: 24px 20px;
                text-align: center;
            }

            .tournament-header h1 {
                font-size: 1.8rem;
                flex-direction: column;
                gap: 12px;
            }

            .tournament-meta {
                grid-template-columns: 1fr;
                gap: 12px;
                text-align: center;
            }

            .format-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .format-card {
                padding: 24px;
                min-height: 140px;
                text-align: center;
            }

            .participant-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
                padding: 20px;
            }

            .participant-info {
                width: 100%;
                justify-content: space-between;
                flex-wrap: wrap;
                gap: 12px;
            }

            .bracket-placeholder {
                height: 300px;
                margin: 12px;
            }

            .bracket-actions {
                flex-direction: column;
                gap: 12px;
                padding: 20px;
            }

            .btn {
                width: 100%;
                justify-content: center;
                min-height: 48px;
                font-size: 1rem;
            }

            .panel-content {
                padding: 20px;
            }

            .config-section {
                margin-bottom: 32px;
            }

            .form-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1.5fr;
            gap: 20px;
            min-height: 600px;
            align-items: start;
        }

        .left-panel {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow);
            overflow: hidden;
            position: sticky;
            top: 15px;
            max-height: calc(100vh - 30px);
            overflow-y: auto;
        }

        .right-panel {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow);
            overflow: hidden;
            min-height: 500px;
        }

        .panel-header {
            background: var(--gray-50);
            padding: 15px 20px;
            border-bottom: 1px solid var(--gray-200);
        }

        .panel-header h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 3px;
        }

        .panel-header p {
            color: var(--gray-600);
            font-size: 0.85rem;
            margin: 0;
        }

        .panel-content {
            padding: 20px;
        }

        /* Format Selection Styles */
        .format-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .format-card {
            border: 2px solid var(--gray-200);
            border-radius: var(--border-radius-lg);
            padding: 18px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: white;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            gap: 10px;
            min-height: 120px;
        }

        .format-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .format-card:hover,
        .format-card:focus {
            border-color: var(--primary-color);
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.15);
            outline: none;
        }

        .format-card:hover::after,
        .format-card:focus::after {
            transform: scaleX(1);
        }

        .format-card:focus {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        .format-card.selected {
            border-color: var(--primary-color);
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 12px 30px rgba(37, 99, 235, 0.25);
        }

        .format-card.selected::after {
            transform: scaleX(1);
            background: rgba(255, 255, 255, 0.3);
        }

        .format-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 10px;
        }

        .format-icon {
            font-size: 2rem;
        }

        .format-name {
            font-size: 1.1rem;
            font-weight: 600;
        }

        .format-description {
            margin-bottom: 10px;
            line-height: 1.4;
            font-size: 0.9rem;
        }

        .format-details {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
        }

        .format-tag {
            background: var(--gray-100);
            color: var(--gray-800);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .format-card.selected .format-tag {
            background: rgba(255, 255, 255, 0.25);
            color: white;
            font-weight: 600;
        }

        .format-selected-indicator {
            position: absolute;
            top: 12px;
            right: 12px;
            background: var(--success-color);
            color: white;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
            z-index: 2;
        }

        .format-action-hint {
            margin-top: auto;
            padding: 10px 12px;
            background: var(--gray-50);
            border-radius: var(--border-radius);
            text-align: center;
            font-size: 0.85rem;
            color: var(--gray-600);
            border-top: 1px solid var(--gray-200);
        }

        .format-card.selected .format-action-hint {
            background: rgba(255, 255, 255, 0.15);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.9);
        }

        .hint-text {
            font-weight: 500;
        }

        /* Focus styles for accessibility */
        .format-card:focus {
            outline: 3px solid var(--primary-color);
            outline-offset: 2px;
        }

        .format-card:focus-visible {
            outline: 3px solid var(--primary-color);
            outline-offset: 2px;
        }

        /* Animation for selection feedback */
        .format-card.selecting {
            animation: selectPulse 0.6s ease-out;
        }

        @keyframes selectPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* Participant Management Styles */
        .participant-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 12px;
            background: var(--gray-50);
            border-radius: var(--border-radius);
        }

        .participant-count {
            font-weight: 600;
            color: var(--gray-700);
            font-size: 0.9rem;
        }

        .participant-actions {
            display: flex;
            gap: 8px;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.8rem;
        }

        .participants-list {
            max-height: 350px;
            overflow-y: auto;
        }

        .participant-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            border: 2px solid var(--gray-200);
            border-radius: var(--border-radius-lg);
            margin-bottom: 8px;
            background: white;
            cursor: grab;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .participant-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--primary-color);
            transform: scaleY(0);
            transition: transform 0.3s ease;
        }

        .participant-item:hover,
        .participant-item:focus {
            border-color: var(--primary-color);
            box-shadow: 0 4px 15px rgba(37, 99, 235, 0.1);
            transform: translateY(-2px);
            outline: none;
        }

        .participant-item:hover::before,
        .participant-item:focus::before {
            transform: scaleY(1);
        }

        .participant-item:focus {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        .participant-item.dragging {
            opacity: 0.8;
            transform: rotate(3deg) scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            cursor: grabbing;
        }

        .participant-item.drag-over {
            border-color: var(--success-color);
            background: rgba(16, 185, 129, 0.1);
            transform: scale(1.02);
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.2);
        }

        .participant-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .seed-number {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .dept-badge {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            color: white;
        }

        .participant-name {
            font-weight: 600;
            color: var(--gray-900);
        }

        .participant-details {
            font-size: 0.85rem;
            color: var(--gray-600);
        }

        /* Bracket Visualization Styles */
        .bracket-container {
            min-height: 400px;
            position: relative;
            overflow: auto;
        }

        .bracket-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 350px;
            color: var(--gray-600);
            text-align: center;
            background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
            border: 2px dashed var(--gray-400);
            border-radius: var(--border-radius-lg);
            margin: 15px;
            position: relative;
            overflow: hidden;
        }

        .bracket-placeholder::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(37, 99, 235, 0.03) 10px,
                rgba(37, 99, 235, 0.03) 20px
            );
            animation: shimmer 3s linear infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%); }
            100% { transform: translateX(100%) translateY(100%); }
        }

        .bracket-placeholder-icon {
            font-size: 5rem;
            margin-bottom: 24px;
            opacity: 0.6;
            position: relative;
            z-index: 1;
        }

        .bracket-placeholder h3 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 12px;
            color: var(--gray-800);
            position: relative;
            z-index: 1;
        }

        .bracket-placeholder p {
            font-size: 1rem;
            line-height: 1.6;
            max-width: 400px;
            color: var(--gray-700);
            position: relative;
            z-index: 1;
        }

        .bracket-actions {
            display: flex;
            gap: 15px;
            padding: 20px;
            border-top: 1px solid var(--gray-200);
            background: var(--gray-50);
        }

        /* Configuration Panel Styles */
        .config-section {
            margin-bottom: 30px;
        }

        .config-section h4 {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 15px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-group label {
            font-weight: 600;
            color: var(--gray-700);
            font-size: var(--font-size-sm);
        }

        .form-control {
            padding: 0.75rem;
            border: 2px solid var(--gray-200);
            border-radius: var(--border-radius);
            font-size: var(--font-size-base);
            transition: var(--transition);
            background: var(--white);
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
        }

        /* Enhanced Bracket Visualization Styles */
        .bracket-visualization {
            padding: 24px;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--gray-200);
            position: relative;
            overflow: hidden;
        }

        .bracket-visualization::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--success-color), var(--warning-color));
        }

        .single-elimination-bracket,
        .double-elimination-bracket,
        .round-robin-bracket {
            width: 100%;
            position: relative;
        }

        .bracket-format-header {
            text-align: center;
            margin-bottom: 32px;
            padding: 20px;
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .bracket-format-header h4 {
            margin: 0 0 8px 0;
            color: var(--gray-900);
            font-size: 1.5rem;
            font-weight: 700;
        }

        .bracket-rounds-container {
            display: flex;
            gap: 40px;
            overflow-x: auto;
            padding: 20px 0;
            min-height: 400px;
        }

        .bracket-round {
            min-width: 280px;
            display: flex;
            flex-direction: column;
            position: relative;
            margin-bottom: 0;
            padding: 0;
            background: transparent;
        }

        .bracket-round-header {
            text-align: center;
            margin-bottom: 24px;
        }

        .bracket-round h5 {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-600) 100%);
            color: white;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
            margin: 0;
        }

        .round-matches {
            display: flex;
            flex-direction: column;
            gap: 24px;
            flex: 1;
            justify-content: center;
        }

        .round-robin-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 20px;
            padding: 20px 0;
        }

        .match-card {
            background: white;
            border: 2px solid var(--gray-200);
            border-radius: var(--border-radius-lg);
            padding: 0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            position: relative;
            text-align: left;
        }

        .match-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-400));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .match-card:hover {
            border-color: var(--primary-color);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.15);
            transform: translateY(-2px);
        }

        .match-card:hover::before {
            transform: scaleX(1);
        }

        .match-participants {
            padding: 20px;
        }

        .match-participant {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            margin: 8px 0;
            background: var(--gray-50);
            border-radius: var(--border-radius);
            border: 2px solid transparent;
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .match-participant:hover {
            background: var(--gray-100);
            border-color: var(--gray-300);
        }

        .match-participant.winner {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(16, 185, 129, 0.05) 100%);
            border-color: var(--success-color);
            color: var(--success-700);
            font-weight: 600;
        }

        .match-vs {
            text-align: center;
            font-size: 0.875rem;
            color: var(--gray-500);
            font-weight: 600;
            margin: 12px 0;
            position: relative;
        }

        .match-header {
            padding: 12px 20px;
            background: var(--gray-50);
            border-bottom: 1px solid var(--gray-200);
            text-align: center;
        }

        .match-id {
            font-size: 0.875rem;
            color: var(--gray-600);
            font-weight: 600;
            margin: 0;
        }

        .match-footer {
            padding: 12px 20px;
            background: var(--gray-50);
            border-top: 1px solid var(--gray-200);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .match-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .match-status[data-status="pending"],
        .match-status.pending {
            background: var(--warning-100);
            color: var(--warning-700);
        }

        .match-status[data-status="completed"],
        .match-status.completed {
            background: var(--success-100);
            color: var(--success-700);
        }

        .match-status[data-status="bye"],
        .match-status.bye {
            background: var(--gray-100);
            color: var(--gray-700);
        }

        .match-status.in-progress {
            background: var(--primary-100);
            color: var(--primary-700);
        }

        .match-time {
            font-size: 0.8rem;
            color: var(--gray-600);
        }

        /* Bracket Connectors */
        .bracket-connector {
            position: absolute;
            right: -20px;
            top: 50%;
            width: 40px;
            height: 2px;
            background: var(--gray-300);
            transform: translateY(-50%);
        }

        .bracket-connector::after {
            content: '';
            position: absolute;
            right: -8px;
            top: -3px;
            width: 0;
            height: 0;
            border-left: 8px solid var(--gray-300);
            border-top: 4px solid transparent;
            border-bottom: 4px solid transparent;
        }

        .match-card.winner .bracket-connector {
            background: var(--success-color);
        }

        .match-card.winner .bracket-connector::after {
            border-left-color: var(--success-color);
        }

        /* Participant Score Display */
        .participant-name {
            flex: 1;
        }

        .participant-score {
            font-weight: 700;
            font-size: 1.1rem;
            color: var(--gray-700);
            min-width: 40px;
            text-align: right;
        }

        .match-participant.winner .participant-score {
            color: var(--success-700);
        }

        .match-participant.winner::after {
            content: '👑';
            margin-left: 8px;
        }

        .bracket-container.fullscreen {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            z-index: 9999 !important;
            background: white !important;
            overflow: auto !important;
        }

        /* Drag and Drop Styles */
        .participant-item.drag-over {
            border-color: var(--primary-color);
            background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(37, 99, 235, 0.05) 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
        }

        .participant-item.drag-over::before {
            content: '';
            position: absolute;
            top: -2px;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-color);
            border-radius: 2px;
            animation: dropIndicator 1s ease-in-out infinite alternate;
        }

        @keyframes dropIndicator {
            0% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .participant-item.touch-dragging {
            background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(37, 99, 235, 0.05) 100%);
            border-color: var(--primary-color);
            transform: scale(1.02);
        }

        .touch-clone {
            background: var(--white);
            border: 2px solid var(--primary-color);
            border-radius: var(--border-radius);
        }

        /* High Contrast Mode Support */
        @media (prefers-contrast: high) {
            .format-card {
                border-width: 3px;
            }

            .format-card.selected {
                border-width: 4px;
            }

            .participant-item {
                border-width: 3px;
            }

            .match-card {
                border-width: 3px;
            }

            .btn {
                border: 2px solid;
            }

            .bracket-placeholder {
                border-width: 3px;
            }
        }

        /* Reduced Motion Support */
        @media (prefers-reduced-motion: reduce) {
            .format-card,
            .participant-item,
            .match-card,
            .wizard-step {
                transition: none;
            }

            .bracket-placeholder::before {
                animation: none;
            }

            @keyframes shimmer {
                0%, 100% { transform: none; }
            }

            @keyframes dropIndicator {
                0%, 100% { opacity: 1; }
            }
        }

        /* Button Styles - Consistent with Admin.css */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            font-size: var(--font-size-base);
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: var(--transition);
            white-space: nowrap;
        }

        .btn-primary {
            background: var(--primary-color);
            color: var(--white);
        }

        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: var(--shadow);
        }

        .btn-secondary {
            background: var(--gray-100);
            color: var(--gray-700);
            border: 1px solid var(--gray-300);
        }

        .btn-secondary:hover {
            background: var(--gray-200);
            color: var(--gray-800);
            border-color: var(--gray-400);
            transform: translateY(-1px);
            box-shadow: var(--shadow-sm);
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: var(--font-size-sm);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .btn:disabled:hover {
            transform: none;
            box-shadow: none;
        }

        /* Improved visual feedback for draggable items */
        .participant-item[draggable="true"] {
            cursor: grab;
        }

        .participant-item[draggable="true"]:active {
            cursor: grabbing;
        }

        .participants-list .participant-item::before {
            content: '⋮⋮';
            position: absolute;
            left: 8px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
            font-size: 1.2rem;
            line-height: 0.8;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .participants-list .participant-item:hover::before {
            opacity: 1;
        }

        .participants-list .participant-item .participant-info {
            margin-left: 24px;
        }

        /* Touch device specific styles */
        .touch-device .participant-item {
            min-height: 60px;
            padding: 16px;
        }

        .touch-device .participant-item::before {
            font-size: 1.4rem;
            left: 12px;
        }

        .touch-device .participants-list .participant-item .participant-info {
            margin-left: 32px;
        }

        .touch-device .seed-number {
            width: 36px;
            height: 36px;
            font-size: 1rem;
        }

        /* Notification Styles */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            min-width: 300px;
            padding: 15px 20px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Icon styles */
        .icon-trophy::before { content: "🏆"; }
        .icon-users::before { content: "👥"; }
        .icon-shuffle::before { content: "🔀"; }
        .icon-reset::before { content: "🔄"; }
        .icon-edit::before { content: "✏️"; }
        .icon-expand::before { content: "🔍"; }
        .icon-download::before { content: "💾"; }
        .icon-refresh::before { content: "🔄"; }
        .icon-success::before { content: "✅"; }
        .icon-error::before { content: "❌"; }
        .icon-info::before { content: "ℹ️"; }
        .icon-warning::before { content: "⚠️"; }
        .icon-next::before { content: "➡️"; }
        .icon-generate::before { content: "⚡"; }
        .icon-check::before { content: "✅"; }
        .icon-settings::before { content: "⚙️"; }
        .icon-bracket::before { content: "🏆"; }
        .icon-fullscreen::before { content: "🔍"; }
        .icon-export::before { content: "📊"; }

        /* Format selection emphasis */
        .format-card {
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
        }

        .format-card:not(.selected) {
            animation: pulse-hint 4s infinite;
        }

        .format-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: var(--primary-color);
        }

        @keyframes pulse-hint {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }
            50% {
                transform: scale(1.02);
                box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
            }
        }

        .format-card.selected {
            animation: none;
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
            border-color: var(--primary-color);
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        }

        .format-card::before {
            content: "👆 Click to select";
            position: absolute;
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.8rem;
            color: var(--gray-500);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .format-card:hover::before {
            opacity: 1;
        }

        .format-card.selected::before {
            content: "✅ Selected";
            color: var(--success-color);
            opacity: 1;
        }

        /* Next steps highlighting */
        .next-steps-container {
            animation: fadeInUp 0.5s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr 1.2fr;
                gap: 20px;
            }

            .left-panel {
                position: static;
                max-height: none;
            }
        }

        /* Duplicate removed - consolidated above */

        @media (max-width: 768px) {
            .tournament-container {
                padding: 12px;
            }

            .score7-bracket-container {
                padding: 12px;
                margin: 12px 0;
            }

            .wizard-step {
                padding: 12px 16px;
                font-size: 0.9rem;
            }

            .tournament-meta {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .format-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .format-card {
                padding: 20px;
                min-height: 120px;
            }

            .participant-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
                padding: 16px;
            }

            .participant-info {
                width: 100%;
                justify-content: space-between;
            }

            .bracket-placeholder {
                height: 300px;
                margin: 12px;
            }

            .bracket-actions {
                flex-direction: column;
                gap: 12px;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }

        @media (max-width: 480px) {
            .tournament-container {
                padding: 8px;
            }

            .tournament-header {
                padding: 20px 16px;
            }

            .tournament-header h1 {
                font-size: 1.5rem;
            }

            .score7-rounds-container {
                gap: 16px;
                padding: 12px 0;
            }

            .score7-round {
                min-width: 200px;
            }

            .step-number {
                width: 24px;
                height: 24px;
                font-size: 0.7rem;
            }

            .step-title {
                font-size: 0.85rem;
            }

            .step-description {
                font-size: 0.7rem;
            }

            .format-card {
                padding: 20px 16px;
                min-height: 120px;
            }

            .format-icon {
                font-size: 2.5rem;
            }

            .format-name {
                font-size: 1rem;
            }

            .format-description {
                font-size: 0.85rem;
                line-height: 1.4;
            }

            .participant-item {
                padding: 16px;
            }

            .seed-number {
                width: 32px;
                height: 32px;
                font-size: 0.8rem;
            }

            .participant-name {
                font-size: 0.9rem;
            }

            .participant-details {
                font-size: 0.8rem;
            }

            .bracket-placeholder {
                height: 280px;
                margin: 8px;
                padding: 20px;
            }

            .bracket-placeholder-icon {
                font-size: 3.5rem;
            }

            .panel-header h3 {
                font-size: 1.2rem;
            }

            .panel-header p {
                font-size: 0.85rem;
            }

            .config-section h4 {
                font-size: 1.1rem;
            }

            .form-group label {
                font-size: 0.9rem;
            }

            .form-control {
                font-size: 0.9rem;
                padding: 12px;
            }

            .btn {
                padding: 12px 20px;
                font-size: 0.9rem;
            }

            .meta-item {
                padding: 12px;
            }

            .meta-label {
                font-size: 0.8rem;
            }

            .meta-value {
                font-size: 0.9rem;
            }
        }

        /* Enhanced Touch Device Support */
        @media (hover: none) and (pointer: coarse) {
            /* Disable hover effects on touch devices */
            .format-card:hover,
            .participant-item:hover,
            .wizard-step:hover,
            .btn:hover {
                transform: none;
                box-shadow: var(--shadow);
            }

            /* Enhanced touch targets */
            .format-card {
                min-height: 160px;
                padding: 24px;
                cursor: pointer;
                -webkit-tap-highlight-color: rgba(37, 99, 235, 0.1);
            }

            .participant-item {
                cursor: pointer;
                min-height: 80px;
                padding: 20px;
                -webkit-tap-highlight-color: rgba(37, 99, 235, 0.1);
            }

            .btn {
                min-height: 52px;
                font-size: 1.1rem;
                padding: 16px 24px;
                -webkit-tap-highlight-color: transparent;
            }

            .wizard-step {
                min-height: 60px;
                padding: 16px 24px;
                -webkit-tap-highlight-color: rgba(37, 99, 235, 0.1);
            }

            /* Enhanced drag and drop for touch */
            .participant-item[draggable="true"] {
                touch-action: manipulation;
                user-select: none;
                -webkit-user-select: none;
                -moz-user-select: none;
                -ms-user-select: none;
            }

            /* Better form controls for touch */
            .form-control,
            select,
            input[type="text"],
            input[type="number"],
            textarea {
                min-height: 48px;
                font-size: 16px; /* Prevents zoom on iOS */
                padding: 12px 16px;
                -webkit-appearance: none;
                appearance: none;
                border-radius: 8px;
            }

            /* Enhanced focus states for touch navigation */
            .format-card:focus,
            .participant-item:focus,
            .wizard-step:focus {
                outline: 3px solid var(--primary-color);
                outline-offset: 2px;
            }

            /* Improved spacing for touch interactions */
            .format-grid {
                gap: 20px;
            }

            .participants-list .participant-item {
                margin-bottom: 16px;
            }

            .bracket-actions .btn {
                margin: 8px 0;
            }

            /* Touch-friendly scrolling */
            .participants-list,
            .bracket-container {
                -webkit-overflow-scrolling: touch;
                scroll-behavior: smooth;
            }
        }

        /* Single Panel Layout Styles */
        .main-content.single-panel {
            display: block;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .bracket-control-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 24px;
            background: linear-gradient(135deg, var(--white) 0%, var(--gray-50) 100%);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--gray-200);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .bracket-title h2 {
            margin: 0 0 8px 0;
            color: var(--gray-900);
            font-size: 1.8rem;
            font-weight: 700;
        }

        .bracket-title p {
            margin: 0;
            color: var(--gray-600);
            font-size: 1rem;
        }

        .bracket-controls {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .tournament-info-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
            padding: 20px;
            background: var(--gray-50);
            border-radius: var(--border-radius);
            border: 1px solid var(--gray-200);
        }

        .info-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .info-label {
            font-size: 0.875rem;
            color: var(--gray-600);
            font-weight: 500;
        }

        .info-value {
            font-size: 1rem;
            color: var(--gray-900);
            font-weight: 600;
        }

        .bracket-container {
            background: var(--white);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--gray-200);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            min-height: 600px;
            position: relative;
            overflow: hidden;
        }

        .bracket-success-message {
            margin: 30px;
            padding: 24px;
            background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
            border-radius: var(--border-radius-lg);
            border-left: 4px solid var(--success-color);
        }

        .success-content h4 {
            color: var(--success-700);
            margin-bottom: 15px;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .success-content p {
            color: var(--success-700);
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .next-steps-info {
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(22, 101, 52, 0.1);
            border-radius: var(--border-radius);
        }

        .next-steps-info h5 {
            color: var(--success-700);
            margin-bottom: 10px;
            font-size: 1rem;
            font-weight: 600;
        }

        .next-steps-info ol {
            color: var(--success-700);
            margin: 0 0 0 20px;
            line-height: 1.6;
            font-size: 0.9rem;
        }

        .success-actions {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .setup-guide {
            margin: 30px 0;
            padding: 20px;
            background: #fef3c7;
            border-radius: var(--border-radius);
            border-left: 4px solid var(--warning-color);
        }

        .setup-guide h5 {
            color: #92400e;
            margin-bottom: 15px;
            font-size: 1rem;
            font-weight: 600;
        }

        .setup-guide ol {
            color: #92400e;
            margin: 0 0 0 20px;
            line-height: 1.8;
        }

        .participants-info {
            margin-top: 20px;
            padding: 15px;
            background: #f0f9ff;
            border-radius: var(--border-radius);
            border-left: 4px solid var(--primary-color);
        }

        .participants-info p {
            color: #0c4a6e;
            margin: 0;
        }

        .btn-lg {
            padding: 15px 30px;
            font-size: 1.1rem;
        }

        /* Modal Styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 99999;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: modalFadeIn 0.3s ease-out;
            visibility: visible;
            opacity: 1;
        }

        .modal[style*="display: none"] {
            display: none !important;
            visibility: hidden;
            opacity: 0;
        }

        .modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
        }

        .modal-content {
            position: relative;
            background: var(--white);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            max-width: 90vw;
            max-height: 90vh;
            width: 1000px;
            overflow: hidden;
            animation: modalSlideIn 0.3s ease-out;
        }

        .modal-header {
            padding: 24px 30px;
            border-bottom: 1px solid var(--gray-200);
            background: linear-gradient(135deg, var(--white) 0%, var(--gray-50) 100%);
            position: relative;
        }

        .modal-header h3 {
            margin: 0 0 8px 0;
            color: var(--gray-900);
            font-size: 1.5rem;
            font-weight: 700;
        }

        .modal-header p {
            margin: 0;
            color: var(--gray-600);
            font-size: 1rem;
        }

        .modal-close {
            position: absolute;
            top: 20px;
            right: 20px;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--gray-500);
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.2s ease;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-close:hover {
            background: var(--gray-100);
            color: var(--gray-700);
        }

        .modal-body {
            padding: 30px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .format-help {
            margin-bottom: 25px;
            padding: 15px;
            background: #f0f9ff;
            border-radius: var(--border-radius);
            border-left: 3px solid var(--primary-color);
        }

        .format-help h5 {
            color: #0c4a6e;
            margin-bottom: 10px;
            font-size: 0.95rem;
            font-weight: 600;
        }

        .format-help ul {
            color: #0c4a6e;
            margin: 0;
            padding-left: 18px;
            line-height: 1.5;
            font-size: 0.9rem;
        }

        .modal-format-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .modal-format-card {
            background: var(--white);
            border: 2px solid var(--gray-200);
            border-radius: var(--border-radius-lg);
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            text-align: center;
        }

        .modal-format-card:hover {
            border-color: var(--primary-color);
            box-shadow: 0 4px 15px rgba(37, 99, 235, 0.15);
            transform: translateY(-2px);
        }

        .modal-format-card.selected {
            border-color: var(--primary-color);
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.2);
        }

        .modal-footer {
            padding: 20px 30px;
            border-top: 1px solid var(--gray-200);
            background: var(--gray-50);
            display: flex;
            justify-content: flex-end;
            gap: 15px;
        }

        @keyframes modalFadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Responsive Styles for Single Panel Layout */
        @media (max-width: 1200px) {
            .bracket-control-header {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .bracket-controls {
                justify-content: center;
                flex-wrap: wrap;
            }

            .tournament-info-summary {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 15px;
            }
        }

        @media (max-width: 768px) {
            .main-content.single-panel {
                padding: 0 15px;
            }

            .bracket-control-header {
                padding: 20px;
                margin-bottom: 20px;
            }

            .bracket-title h2 {
                font-size: 1.5rem;
            }

            .bracket-controls {
                flex-direction: column;
                width: 100%;
            }

            .bracket-controls .btn {
                width: 100%;
                justify-content: center;
            }

            .tournament-info-summary {
                grid-template-columns: 1fr 1fr;
                gap: 12px;
                padding: 15px;
            }

            .modal-content {
                width: 95vw;
                margin: 20px;
            }

            .modal-header {
                padding: 20px;
            }

            .modal-body {
                padding: 20px;
            }

            .modal-format-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .modal-footer {
                padding: 15px 20px;
                flex-direction: column;
            }

            .modal-footer .btn {
                width: 100%;
            }
        }

        @media (max-width: 480px) {
            .bracket-title h2 {
                font-size: 1.3rem;
            }

            .bracket-title p {
                font-size: 0.9rem;
            }

            .tournament-info-summary {
                grid-template-columns: 1fr;
                gap: 10px;
                padding: 12px;
            }

            .info-item {
                text-align: center;
                padding: 10px;
                background: var(--white);
                border-radius: var(--border-radius);
            }

            .bracket-container {
                min-height: 400px;
            }

            .bracket-placeholder {
                padding: 20px;
                margin: 15px;
            }

            .bracket-placeholder-icon {
                font-size: 3rem;
            }

            .setup-guide,
            .participants-info,
            .bracket-success-message {
                margin: 15px;
                padding: 15px;
            }
        }

        /* Professional Bracket Visualization Styles */
        .professional-bracket-visualization {
            padding: 30px;
            background: var(--white);
            border-radius: var(--border-radius-lg);
            min-height: 500px;
        }

        .bracket-format-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid var(--gray-200);
        }

        .bracket-format-header h4 {
            margin: 0 0 8px 0;
            color: var(--gray-900);
            font-size: 1.5rem;
            font-weight: 700;
        }

        .bracket-format-header p {
            margin: 0;
            color: var(--gray-600);
            font-size: 1rem;
        }

        .professional-bracket-container {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .professional-bracket-container.double-elimination {
            gap: 40px;
        }

        .bracket-section-title {
            margin: 0 0 20px 0;
            color: var(--gray-900);
            font-size: 1.3rem;
            font-weight: 600;
            padding: 15px 20px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: var(--white);
            border-radius: var(--border-radius);
            text-align: center;
        }

        .bracket-rounds-container {
            display: flex;
            gap: 30px;
            overflow-x: auto;
            padding: 20px 0;
            min-height: 400px;
        }

        .bracket-round {
            flex: 0 0 auto;
            min-width: 280px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .bracket-round-header {
            text-align: center;
            padding: 12px 20px;
            background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
            border-radius: var(--border-radius);
            border: 1px solid var(--gray-300);
        }

        .bracket-round-header h5 {
            margin: 0;
            color: var(--gray-800);
            font-size: 1.1rem;
            font-weight: 600;
        }

        .round-matches {
            display: flex;
            flex-direction: column;
            gap: 20px;
            flex: 1;
            justify-content: center;
        }

        .professional-match-card {
            background: var(--white);
            border: 2px solid var(--gray-200);
            border-radius: var(--border-radius-lg);
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
        }

        .professional-match-card:hover {
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .professional-match-card.completed {
            border-color: var(--success-color);
            background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
        }

        .match-team {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            margin: 5px 0;
            background: var(--gray-50);
            border-radius: var(--border-radius);
            border: 1px solid var(--gray-200);
            transition: all 0.2s ease;
        }

        .match-team.winner {
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            border-color: var(--success-color);
            font-weight: 600;
            color: var(--success-700);
        }

        .team-name {
            font-size: 1rem;
            font-weight: 500;
            color: var(--gray-800);
        }

        .match-team.winner .team-name {
            color: var(--success-700);
            font-weight: 600;
        }

        .team-score {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--gray-900);
            background: var(--white);
            padding: 6px 12px;
            border-radius: var(--border-radius);
            border: 1px solid var(--gray-300);
            min-width: 40px;
            text-align: center;
        }

        .match-team.winner .team-score {
            background: var(--success-color);
            color: var(--white);
            border-color: var(--success-color);
        }

        .match-vs {
            text-align: center;
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--gray-500);
            margin: 8px 0;
            padding: 5px;
        }

        .match-status {
            text-align: center;
            margin-top: 15px;
            padding: 8px 12px;
            border-radius: var(--border-radius);
            font-size: 0.9rem;
            font-weight: 500;
        }

        .match-status.completed {
            background: var(--success-color);
            color: var(--white);
        }

        .match-status.pending {
            background: var(--warning-color);
            color: var(--white);
        }

        /* Round Robin Specific Styles */
        .round-robin-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .round-robin-round {
            background: var(--gray-50);
            border-radius: var(--border-radius-lg);
            padding: 20px;
            border: 1px solid var(--gray-200);
        }

        .round-robin-round h5 {
            margin: 0 0 20px 0;
            color: var(--gray-800);
            font-size: 1.2rem;
            font-weight: 600;
            text-align: center;
            padding: 10px;
            background: var(--white);
            border-radius: var(--border-radius);
            border: 1px solid var(--gray-300);
        }

        /* Responsive Styles for Professional Bracket */
        @media (max-width: 1200px) {
            .bracket-rounds-container {
                gap: 20px;
                padding: 15px 0;
            }

            .bracket-round {
                min-width: 250px;
            }

            .professional-match-card {
                padding: 15px;
            }
        }

        @media (max-width: 768px) {
            .professional-bracket-visualization {
                padding: 20px;
            }

            .bracket-rounds-container {
                flex-direction: column;
                gap: 25px;
                padding: 10px 0;
                min-height: auto;
            }

            .bracket-round {
                min-width: 100%;
                width: 100%;
            }

            .round-matches {
                gap: 15px;
            }

            .professional-match-card {
                padding: 15px;
            }

            .match-team {
                padding: 10px 12px;
                flex-direction: column;
                gap: 8px;
                text-align: center;
            }

            .team-score {
                font-size: 1.1rem;
                padding: 5px 10px;
                min-width: 35px;
            }

            .round-robin-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .round-robin-round {
                padding: 15px;
            }
        }

        @media (max-width: 480px) {
            .professional-bracket-visualization {
                padding: 15px;
            }

            .bracket-format-header h4 {
                font-size: 1.3rem;
            }

            .bracket-section-title {
                font-size: 1.1rem;
                padding: 12px 15px;
            }

            .bracket-round-header h5 {
                font-size: 1rem;
            }

            .professional-match-card {
                padding: 12px;
            }

            .match-team {
                padding: 8px 10px;
                gap: 6px;
            }

            .team-name {
                font-size: 0.9rem;
            }

            .team-score {
                font-size: 1rem;
                padding: 4px 8px;
                min-width: 30px;
            }

            .match-status {
                padding: 6px 10px;
                font-size: 0.8rem;
            }
        }

        /* Enhanced Form Validation Styles */
        .field-error {
            border-color: #ef4444 !important;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
            animation: shake 0.3s ease-in-out;
        }

        .field-success {
            border-color: #10b981 !important;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
        }

        .field-error-message {
            animation: fadeInUp 0.3s ease-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideOutRight {
            from {
                opacity: 1;
                transform: translateX(0);
            }
            to {
                opacity: 0;
                transform: translateX(100%);
            }
        }

        /* Enhanced form states */
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
            outline: none;
        }

        .form-control.field-error:focus {
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .form-control.field-success:focus {
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }

        /* Loading states */
        .btn.loading {
            position: relative;
            color: transparent !important;
            pointer-events: none;
        }

        .btn.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Progress indicators */
        .validation-progress {
            height: 4px;
            background: #e5e7eb;
            border-radius: 2px;
            overflow: hidden;
            margin-top: 8px;
        }

        .validation-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #10b981, #059669);
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        /* High DPI displays */
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
            .format-icon,
            .bracket-placeholder-icon {
                image-rendering: -webkit-optimize-contrast;
                image-rendering: crisp-edges;
            }
        }
    </style>
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>

    <main class="admin-main">
        <div class="tournament-container">
            <!-- Score7.io Style Tournament Header -->
            <div class="tournament-header-section">
                <div class="tournament-header-content">
                    <h1 class="tournament-title"><?php echo htmlspecialchars($eventSport['sport_name'] ?? 'Basketball'); ?> Tournament</h1>
                    <p class="tournament-subtitle"><?php echo htmlspecialchars($eventSport['event_name'] ?? 'Intramural Championship 2024'); ?></p>
                    <div class="tournament-meta-info">
                        <div class="meta-item">
                            <span class="meta-icon">🏆</span>
                            <span><?php echo ucfirst(str_replace('_', ' ', $tournamentConfig['tournament_format'] ?? 'Not Set')); ?></span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-icon">👥</span>
                            <span><?php echo count($participants); ?> <?php echo ($eventSport['sport_category'] === 'individual') ? 'Participants' : 'Teams'; ?></span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-icon">📅</span>
                            <span><?php
                                $startDate = $eventSport['start_date'] ?? '2024-01-15';
                                echo date('M d, Y', strtotime($startDate));
                            ?></span>
                        </div>
                        <div class="meta-item">
                            <span class="meta-icon">📍</span>
                            <span><?php echo htmlspecialchars($eventSport['venue'] ?? 'Main Gymnasium'); ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Alert Messages -->
            <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?>">
                    <i class="icon-<?php echo $messageType; ?>"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <!-- Main Content Area - Single Panel Layout -->
            <div class="main-content single-panel">
                <!-- Bracket Control Header -->
                <div class="bracket-control-header">
                    <div class="bracket-title">
                        <h2>🏆 Tournament Bracket Setup</h2>
                        <p>Configure your tournament format and visualize the bracket structure</p>
                    </div>
                    <div class="bracket-controls">
                        <button type="button" class="btn btn-primary" onclick="openFormatModal()" id="formatSelectBtn">
                            <i class="icon-settings"></i>
                            <?php if ($tournamentConfig && $tournamentConfig['tournament_format']): ?>
                                Change Format (<?php echo ucfirst(str_replace('_', ' ', $tournamentConfig['tournament_format'])); ?>)
                            <?php else: ?>
                                Select Tournament Format
                            <?php endif; ?>
                        </button>
                        <?php if ($tournamentConfig && $tournamentConfig['tournament_format']): ?>
                            <button type="button" class="btn btn-secondary" onclick="regenerateBracket()">
                                <i class="icon-refresh"></i>
                                Regenerate Bracket
                            </button>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Tournament Info Summary -->
                <?php if ($tournamentConfig): ?>
                    <div class="tournament-info-summary">
                        <div class="info-item">
                            <span class="info-label">Format:</span>
                            <span class="info-value"><?php echo ucfirst(str_replace('_', ' ', $tournamentConfig['tournament_format'])); ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Teams:</span>
                            <span class="info-value"><?php echo count($participants); ?> participating</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Bracket Size:</span>
                            <span class="info-value">Up to <?php echo $tournamentConfig['bracket_size']; ?> teams</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Seeding:</span>
                            <span class="info-value"><?php echo ucfirst($tournamentConfig['seeding_type']); ?> order</span>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Professional Bracket Visualization -->
                <div class="bracket-container" id="bracketContainer">
                    <?php if ($tournamentConfig && !empty($tournamentConfig['bracket_data'])): ?>
                        <?php
                        $bracketData = json_decode($tournamentConfig['bracket_data'], true);
                        renderProfessionalBracketVisualization($bracketData, $participants);
                        ?>

                        <!-- Success Message and Next Steps -->
                        <div class="bracket-success-message">
                            <div class="success-content">
                                <h4>✅ Bracket Configuration Complete!</h4>
                                <p>Your tournament bracket has been successfully configured. You can now proceed to the next steps in the tournament setup process.</p>

                                <div class="next-steps-info">
                                    <h5>📋 Next Steps:</h5>
                                    <ol>
                                        <li><strong>Step 3:</strong> Schedule your matches and assign venues</li>
                                        <li><strong>Step 4:</strong> Configure scoring rules and finalize tournament settings</li>
                                        <li><strong>Publish:</strong> Make your tournament live for participants</li>
                                    </ol>
                                </div>

                                <div class="success-actions">
                                    <a href="tournament_schedule.php?event_sport_id=<?php echo $eventSportId; ?>&event_id=<?php echo $eventId; ?>"
                                       class="btn btn-primary">
                                        <i class="icon-next"></i>
                                        Next: Schedule Matches
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php elseif ($tournamentConfig && $tournamentConfig['tournament_format']): ?>
                        <div class="bracket-placeholder">
                            <div class="bracket-placeholder-icon">🏆</div>
                            <h4>Ready to Create Your Bracket!</h4>
                            <p>Tournament type: <strong><?php echo ucfirst(str_replace('_', ' ', $tournamentConfig['tournament_format'])); ?></strong></p>
                            <p>Teams: <strong><?php echo count($participants); ?></strong></p>
                            <form method="POST" style="margin-top: 20px;" id="generateBracketForm">
                                <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                <input type="hidden" name="action" value="generate_bracket">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="icon-trophy"></i>
                                    Create Tournament Bracket
                                </button>
                            </form>
                        </div>
                    <?php else: ?>
                        <div class="bracket-placeholder">
                            <div class="bracket-placeholder-icon">🏆</div>
                            <h4>Let's Set Up Your Tournament!</h4>
                            <p>Click "Select Tournament Format" above to choose how your tournament will work.</p>

                            <div class="setup-guide">
                                <h5>📋 Quick Setup Guide:</h5>
                                <ol>
                                    <li><strong>Select Format:</strong> Choose your tournament style (Single Elimination, Round Robin, etc.)</li>
                                    <li><strong>Generate Bracket:</strong> Create the tournament structure</li>
                                    <li><strong>Schedule Matches:</strong> Set dates, times, and venues</li>
                                    <li><strong>Publish Tournament:</strong> Make it live for participants</li>
                                </ol>
                            </div>

                            <div class="participants-info">
                                <p><strong>💡 Good news:</strong> You have <strong><?php echo count($participants); ?> teams</strong> ready to compete!</p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Bracket Actions -->
                <div class="bracket-actions">
                    <button type="button" class="btn btn-secondary" onclick="toggleFullView()">
                        <i class="icon-expand"></i>
                        Full View
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="exportBracket()">
                        <i class="icon-download"></i>
                        Export Bracket
                    </button>
                </div>
            </div>
        </div>

        <!-- Tournament Format Selection Modal -->
        <div id="formatModal" class="modal" style="display: none;">
            <div class="modal-overlay" onclick="closeFormatModal()"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3>🎯 Select Tournament Format</h3>
                    <p>Choose the tournament style that works best for your sport</p>
                    <button type="button" class="modal-close" onclick="closeFormatModal()" aria-label="Close modal">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>

                <div class="modal-body">
                    <!-- Help Section -->
                    <div class="format-help">
                        <h5>💡 Need Help Choosing?</h5>
                        <ul>
                            <li><strong>Single Elimination:</strong> Fastest option - lose once and you're out</li>
                            <li><strong>Double Elimination:</strong> More fair - teams get a second chance</li>
                            <li><strong>Round Robin:</strong> Most fair - everyone plays everyone</li>
                        </ul>
                    </div>

                    <form method="POST" id="modalFormatForm">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                        <input type="hidden" name="action" value="save_format">
                        <input type="hidden" name="tournament_format" id="modal_selected_format" value="">
                        <input type="hidden" name="bracket_size" value="<?php echo $tournamentConfig ? $tournamentConfig['bracket_size'] : '16'; ?>">
                        <input type="hidden" name="seeding_type" value="<?php echo $tournamentConfig ? $tournamentConfig['seeding_type'] : 'manual'; ?>">

                        <div class="modal-format-grid">
                            <?php foreach ($availableFormats as $formatKey => $format): ?>
                                <div class="modal-format-card <?php echo ($tournamentConfig && $tournamentConfig['tournament_format'] === $formatKey) ? 'selected' : ''; ?>"
                                     onclick="selectModalFormat('<?php echo $formatKey; ?>')"
                                     role="button"
                                     tabindex="0"
                                     aria-label="Select <?php echo htmlspecialchars($format['name']); ?> tournament format"
                                     onkeydown="handleModalFormatKeydown(event, '<?php echo $formatKey; ?>')"
                                     data-format="<?php echo $formatKey; ?>">

                                    <?php if ($tournamentConfig && $tournamentConfig['tournament_format'] === $formatKey): ?>
                                        <div class="format-selected-indicator" aria-hidden="true">
                                            <span class="checkmark">✓</span>
                                        </div>
                                    <?php endif; ?>

                                    <div class="format-header">
                                        <div class="format-icon" aria-hidden="true"><?php echo $format['icon']; ?></div>
                                        <div class="format-name"><?php echo htmlspecialchars($format['name']); ?></div>
                                    </div>

                                    <div class="format-description">
                                        <?php echo htmlspecialchars($format['description']); ?>
                                    </div>

                                    <div class="format-details" role="list" aria-label="Format features">
                                        <?php foreach ($format['details'] as $detail): ?>
                                            <span class="format-tag" role="listitem"><?php echo htmlspecialchars($detail); ?></span>
                                        <?php endforeach; ?>
                                    </div>

                                    <div class="format-action-hint">
                                        <span class="hint-text">
                                            <?php if ($tournamentConfig && $tournamentConfig['tournament_format'] === $formatKey): ?>
                                                ✅ Currently Selected
                                            <?php else: ?>
                                                👆 Click to select
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </form>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeFormatModal()">
                        Cancel
                    </button>
                    <button type="button" class="btn btn-primary" onclick="confirmFormatSelection()" id="confirmFormatBtn" disabled>
                        <i class="icon-check"></i>
                        Apply Format
                    </button>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Tournament Configuration JavaScript
        let selectedFormat = '<?php echo $tournamentConfig ? $tournamentConfig['tournament_format'] : ''; ?>';
        let participants = <?php echo json_encode($participants); ?>;
        let draggedElement = null;
        let modalSelectedFormat = '';

        // Modal Management
        function openFormatModal() {
            const modal = document.getElementById('formatModal');

            // Ensure modal is visible with multiple properties
            modal.style.display = 'flex';
            modal.style.visibility = 'visible';
            modal.style.opacity = '1';
            modal.style.zIndex = '99999';

            document.body.style.overflow = 'hidden';

            // Set current selection
            modalSelectedFormat = selectedFormat;
            updateModalSelection();

            // Focus management with delay to ensure modal is rendered
            setTimeout(() => {
                const firstCard = modal.querySelector('.modal-format-card');
                if (firstCard) {
                    firstCard.focus();
                }
            }, 100);

            console.log('Modal opened - Display:', modal.style.display, 'Visibility:', modal.style.visibility);
        }

        function closeFormatModal() {
            const modal = document.getElementById('formatModal');

            // Hide modal with multiple properties
            modal.style.display = 'none';
            modal.style.visibility = 'hidden';
            modal.style.opacity = '0';

            document.body.style.overflow = '';

            // Reset modal selection
            modalSelectedFormat = '';
            updateModalConfirmButton();

            console.log('Modal closed - Display:', modal.style.display, 'Visibility:', modal.style.visibility);
        }

        function selectModalFormat(formatKey) {
            // Remove previous selection in modal
            document.querySelectorAll('.modal-format-card').forEach(card => {
                card.classList.remove('selected');
                // Remove old checkmark indicators
                const oldIndicator = card.querySelector('.format-selected-indicator');
                if (oldIndicator) oldIndicator.remove();

                // Reset action hint
                const actionHint = card.querySelector('.hint-text');
                if (actionHint) {
                    actionHint.textContent = '👆 Click to select';
                }
            });

            // Add selection to clicked card
            const selectedCard = event.currentTarget || document.querySelector(`[data-format="${formatKey}"]`);
            selectedCard.classList.add('selected');
            modalSelectedFormat = formatKey;

            // Add checkmark indicator
            const indicator = document.createElement('div');
            indicator.className = 'format-selected-indicator';
            indicator.setAttribute('aria-hidden', 'true');
            indicator.innerHTML = '<span class="checkmark">✓</span>';
            selectedCard.appendChild(indicator);

            // Update action hint
            const actionHint = selectedCard.querySelector('.hint-text');
            if (actionHint) {
                actionHint.textContent = '✅ Currently Selected';
            }

            updateModalConfirmButton();

            // Show notification
            const formatName = formatKey.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
            showNotification(`Selected: ${formatName}`, 'success');
        }

        function updateModalSelection() {
            document.querySelectorAll('.modal-format-card').forEach(card => {
                const formatKey = card.dataset.format;
                if (formatKey === modalSelectedFormat) {
                    card.classList.add('selected');

                    // Add checkmark if not present
                    if (!card.querySelector('.format-selected-indicator')) {
                        const indicator = document.createElement('div');
                        indicator.className = 'format-selected-indicator';
                        indicator.setAttribute('aria-hidden', 'true');
                        indicator.innerHTML = '<span class="checkmark">✓</span>';
                        card.appendChild(indicator);
                    }

                    const actionHint = card.querySelector('.hint-text');
                    if (actionHint) {
                        actionHint.textContent = '✅ Currently Selected';
                    }
                } else {
                    card.classList.remove('selected');
                    const indicator = card.querySelector('.format-selected-indicator');
                    if (indicator) indicator.remove();

                    const actionHint = card.querySelector('.hint-text');
                    if (actionHint) {
                        actionHint.textContent = '👆 Click to select';
                    }
                }
            });
            updateModalConfirmButton();
        }

        function updateModalConfirmButton() {
            const confirmBtn = document.getElementById('confirmFormatBtn');
            if (confirmBtn) {
                confirmBtn.disabled = !modalSelectedFormat;
                if (modalSelectedFormat) {
                    confirmBtn.style.opacity = '1';
                    confirmBtn.style.cursor = 'pointer';
                } else {
                    confirmBtn.style.opacity = '0.5';
                    confirmBtn.style.cursor = 'not-allowed';
                }
            }
        }

        function confirmFormatSelection() {
            if (!modalSelectedFormat) return;

            // Update the hidden form field
            document.getElementById('modal_selected_format').value = modalSelectedFormat;

            // Submit the form
            document.getElementById('modalFormatForm').submit();
        }

        // Keyboard accessibility for modal format selection
        function handleModalFormatKeydown(event, formatKey) {
            if (event.key === 'Enter' || event.key === ' ') {
                event.preventDefault();
                selectModalFormat(formatKey);
            }
        }

        // Bracket Management
        function regenerateBracket() {
            if (!selectedFormat) {
                showNotification('Please select a tournament format first', 'warning');
                return;
            }

            // Create and submit form for bracket regeneration
            const form = document.createElement('form');
            form.method = 'POST';
            form.style.display = 'none';

            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = '<?php echo $csrfToken; ?>';

            const actionInput = document.createElement('input');
            actionInput.type = 'hidden';
            actionInput.name = 'action';
            actionInput.value = 'generate_bracket';

            form.appendChild(csrfInput);
            form.appendChild(actionInput);
            document.body.appendChild(form);

            showNotification('Regenerating bracket...', 'info');
            form.submit();
        }

        // Enhanced bracket visualization functions
        function toggleFullView() {
            const container = document.getElementById('bracketContainer');
            if (container) {
                container.classList.toggle('fullscreen');

                if (container.classList.contains('fullscreen')) {
                    showNotification('Entered full view mode', 'info');
                } else {
                    showNotification('Exited full view mode', 'info');
                }
            }
        }

        function exportBracket() {
            showNotification('Bracket export feature coming soon!', 'info');
        }

        // Legacy format selection (kept for compatibility)
        function selectFormat(formatKey) {
            selectedFormat = formatKey;
            const formatName = formatKey.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
            showNotification(`Selected: ${formatName}`, 'success');
        }

        function handleFormatKeydown(event, formatKey) {
            if (event.key === 'Enter' || event.key === ' ') {
                event.preventDefault();
                selectFormat(formatKey);
            }
        }

        // Participant Management
        function randomizeSeeding() {
            const participantsList = document.getElementById('participantsList');
            const items = Array.from(participantsList.children);

            // Shuffle array
            for (let i = items.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [items[i], items[j]] = [items[j], items[i]];
            }

            // Update DOM and seed numbers
            items.forEach((item, index) => {
                const seedNumber = item.querySelector('.seed-number');
                seedNumber.textContent = index + 1;
                item.dataset.seed = index + 1;
                participantsList.appendChild(item);
            });

            showNotification('Participant seeding randomized!', 'success');
        }

        function resetSeeding() {
            const participantsList = document.getElementById('participantsList');
            const items = Array.from(participantsList.children);

            // Sort by original order (dept_name)
            items.sort((a, b) => {
                const nameA = a.querySelector('.participant-name').textContent;
                const nameB = b.querySelector('.participant-name').textContent;
                return nameA.localeCompare(nameB);
            });

            // Update DOM and seed numbers
            items.forEach((item, index) => {
                const seedNumber = item.querySelector('.seed-number');
                seedNumber.textContent = index + 1;
                item.dataset.seed = index + 1;
                participantsList.appendChild(item);
            });

            showNotification('Participant seeding reset to alphabetical order!', 'info');
        }

        function editParticipant(deptId) {
            // Open participant editing interface
            console.log('Edit participant:', deptId);
            showNotification('Participant details updated!', 'success');
        }

        // Drag and Drop Functionality
        function initializeDragAndDrop() {
            const participantItems = document.querySelectorAll('.participant-item');

            participantItems.forEach(item => {
                // Desktop drag and drop
                item.addEventListener('dragstart', handleDragStart);
                item.addEventListener('dragover', handleDragOver);
                item.addEventListener('dragenter', handleDragEnter);
                item.addEventListener('dragleave', handleDragLeave);
                item.addEventListener('drop', handleDrop);
                item.addEventListener('dragend', handleDragEnd);

                // Touch device support
                item.addEventListener('touchstart', handleTouchStart, { passive: false });
                item.addEventListener('touchmove', handleTouchMove, { passive: false });
                item.addEventListener('touchend', handleTouchEnd, { passive: false });
            });
        }

        let touchStartY = 0;
        let touchElement = null;
        let touchClone = null;

        function handleDragStart(e) {
            draggedElement = this;
            this.classList.add('dragging');
            e.dataTransfer.effectAllowed = 'move';
            e.dataTransfer.setData('text/html', this.outerHTML);

            // Create drag image
            const dragImage = this.cloneNode(true);
            dragImage.style.transform = 'rotate(3deg)';
            dragImage.style.opacity = '0.8';
            e.dataTransfer.setDragImage(dragImage, 0, 0);
        }

        function handleDragEnter(e) {
            if (draggedElement !== this) {
                this.classList.add('drag-over');
            }
        }

        function handleDragLeave(e) {
            this.classList.remove('drag-over');
        }

        function handleDragOver(e) {
            if (e.preventDefault) {
                e.preventDefault();
            }
            e.dataTransfer.dropEffect = 'move';
            return false;
        }

        function handleDrop(e) {
            if (e.stopPropagation) {
                e.stopPropagation();
            }

            // Remove drag-over class from all items
            document.querySelectorAll('.participant-item').forEach(item => {
                item.classList.remove('drag-over');
            });

            if (draggedElement !== this) {
                swapParticipants(draggedElement, this);
            }

            return false;
        }

        function handleDragEnd(e) {
            this.classList.remove('dragging');
            document.querySelectorAll('.participant-item').forEach(item => {
                item.classList.remove('drag-over');
            });
            draggedElement = null;
        }

        // Touch device support
        function handleTouchStart(e) {
            touchStartY = e.touches[0].clientY;
            touchElement = this;

            // Create visual feedback for touch
            setTimeout(() => {
                if (touchElement === this) {
                    this.classList.add('touch-dragging');
                }
            }, 200);
        }

        function handleTouchMove(e) {
            if (!touchElement) return;

            e.preventDefault();
            const touch = e.touches[0];
            const currentY = touch.clientY;
            const deltaY = currentY - touchStartY;

            // Create clone for visual feedback if not exists
            if (!touchClone && Math.abs(deltaY) > 10) {
                touchClone = touchElement.cloneNode(true);
                touchClone.classList.add('touch-clone');
                touchClone.style.position = 'fixed';
                touchClone.style.zIndex = '10000';
                touchClone.style.pointerEvents = 'none';
                touchClone.style.transform = 'rotate(3deg) scale(1.05)';
                touchClone.style.opacity = '0.9';
                touchClone.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.3)';
                document.body.appendChild(touchClone);

                touchElement.classList.add('dragging');
            }

            if (touchClone) {
                const rect = touchElement.getBoundingClientRect();
                touchClone.style.left = rect.left + 'px';
                touchClone.style.top = (currentY - rect.height / 2) + 'px';
                touchClone.style.width = rect.width + 'px';

                // Find element under touch
                const elementBelow = document.elementFromPoint(touch.clientX, touch.clientY);
                const participantBelow = elementBelow?.closest('.participant-item');

                // Remove previous drag-over states
                document.querySelectorAll('.participant-item').forEach(item => {
                    item.classList.remove('drag-over');
                });

                // Add drag-over to target
                if (participantBelow && participantBelow !== touchElement) {
                    participantBelow.classList.add('drag-over');
                }
            }
        }

        function handleTouchEnd(e) {
            if (!touchElement) return;

            const touch = e.changedTouches[0];
            const elementBelow = document.elementFromPoint(touch.clientX, touch.clientY);
            const participantBelow = elementBelow?.closest('.participant-item');

            // Clean up
            if (touchClone) {
                touchClone.remove();
                touchClone = null;
            }

            touchElement.classList.remove('touch-dragging', 'dragging');
            document.querySelectorAll('.participant-item').forEach(item => {
                item.classList.remove('drag-over');
            });

            // Perform swap if valid target
            if (participantBelow && participantBelow !== touchElement) {
                swapParticipants(touchElement, participantBelow);
            }

            touchElement = null;
        }

        function swapParticipants(element1, element2) {
            // Swap positions with animation
            const draggedSeed = element1.dataset.seed;
            const targetSeed = element2.dataset.seed;

            // Update seed numbers
            element1.querySelector('.seed-number').textContent = targetSeed;
            element2.querySelector('.seed-number').textContent = draggedSeed;

            // Update data attributes
            element1.dataset.seed = targetSeed;
            element2.dataset.seed = draggedSeed;

            // Swap DOM positions with animation
            const parent = element2.parentNode;
            const element1Next = element1.nextSibling;
            const element2Next = element2.nextSibling;

            // Add swap animation
            element1.style.transition = 'transform 0.3s ease';
            element2.style.transition = 'transform 0.3s ease';

            parent.insertBefore(element1, element2Next);
            parent.insertBefore(element2, element1Next);

            // Reset transitions
            setTimeout(() => {
                element1.style.transition = '';
                element2.style.transition = '';
            }, 300);

            showNotification('Participant seeding updated!', 'success');
        }

        function handleDragEnd(e) {
            this.classList.remove('dragging');
            draggedElement = null;
        }

        // Bracket Actions
        function toggleFullView() {
            const bracketContainer = document.getElementById('bracketContainer');

            if (bracketContainer.classList.contains('fullscreen')) {
                bracketContainer.classList.remove('fullscreen');
                document.body.style.overflow = 'auto';
            } else {
                bracketContainer.classList.add('fullscreen');
                bracketContainer.style.position = 'fixed';
                bracketContainer.style.top = '0';
                bracketContainer.style.left = '0';
                bracketContainer.style.width = '100vw';
                bracketContainer.style.height = '100vh';
                bracketContainer.style.zIndex = '9999';
                bracketContainer.style.background = 'white';
                document.body.style.overflow = 'hidden';
            }
        }

        function exportBracket() {
            // Export bracket as image or PDF
            const bracketContainer = document.getElementById('bracketContainer');
            const bracketData = bracketContainer.innerHTML;

            // Create downloadable content
            const blob = new Blob([bracketData], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'tournament_bracket.html';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showNotification('Bracket exported successfully!', 'success');
        }

        // Enhanced Notification System
        function showNotification(message, type = 'info', duration = 4000, persistent = false) {
            // Remove existing notifications of the same type
            const existingNotifications = document.querySelectorAll(`.notification-${type}`);
            existingNotifications.forEach(notification => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            });

            // Create notification element
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} notification-${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                min-width: 320px;
                max-width: 500px;
                padding: 16px 20px;
                border-radius: 8px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                animation: slideInRight 0.3s ease-out;
                font-size: 0.95rem;
                line-height: 1.4;
            `;

            // Set type-specific styles
            const typeStyles = {
                success: 'background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white;',
                error: 'background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white;',
                warning: 'background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white;',
                info: 'background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%); color: white;'
            };

            notification.style.cssText += typeStyles[type] || typeStyles.info;

            // Add icon and close button
            const icons = {
                success: '✅',
                error: '❌',
                warning: '⚠️',
                info: 'ℹ️'
            };

            notification.innerHTML = `
                <div style="display: flex; align-items: flex-start; gap: 12px;">
                    <span style="font-size: 1.2rem; flex-shrink: 0;">${icons[type] || icons.info}</span>
                    <div style="flex: 1;">
                        <div style="font-weight: 600; margin-bottom: 4px;">${type.charAt(0).toUpperCase() + type.slice(1)}</div>
                        <div>${message}</div>
                    </div>
                    ${!persistent ? '<button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: inherit; font-size: 1.2rem; cursor: pointer; padding: 0; margin-left: 8px; opacity: 0.7; hover: opacity: 1;">×</button>' : ''}
                </div>
            `;

            document.body.appendChild(notification);

            // Auto remove after specified duration (unless persistent)
            if (!persistent) {
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.style.animation = 'slideOutRight 0.3s ease-in';
                        setTimeout(() => {
                            if (notification.parentNode) {
                                notification.parentNode.removeChild(notification);
                            }
                        }, 300);
                    }
                }, duration);
            }

            return notification;
        }

        // Enhanced form validation with visual feedback
        function validateField(field, rules = {}) {
            const value = field.value.trim();
            const fieldName = field.getAttribute('data-field-name') || field.name || 'Field';
            let isValid = true;
            let errorMessage = '';

            // Remove existing error styling
            field.classList.remove('field-error', 'field-success');
            const existingError = field.parentNode.querySelector('.field-error-message');
            if (existingError) {
                existingError.remove();
            }

            // Required validation
            if (rules.required && !value) {
                isValid = false;
                errorMessage = `${fieldName} is required`;
            }

            // Minimum length validation
            if (isValid && rules.minLength && value.length < rules.minLength) {
                isValid = false;
                errorMessage = `${fieldName} must be at least ${rules.minLength} characters`;
            }

            // Maximum length validation
            if (isValid && rules.maxLength && value.length > rules.maxLength) {
                isValid = false;
                errorMessage = `${fieldName} must not exceed ${rules.maxLength} characters`;
            }

            // Email validation
            if (isValid && rules.email && value) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                    isValid = false;
                    errorMessage = `Please enter a valid email address`;
                }
            }

            // Number validation
            if (isValid && rules.number && value) {
                if (isNaN(value)) {
                    isValid = false;
                    errorMessage = `${fieldName} must be a valid number`;
                } else if (rules.min !== undefined && parseFloat(value) < rules.min) {
                    isValid = false;
                    errorMessage = `${fieldName} must be at least ${rules.min}`;
                } else if (rules.max !== undefined && parseFloat(value) > rules.max) {
                    isValid = false;
                    errorMessage = `${fieldName} must not exceed ${rules.max}`;
                }
            }

            // Date validation
            if (isValid && rules.date && value) {
                const date = new Date(value);
                if (isNaN(date.getTime())) {
                    isValid = false;
                    errorMessage = `Please enter a valid date`;
                } else if (rules.minDate && date < new Date(rules.minDate)) {
                    isValid = false;
                    errorMessage = `Date must be after ${rules.minDate}`;
                } else if (rules.maxDate && date > new Date(rules.maxDate)) {
                    isValid = false;
                    errorMessage = `Date must be before ${rules.maxDate}`;
                }
            }

            // Apply visual feedback
            if (isValid) {
                field.classList.add('field-success');
            } else {
                field.classList.add('field-error');

                // Add error message
                const errorElement = document.createElement('div');
                errorElement.className = 'field-error-message';
                errorElement.style.cssText = `
                    color: #ef4444;
                    font-size: 0.85rem;
                    margin-top: 4px;
                    display: flex;
                    align-items: center;
                    gap: 4px;
                `;
                errorElement.innerHTML = `<span>⚠️</span> ${errorMessage}`;
                field.parentNode.appendChild(errorElement);
            }

            return { isValid, errorMessage };
        }

        // Enhanced Form Validation Functions
        function validateFormatSelection() {
            if (!selectedFormat) {
                showNotification('Please select a tournament format before proceeding!', 'error', 5000);

                // Highlight format selection area
                const formatGrid = document.querySelector('.format-grid');
                if (formatGrid) {
                    formatGrid.style.border = '2px solid #ef4444';
                    formatGrid.style.borderRadius = '8px';
                    formatGrid.style.animation = 'shake 0.5s ease-in-out';

                    setTimeout(() => {
                        formatGrid.style.border = '';
                        formatGrid.style.animation = '';
                    }, 2000);
                }

                return false;
            }
            return true;
        }

        function validateBracketConfiguration() {
            const errors = [];

            // Check if format is selected
            if (!selectedFormat) {
                errors.push('Tournament format must be selected');
            }

            // Check bracket size
            const bracketSizeInput = document.querySelector('input[name="bracket_size"]');
            if (bracketSizeInput) {
                const bracketSize = parseInt(bracketSizeInput.value);
                const participantCount = <?php echo count($participants); ?>;

                if (bracketSize < 2) {
                    errors.push('Bracket size must be at least 2');
                } else if (bracketSize > participantCount) {
                    errors.push(`Bracket size cannot exceed participant count (${participantCount})`);
                }
            }

            // Check seeding configuration
            const seedingType = document.querySelector('select[name="seeding_type"]');
            if (seedingType && !seedingType.value) {
                errors.push('Seeding type must be selected');
            }

            return errors;
        }

        function validateParticipantConfiguration() {
            const errors = [];
            const participantCount = <?php echo count($participants); ?>;

            if (participantCount < 2) {
                errors.push('At least 2 participants are required for a tournament');
            }

            // Check for duplicate participants based on department ID (more reliable)
            const participantDeptIds = [];
            document.querySelectorAll('.participant-item').forEach(participantElement => {
                const deptId = participantElement.getAttribute('data-dept-id');
                if (deptId && participantDeptIds.includes(deptId)) {
                    const deptName = participantElement.querySelector('.participant-name')?.textContent.trim() || 'Unknown';
                    errors.push(`Duplicate participant found: ${deptName}`);
                } else if (deptId) {
                    participantDeptIds.push(deptId);
                }
            });

            return errors;
        }

        function validateCompleteConfiguration() {
            const allErrors = [
                ...validateBracketConfiguration(),
                ...validateParticipantConfiguration()
            ];

            if (allErrors.length > 0) {
                const errorList = allErrors.map(error => `• ${error}`).join('<br>');
                showNotification(
                    `Please fix the following issues:<br><br>${errorList}`,
                    'error',
                    8000
                );
                return false;
            }

            return true;
        }

        function showValidationProgress(current, total) {
            let progressContainer = document.querySelector('.validation-progress-container');

            if (!progressContainer) {
                progressContainer = document.createElement('div');
                progressContainer.className = 'validation-progress-container';
                progressContainer.style.cssText = `
                    position: fixed;
                    top: 80px;
                    right: 20px;
                    width: 300px;
                    background: white;
                    padding: 16px;
                    border-radius: 8px;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
                    z-index: 9999;
                `;
                progressContainer.innerHTML = `
                    <div style="font-weight: 600; margin-bottom: 8px;">Validation Progress</div>
                    <div class="validation-progress">
                        <div class="validation-progress-bar" style="width: 0%;"></div>
                    </div>
                    <div class="validation-status" style="font-size: 0.85rem; margin-top: 8px; color: #6b7280;">
                        Checking configuration...
                    </div>
                `;
                document.body.appendChild(progressContainer);
            }

            const progressBar = progressContainer.querySelector('.validation-progress-bar');
            const statusText = progressContainer.querySelector('.validation-status');

            const percentage = Math.round((current / total) * 100);
            progressBar.style.width = `${percentage}%`;
            statusText.textContent = `${current} of ${total} checks completed`;

            if (current === total) {
                setTimeout(() => {
                    if (progressContainer.parentNode) {
                        progressContainer.parentNode.removeChild(progressContainer);
                    }
                }, 2000);
            }
        }

        // Regenerate bracket function
        function regenerateBracket() {
            if (confirm('Are you sure you want to regenerate the tournament bracket? This will reset all current bracket data.')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                    <input type="hidden" name="action" value="generate_bracket">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // Enhanced Wizard Navigation Functions
        function updateWizardProgress() {
            const progressBar = document.querySelector('.wizard-progress');
            const steps = document.querySelectorAll('.wizard-step');
            let completedSteps = 0;

            steps.forEach(step => {
                if (step.classList.contains('completed')) {
                    completedSteps++;
                } else if (step.classList.contains('active')) {
                    completedSteps += 0.5; // Current step is half complete
                }
            });

            const progressPercentage = (completedSteps / steps.length) * 100;
            if (progressBar) {
                progressBar.style.width = progressPercentage + '%';
            }
        }

        function enableNextStep() {
            const currentStep = document.querySelector('.wizard-step.active');
            const nextStep = currentStep?.nextElementSibling;

            if (nextStep && nextStep.classList.contains('wizard-step')) {
                nextStep.classList.remove('disabled');
                nextStep.style.opacity = '1';
                nextStep.style.cursor = 'pointer';

                // Add click handler for next step
                if (nextStep.querySelector('.step-number').textContent === '3') {
                    nextStep.onclick = () => {
                        window.location.href = `tournament_schedule.php?event_sport_id=<?php echo $eventSportId; ?>&event_id=<?php echo $eventId; ?>`;
                    };
                }
            }

            updateWizardProgress();
        }

        function addWizardStepHoverEffects() {
            const steps = document.querySelectorAll('.wizard-step');

            steps.forEach(step => {
                if (!step.classList.contains('disabled')) {
                    step.addEventListener('mouseenter', function() {
                        if (!this.classList.contains('active')) {
                            this.style.transform = 'translateY(-2px)';
                            this.style.boxShadow = '0 6px 20px rgba(0, 0, 0, 0.15)';
                        }
                    });

                    step.addEventListener('mouseleave', function() {
                        if (!this.classList.contains('active')) {
                            this.style.transform = '';
                            this.style.boxShadow = '';
                        }
                    });
                }
            });
        }

        function initializeWizardKeyboardNavigation() {
            const steps = document.querySelectorAll('.wizard-step:not(.disabled)');

            steps.forEach((step, index) => {
                step.setAttribute('tabindex', '0');
                step.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        this.click();
                    } else if (e.key === 'ArrowLeft' && index > 0) {
                        e.preventDefault();
                        steps[index - 1].focus();
                    } else if (e.key === 'ArrowRight' && index < steps.length - 1) {
                        e.preventDefault();
                        steps[index + 1].focus();
                    }
                });
            });
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Tournament Configuration Page Loaded');
            console.log('Available formats:', <?php echo json_encode(array_keys($availableFormats)); ?>);
            console.log('Participants:', participants);

            // Initialize enhanced wizard navigation
            updateWizardProgress();
            addWizardStepHoverEffects();
            initializeWizardKeyboardNavigation();

            // Initialize drag and drop
            initializeDragAndDrop();

            // Enhanced form submission validation
            const formatForm = document.getElementById('formatForm');
            if (formatForm) {
                formatForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    // Show loading state
                    const submitBtn = formatForm.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.classList.add('loading');
                        submitBtn.disabled = true;
                    }

                    // Perform comprehensive validation
                    let validationStep = 0;
                    const totalSteps = 3;

                    showValidationProgress(++validationStep, totalSteps);

                    setTimeout(() => {
                        if (!validateFormatSelection()) {
                            resetSubmitButton(submitBtn);
                            return;
                        }

                        showValidationProgress(++validationStep, totalSteps);

                        setTimeout(() => {
                            if (!validateCompleteConfiguration()) {
                                resetSubmitButton(submitBtn);
                                return;
                            }

                            showValidationProgress(++validationStep, totalSteps);

                            setTimeout(() => {
                                // All validations passed, submit the form
                                showNotification('Validation successful! Saving configuration...', 'success', 3000);

                                // Actually submit the form
                                formatForm.submit();
                            }, 500);
                        }, 500);
                    }, 500);
                });
            }

            function resetSubmitButton(button) {
                if (button) {
                    button.classList.remove('loading');
                    button.disabled = false;
                }
            }

            // Initialize save button state
            const saveBtn = document.getElementById('saveConfigBtn');
            if (saveBtn && !selectedFormat) {
                saveBtn.disabled = true;
                saveBtn.style.opacity = '0.5';
                saveBtn.style.cursor = 'not-allowed';
            }

            // Highlight selected format if exists
            if (selectedFormat) {
                const formatCard = document.querySelector(`[onclick="selectFormat('${selectedFormat}')"]`);
                if (formatCard) {
                    formatCard.classList.add('selected');
                    // Add checkmark
                    const checkmark = document.createElement('div');
                    checkmark.style.cssText = 'position: absolute; top: 10px; right: 10px; background: #10b981; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; font-size: 14px;';
                    checkmark.textContent = '✓';
                    formatCard.appendChild(checkmark);
                }

                // Enable save button
                if (saveBtn) {
                    saveBtn.disabled = false;
                    saveBtn.style.opacity = '1';
                    saveBtn.style.cursor = 'pointer';
                }

                // Enable next step if bracket is already generated
                <?php if ($tournamentConfig && !empty($tournamentConfig['bracket_data'])): ?>
                enableNextStep();
                <?php endif; ?>
            }

            // Auto-save functionality (optional)
            let autoSaveTimeout;
            document.addEventListener('change', function() {
                clearTimeout(autoSaveTimeout);
                autoSaveTimeout = setTimeout(() => {
                    console.log('Auto-save triggered');
                    // Implement auto-save logic here
                }, 2000);
            });

            // Show helpful message if no format selected
            if (!selectedFormat) {
                setTimeout(() => {
                    showNotification('👆 Click on a tournament format card to select it!', 'info');
                }, 2000);
            } else {
                setTimeout(() => {
                    showNotification('Format already selected. You can change it or save configuration.', 'success');
                }, 1000);
            }
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+S to save
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                if (validateFormatSelection()) {
                    document.getElementById('formatForm').submit();
                }
            }

            // Escape to close full view
            if (e.key === 'Escape') {
                const bracketContainer = document.getElementById('bracketContainer');
                if (bracketContainer.classList.contains('fullscreen')) {
                    toggleFullView();
                }
            }
        });
    </script>
</body>
</html>

<?php
/**
 * Render professional Score7.io-style bracket visualization
 */
function renderProfessionalBracketVisualization($bracketData, $participants) {
    if (!$bracketData || !isset($bracketData['format'])) {
        echo '<div class="bracket-placeholder">
                <div class="bracket-placeholder-icon">⚠️</div>
                <h4>Invalid Bracket Data</h4>
                <p>The bracket data appears to be corrupted or invalid.</p>
              </div>';
        return;
    }

    $format = $bracketData['format'];
    $matches = $bracketData['matches'] ?? [];

    // Create participant lookup for quick access
    $participantLookup = [];
    foreach ($participants as $participant) {
        $participantLookup[$participant['dept_id']] = $participant;
    }

    echo '<div class="professional-bracket-visualization">';

    // Bracket Header
    echo '<div class="bracket-format-header">';
    echo '<h4>' . ucfirst(str_replace('_', ' ', $format)) . ' Tournament</h4>';
    echo '<p>' . count($participants) . ' teams competing</p>';
    echo '</div>';

    if ($format === 'single_elimination') {
        renderProfessionalSingleElimination($matches, $participantLookup);
    } elseif ($format === 'double_elimination') {
        renderProfessionalDoubleElimination($matches, $participantLookup);
    } elseif ($format === 'round_robin') {
        renderProfessionalRoundRobin($matches, $participantLookup);
    } else {
        echo '<div class="bracket-placeholder">
                <div class="bracket-placeholder-icon">🏗️</div>
                <h4>Format Not Supported</h4>
                <p>Professional visualization for ' . htmlspecialchars($format) . ' is coming soon!</p>
              </div>';
    }

    echo '</div>';
}

/**
 * Render professional single elimination bracket
 */
function renderProfessionalSingleElimination($matches, $participantLookup) {
    // Group matches by round
    $rounds = [];
    foreach ($matches as $match) {
        $round = $match['round'] ?? 1;
        if (!isset($rounds[$round])) {
            $rounds[$round] = [];
        }
        $rounds[$round][] = $match;
    }

    ksort($rounds);

    echo '<div class="score7-bracket-container">';
    echo '<div class="score7-bracket-header">';
    echo '<h3 class="bracket-title">🏆 Tournament Bracket</h3>';
    echo '<div class="bracket-format-info">Single Elimination • ' . count($rounds) . ' Rounds</div>';
    echo '</div>';

    echo '<div class="score7-rounds-container">';

    foreach ($rounds as $roundNum => $roundMatches) {
        $roundName = 'Round ' . $roundNum;
        if ($roundNum == count($rounds)) {
            $roundName = 'Final';
        } elseif ($roundNum == count($rounds) - 1) {
            $roundName = 'Semifinals';
        } elseif ($roundNum == count($rounds) - 2) {
            $roundName = 'Quarterfinals';
        }

        echo '<div class="score7-round">';
        echo '<div class="score7-round-header">';
        echo '<h4>' . htmlspecialchars($roundName) . '</h4>';
        echo '</div>';

        echo '<div class="score7-round-matches">';
        foreach ($roundMatches as $match) {
            renderScore7MatchCard($match, $participantLookup);
        }
        echo '</div>';
        echo '</div>';
    }

    echo '</div>';
    echo '</div>';
}

/**
 * Render professional double elimination bracket
 */
function renderProfessionalDoubleElimination($matches, $participantLookup) {
    // Separate winner and loser bracket matches
    $winnerBracket = [];
    $loserBracket = [];

    foreach ($matches as $match) {
        if (isset($match['bracket_type']) && $match['bracket_type'] === 'loser') {
            $round = $match['round'] ?? 1;
            if (!isset($loserBracket[$round])) {
                $loserBracket[$round] = [];
            }
            $loserBracket[$round][] = $match;
        } else {
            $round = $match['round'] ?? 1;
            if (!isset($winnerBracket[$round])) {
                $winnerBracket[$round] = [];
            }
            $winnerBracket[$round][] = $match;
        }
    }

    echo '<div class="professional-bracket-container double-elimination">';

    // Winner Bracket
    if (!empty($winnerBracket)) {
        echo '<div class="winner-bracket">';
        echo '<h4 class="bracket-section-title">Winner Bracket</h4>';
        echo '<div class="bracket-rounds-container">';

        ksort($winnerBracket);
        foreach ($winnerBracket as $roundNum => $roundMatches) {
            $roundName = 'Round ' . $roundNum;

            echo '<div class="bracket-round">';
            echo '<div class="bracket-round-header">';
            echo '<h5>' . htmlspecialchars($roundName) . '</h5>';
            echo '</div>';

            echo '<div class="round-matches">';
            foreach ($roundMatches as $match) {
                renderScore7MatchCard($match, $participantLookup);
            }
            echo '</div>';
            echo '</div>';
        }

        echo '</div>';
        echo '</div>';
    }

    // Loser Bracket
    if (!empty($loserBracket)) {
        echo '<div class="loser-bracket">';
        echo '<h4 class="bracket-section-title">Loser Bracket</h4>';
        echo '<div class="bracket-rounds-container">';

        ksort($loserBracket);
        foreach ($loserBracket as $roundNum => $roundMatches) {
            $roundName = 'LB Round ' . $roundNum;

            echo '<div class="bracket-round">';
            echo '<div class="bracket-round-header">';
            echo '<h5>' . htmlspecialchars($roundName) . '</h5>';
            echo '</div>';

            echo '<div class="round-matches">';
            foreach ($roundMatches as $match) {
                renderScore7MatchCard($match, $participantLookup);
            }
            echo '</div>';
            echo '</div>';
        }

        echo '</div>';
        echo '</div>';
    }

    echo '</div>';
}

/**
 * Render professional round robin bracket
 */
function renderProfessionalRoundRobin($matches, $participantLookup) {
    echo '<div class="professional-bracket-container round-robin">';
    echo '<div class="round-robin-grid">';

    // Group matches by round
    $rounds = [];
    foreach ($matches as $match) {
        $round = $match['round'] ?? 1;
        if (!isset($rounds[$round])) {
            $rounds[$round] = [];
        }
        $rounds[$round][] = $match;
    }

    ksort($rounds);

    foreach ($rounds as $roundNum => $roundMatches) {
        echo '<div class="round-robin-round">';
        echo '<h5>Round ' . $roundNum . '</h5>';
        echo '<div class="round-matches">';

        foreach ($roundMatches as $match) {
            renderScore7MatchCard($match, $participantLookup);
        }

        echo '</div>';
        echo '</div>';
    }

    echo '</div>';
    echo '</div>';
}

/**
 * Render Score7.io style match card
 */
function renderScore7MatchCard($match, $participantLookup) {
    $team1 = isset($match['team1_id']) && isset($participantLookup[$match['team1_id']])
        ? $participantLookup[$match['team1_id']]
        : null;
    $team2 = isset($match['team2_id']) && isset($participantLookup[$match['team2_id']])
        ? $participantLookup[$match['team2_id']]
        : null;

    $team1Name = $team1 ? htmlspecialchars($team1['dept_name']) : 'TBD';
    $team2Name = $team2 ? htmlspecialchars($team2['dept_name']) : 'TBD';

    $team1Score = $match['team1_score'] ?? '';
    $team2Score = $match['team2_score'] ?? '';

    $isCompleted = !empty($team1Score) && !empty($team2Score);
    $winnerId = $match['winner_id'] ?? null;

    echo '<div class="score7-match-card' . ($isCompleted ? ' completed' : '') . '">';

    // Team 1
    $team1Id = $match['team1_id'] ?? null;
    echo '<div class="score7-team' . ($winnerId && $winnerId == $team1Id ? ' winner' : '') . '">';
    echo '<div class="team-flag">🏴</div>';
    echo '<div class="team-info">';
    echo '<div class="team-name">' . $team1Name . '</div>';
    echo '</div>';
    if ($isCompleted) {
        echo '<div class="team-score">' . htmlspecialchars($team1Score) . '</div>';
    } else {
        echo '<div class="team-score placeholder">-</div>';
    }
    echo '</div>';

    // Team 2
    $team2Id = $match['team2_id'] ?? null;
    echo '<div class="score7-team' . ($winnerId && $winnerId == $team2Id ? ' winner' : '') . '">';
    echo '<div class="team-flag">🏴</div>';
    echo '<div class="team-info">';
    echo '<div class="team-name">' . $team2Name . '</div>';
    echo '</div>';
    if ($isCompleted) {
        echo '<div class="team-score">' . htmlspecialchars($team2Score) . '</div>';
    } else {
        echo '<div class="team-score placeholder">-</div>';
    }
    echo '</div>';

    // Match status indicator
    if ($isCompleted) {
        echo '<div class="match-status-indicator completed"></div>';
    } else {
        echo '<div class="match-status-indicator pending"></div>';
    }

    echo '</div>';
}

/**
 * Get round name based on round number and total rounds
 */
function getRoundName($roundNum, $totalRounds) {
    if ($totalRounds <= 1) {
        return 'Final';
    }

    $roundsFromEnd = $totalRounds - $roundNum + 1;

    switch ($roundsFromEnd) {
        case 1:
            return 'Final';
        case 2:
            return 'Semifinals';
        case 3:
            return 'Quarterfinals';
        case 4:
            return 'Round of 16';
        case 5:
            return 'Round of 32';
        default:
            return 'Round ' . $roundNum;
    }
}

/**
 * Render bracket visualization based on bracket data
 */
function renderBracketVisualization($bracketData) {
    if (!$bracketData || !isset($bracketData['format'])) {
        echo '<div class="bracket-placeholder">
                <div class="bracket-placeholder-icon">⚠️</div>
                <h4>Invalid Bracket Data</h4>
                <p>The bracket data appears to be corrupted or invalid.</p>
              </div>';
        return;
    }

    $format = $bracketData['format'];

    echo '<div class="bracket-visualization" data-format="' . htmlspecialchars($format) . '">';

    switch ($format) {
        case 'single_elimination':
            renderSingleEliminationBracket($bracketData);
            break;

        case 'double_elimination':
            renderDoubleEliminationBracket($bracketData);
            break;

        case 'round_robin':
            renderRoundRobinBracket($bracketData);
            break;

        default:
            echo '<div class="bracket-placeholder">
                    <div class="bracket-placeholder-icon">🔧</div>
                    <h4>Format Not Implemented</h4>
                    <p>Visualization for ' . htmlspecialchars($format) . ' format is coming soon!</p>
                  </div>';
    }

    echo '</div>';
}

/**
 * Render single elimination bracket
 */
function renderSingleEliminationBracket($bracketData) {
    if (!isset($bracketData['rounds']) || empty($bracketData['rounds'])) {
        echo '<div class="bracket-placeholder">
                <div class="bracket-placeholder-icon">🏆</div>
                <h4>No Rounds Generated</h4>
                <p>Generate the bracket to see the tournament structure.</p>
              </div>';
        return;
    }

    echo '<div class="single-elimination-bracket">';

    // Add format header
    echo '<div class="bracket-format-header">';
    echo '<h4>Single Elimination Tournament</h4>';
    echo '<div class="bracket-format-meta">';
    echo '<span class="bracket-meta-item">Rounds: ' . count($bracketData['rounds']) . '</span>';
    echo '<span class="bracket-meta-item">Format: Single Elimination</span>';
    echo '</div>';
    echo '</div>';

    echo '<div class="bracket-rounds-container">';
    foreach ($bracketData['rounds'] as $roundIndex => $round) {
        echo '<div class="bracket-round" data-round="' . ($roundIndex + 1) . '">';
        echo '<div class="bracket-round-header">';
        echo '<h5 class="bracket-round-title">' . htmlspecialchars($round['round_name']) . '</h5>';
        echo '</div>';

        if (!empty($round['matches'])) {
            echo '<div class="round-matches">';
            foreach ($round['matches'] as $matchIndex => $match) {
                echo '<div class="match-card">';

                // Match header
                echo '<div class="match-header">';
                echo '<div class="match-id">Match ' . ($matchIndex + 1) . '</div>';
                echo '</div>';

                // Match participants
                echo '<div class="match-participants">';
                echo '<div class="match-participant">';
                echo '<span class="participant-name">' . htmlspecialchars($match['participant_1']) . '</span>';
                echo '</div>';

                echo '<div class="match-vs">VS</div>';

                echo '<div class="match-participant">';
                echo '<span class="participant-name">' . htmlspecialchars($match['participant_2']) . '</span>';
                echo '</div>';
                echo '</div>';

                // Match footer
                echo '<div class="match-footer">';
                echo '<div class="match-status pending">' . htmlspecialchars($match['status']) . '</div>';
                echo '</div>';

                echo '</div>';
            }
            echo '</div>';
        }

        echo '</div>';
    }
    echo '</div>';

    echo '</div>';
}

/**
 * Render double elimination bracket
 */
function renderDoubleEliminationBracket($bracketData) {
    echo '<div class="double-elimination-bracket">';
    echo '<h4>Double Elimination Tournament</h4>';

    // Winners Bracket
    if (isset($bracketData['winners_bracket']) && !empty($bracketData['winners_bracket'])) {
        echo '<div class="winners-bracket">';
        echo '<h5 style="color: #059669;">Winners Bracket</h5>';
        foreach ($bracketData['winners_bracket'] as $round) {
            echo '<div class="bracket-round">';
            echo '<h6>' . htmlspecialchars($round['round_name']) . '</h6>';
            if (!empty($round['matches'])) {
                echo '<div class="round-matches">';
                foreach ($round['matches'] as $match) {
                    echo '<div class="match-card">';
                    echo '<div class="match-participant">' . htmlspecialchars($match['participant_1']) . '</div>';
                    echo '<div class="match-vs">VS</div>';
                    echo '<div class="match-participant">' . htmlspecialchars($match['participant_2']) . '</div>';
                    echo '<div class="match-status" data-status="' . $match['status'] . '">' . htmlspecialchars($match['status']) . '</div>';
                    echo '</div>';
                }
                echo '</div>';
            }
            echo '</div>';
        }
        echo '</div>';
    }

    // Losers Bracket (placeholder for now)
    echo '<div class="losers-bracket" style="margin-top: 30px;">';
    echo '<h5 style="color: #dc2626;">Losers Bracket</h5>';
    echo '<p style="color: #6b7280;">Losers bracket will be populated as matches are completed.</p>';
    echo '</div>';

    echo '</div>';
}

/**
 * Render round robin bracket
 */
function renderRoundRobinBracket($bracketData) {
    if (!isset($bracketData['matches']) || empty($bracketData['matches'])) {
        echo '<div class="bracket-placeholder">
                <div class="bracket-placeholder-icon">🔁</div>
                <h4>No Matches Generated</h4>
                <p>Generate the bracket to see the round robin schedule.</p>
              </div>';
        return;
    }

    echo '<div class="round-robin-bracket">';

    // Add format header
    echo '<div class="bracket-format-header">';
    echo '<h4>Round Robin Tournament</h4>';
    echo '<div class="bracket-format-meta">';
    echo '<span class="bracket-meta-item">Matches: ' . count($bracketData['matches']) . '</span>';
    echo '<span class="bracket-meta-item">Format: Round Robin</span>';
    echo '</div>';
    echo '</div>';

    echo '<div class="round-robin-grid">';
    foreach ($bracketData['matches'] as $matchIndex => $match) {
        echo '<div class="match-card">';

        // Match header
        echo '<div class="match-header">';
        echo '<div class="match-id">Match ' . ($matchIndex + 1) . '</div>';
        echo '</div>';

        // Match participants
        echo '<div class="match-participants">';
        echo '<div class="match-participant">';
        echo '<span class="participant-name">' . htmlspecialchars($match['participant_1']) . '</span>';
        echo '</div>';

        echo '<div class="match-vs">VS</div>';

        echo '<div class="match-participant">';
        echo '<span class="participant-name">' . htmlspecialchars($match['participant_2']) . '</span>';
        echo '</div>';
        echo '</div>';

        // Match footer
        echo '<div class="match-footer">';
        echo '<div class="match-status pending">' . htmlspecialchars($match['status']) . '</div>';
        echo '</div>';

        echo '</div>';
    }

    echo '</div>';
    echo '</div>';
}
?>