-- Migration: Add phone field to admin_users table
-- Date: December 2024
-- Description: Adds phone field to support profile management

-- Check if phone column exists before adding it
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'admin_users'
    AND COLUMN_NAME = 'phone'
);

-- Add phone column if it doesn't exist
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE admin_users ADD COLUMN phone VARCHAR(20) NULL AFTER full_name',
    'SELECT "Phone column already exists" AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Verify the change
SELECT 'Phone field migration completed successfully' AS status;
