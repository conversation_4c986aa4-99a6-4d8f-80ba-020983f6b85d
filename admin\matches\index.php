<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Match Management - Main Page
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication
requireAuth();

// Handle actions
$action = $_GET['action'] ?? '';
$message = '';
$messageType = 'info';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('Invalid security token');
        }
        
        switch ($action) {
            case 'delete':
                $matchId = (int)($_POST['match_id'] ?? 0);
                if ($matchId) {
                    // Check if match has scores
                    $scoreCount = fetchOne("SELECT COUNT(*) as count FROM scores WHERE match_id = ?", [$matchId])['count'];
                    if ($scoreCount > 0) {
                        throw new Exception('Cannot delete match with recorded scores');
                    }
                    
                    deleteRecord('matches', 'match_id = :match_id', ['match_id' => $matchId]);
                    logActivity('match_deleted', "Match ID {$matchId} deleted");
                    $message = 'Match deleted successfully';
                    $messageType = 'success';
                }
                break;
                
            case 'update_status':
                $matchId = (int)($_POST['match_id'] ?? 0);
                $newStatus = sanitizeInput($_POST['new_status'] ?? '');
                
                if ($matchId && in_array($newStatus, ['scheduled', 'ongoing', 'completed', 'cancelled'])) {
                    updateRecord('matches', ['status' => $newStatus], 'match_id = :match_id', ['match_id' => $matchId]);
                    logActivity('match_status_changed', "Match ID {$matchId} status changed to {$newStatus}");
                    $message = 'Match status updated successfully';
                    $messageType = 'success';
                }
                break;
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get matches with pagination and filters
$page = (int)($_GET['page'] ?? 1);
$search = sanitizeInput($_GET['search'] ?? '');
$eventFilter = (int)($_GET['event'] ?? 0);
$sportFilter = (int)($_GET['sport'] ?? 0);
$statusFilter = sanitizeInput($_GET['status'] ?? '');
$dateFilter = sanitizeInput($_GET['date'] ?? '');

$whereConditions = [];
$params = [];

if ($search) {
    $whereConditions[] = "(s.name LIKE ? OR v.name LIKE ? OR m.match_number LIKE ?)";
    $params[] = "%{$search}%";
    $params[] = "%{$search}%";
    $params[] = "%{$search}%";
}

if ($eventFilter) {
    $whereConditions[] = "m.event_id = ?";
    $params[] = $eventFilter;
}

if ($sportFilter) {
    $whereConditions[] = "m.sport_id = ?";
    $params[] = $sportFilter;
}

if ($statusFilter) {
    $whereConditions[] = "m.status = ?";
    $params[] = $statusFilter;
}

if ($dateFilter) {
    $whereConditions[] = "m.match_date = ?";
    $params[] = $dateFilter;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// Count total records
$totalRecords = fetchOne("
    SELECT COUNT(*) as count 
    FROM matches m
    JOIN sports s ON m.sport_id = s.sport_id
    LEFT JOIN venues v ON m.venue_id = v.venue_id
    JOIN events e ON m.event_id = e.event_id
    {$whereClause}
", $params)['count'];

$pagination = paginate($totalRecords, $page);

// Get matches
$matches = fetchAll("
    SELECT m.*, s.name as sport_name, s.category as sport_category,
           v.name as venue_name, v.location as venue_location,
           e.name as event_name,
           (SELECT COUNT(*) FROM match_participants WHERE match_id = m.match_id) as participant_count,
           (SELECT COUNT(*) FROM scores WHERE match_id = m.match_id AND is_final = 1) as score_count
    FROM matches m
    JOIN sports s ON m.sport_id = s.sport_id
    LEFT JOIN venues v ON m.venue_id = v.venue_id
    JOIN events e ON m.event_id = e.event_id
    {$whereClause}
    ORDER BY m.match_date DESC, m.match_time DESC
    LIMIT {$pagination['records_per_page']} OFFSET {$pagination['offset']}
", $params);

// Get filter options
$events = fetchAll("SELECT event_id, name FROM events ORDER BY name");
$sports = fetchAll("SELECT sport_id, name FROM sports WHERE status = 'active' ORDER BY name");

$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Match Management - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>
    
    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1>Match Management</h1>
                <nav class="breadcrumb">
                    <a href="../dashboard.php">Dashboard</a>
                    <span>/</span>
                    <span>Matches</span>
                </nav>
            </div>
            <div class="page-actions">
                <a href="create.php" class="btn btn-primary">
                    <i class="icon-plus"></i>
                    Schedule Match
                </a>
                <a href="bulk-schedule.php" class="btn btn-secondary">
                    <i class="icon-calendar"></i>
                    Bulk Schedule
                </a>
            </div>
        </div>
        
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <!-- Filters and Search -->
        <div class="filters-container">
            <form method="GET" class="filters-form">
                <div class="filter-group">
                    <input type="text" name="search" placeholder="Search matches..." 
                           value="<?php echo htmlspecialchars($search); ?>" class="form-control">
                </div>
                
                <div class="filter-group">
                    <select name="event" class="form-control">
                        <option value="">All Events</option>
                        <?php foreach ($events as $event): ?>
                            <option value="<?php echo $event['event_id']; ?>" 
                                    <?php echo $eventFilter == $event['event_id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($event['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="filter-group">
                    <select name="sport" class="form-control">
                        <option value="">All Sports</option>
                        <?php foreach ($sports as $sport): ?>
                            <option value="<?php echo $sport['sport_id']; ?>" 
                                    <?php echo $sportFilter == $sport['sport_id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($sport['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="filter-group">
                    <select name="status" class="form-control">
                        <option value="">All Status</option>
                        <option value="scheduled" <?php echo $statusFilter === 'scheduled' ? 'selected' : ''; ?>>Scheduled</option>
                        <option value="ongoing" <?php echo $statusFilter === 'ongoing' ? 'selected' : ''; ?>>Ongoing</option>
                        <option value="completed" <?php echo $statusFilter === 'completed' ? 'selected' : ''; ?>>Completed</option>
                        <option value="cancelled" <?php echo $statusFilter === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <input type="date" name="date" value="<?php echo htmlspecialchars($dateFilter); ?>" class="form-control">
                </div>
                
                <div class="filter-actions">
                    <button type="submit" class="btn btn-secondary">Filter</button>
                    <a href="index.php" class="btn btn-outline">Clear</a>
                </div>
            </form>
        </div>
        
        <!-- Matches Table -->
        <div class="table-container">
            <?php if (!empty($matches)): ?>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Match Details</th>
                            <th>Sport</th>
                            <th>Date & Time</th>
                            <th>Venue</th>
                            <th>Status</th>
                            <th>Participants</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($matches as $match): ?>
                            <tr>
                                <td>
                                    <div class="match-details">
                                        <strong><?php echo htmlspecialchars($match['event_name']); ?></strong>
                                        <?php if ($match['match_number']): ?>
                                            <br><small>Match #<?php echo htmlspecialchars($match['match_number']); ?></small>
                                        <?php endif; ?>
                                        <br><small class="text-muted"><?php echo ucfirst($match['round_type']); ?></small>
                                    </div>
                                </td>
                                <td>
                                    <span class="sport-name"><?php echo htmlspecialchars($match['sport_name']); ?></span>
                                    <br><small class="category-badge category-<?php echo $match['sport_category']; ?>">
                                        <?php echo ucfirst(str_replace('_', ' ', $match['sport_category'])); ?>
                                    </small>
                                </td>
                                <td>
                                    <div class="datetime-info">
                                        <strong><?php echo formatDate($match['match_date']); ?></strong>
                                        <br><small><?php echo formatTime($match['match_time']); ?></small>
                                    </div>
                                </td>
                                <td>
                                    <?php if ($match['venue_name']): ?>
                                        <strong><?php echo htmlspecialchars($match['venue_name']); ?></strong>
                                        <?php if ($match['venue_location']): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($match['venue_location']); ?></small>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="text-muted">TBD</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="status-badge status-<?php echo $match['status']; ?>">
                                        <?php echo ucfirst($match['status']); ?>
                                    </span>
                                </td>
                                <td class="text-center">
                                    <span class="participant-count"><?php echo $match['participant_count']; ?></span>
                                    <?php if ($match['score_count'] > 0): ?>
                                        <br><small class="text-success">✓ Scored</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="view.php?id=<?php echo $match['match_id']; ?>" 
                                           class="btn btn-xs btn-outline" title="View Details">
                                            <i class="icon-eye"></i>
                                        </a>
                                        <a href="edit.php?id=<?php echo $match['match_id']; ?>" 
                                           class="btn btn-xs btn-primary" title="Edit Match">
                                            <i class="icon-edit"></i>
                                        </a>
                                        <?php if ($match['status'] === 'scheduled'): ?>
                                            <a href="../scores/record.php?match_id=<?php echo $match['match_id']; ?>" 
                                               class="btn btn-xs btn-success" title="Record Score">
                                                <i class="icon-edit"></i>
                                            </a>
                                        <?php endif; ?>
                                        <?php if ($match['score_count'] == 0): ?>
                                            <button class="btn btn-xs btn-danger" 
                                                    onclick="deleteMatch(<?php echo $match['match_id']; ?>, '<?php echo htmlspecialchars($match['sport_name']); ?>')"
                                                    title="Delete Match">
                                                <i class="icon-trash"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                
                <!-- Pagination -->
                <?php echo generatePaginationHTML($pagination, 'index.php'); ?>
                
            <?php else: ?>
                <div class="no-data">
                    <h3>No matches found</h3>
                    <p>Start by scheduling your first match.</p>
                    <a href="create.php" class="btn btn-primary">Schedule Match</a>
                </div>
            <?php endif; ?>
        </div>
    </main>
    
    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal">
        <div class="modal-overlay">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Confirm Delete</h3>
                    <button class="modal-close" onclick="closeModal('deleteModal')">&times;</button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this match for "<span id="matchSport"></span>"?</p>
                    <p class="text-warning">This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <form id="deleteForm" method="POST">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="match_id" id="deleteMatchId">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('deleteModal')">Cancel</button>
                        <button type="submit" class="btn btn-danger">Delete Match</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <script src="../../assets/js/admin.js"></script>
    <script>
        function deleteMatch(matchId, sportName) {
            document.getElementById('matchSport').textContent = sportName;
            document.getElementById('deleteMatchId').value = matchId;
            openModal('deleteModal');
        }
        
        function openModal(modalId) {
            document.getElementById(modalId).classList.add('show');
        }
        
        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('show');
        }
    </script>
</body>
</html>
