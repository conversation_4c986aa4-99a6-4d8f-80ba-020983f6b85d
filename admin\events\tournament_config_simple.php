<?php
/**
 * Simplified Tournament Configuration Page
 * Minimal version to test functionality without complex dependencies
 */

// Basic configuration
define('SCIMS_ACCESS', true);
define('APP_NAME', 'SCIMS');

// Simple error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Mock data for testing
$eventSportId = $_GET['event_sport_id'] ?? 1;
$eventId = $_GET['event_id'] ?? 1;

$eventSport = [
    'sport_name' => 'Basketball',
    'sport_category' => 'team',
    'event_name' => 'Intramural Championship 2024',
    'start_date' => '2024-01-15',
    'end_date' => '2024-01-30',
    'scoring_type' => 'points'
];

$participants = [
    ['dept_id' => 1, 'dept_name' => 'Computer Science', 'dept_abbrev' => 'CS', 'color_code' => '#3b82f6', 'seed_position' => 1],
    ['dept_id' => 2, 'dept_name' => 'Engineering', 'dept_abbrev' => 'ENG', 'color_code' => '#ef4444', 'seed_position' => 2],
    ['dept_id' => 3, 'dept_name' => 'Business', 'dept_abbrev' => 'BUS', 'color_code' => '#10b981', 'seed_position' => 3],
    ['dept_id' => 4, 'dept_name' => 'Education', 'dept_abbrev' => 'EDU', 'color_code' => '#f59e0b', 'seed_position' => 4],
    ['dept_id' => 5, 'dept_name' => 'Arts & Sciences', 'dept_abbrev' => 'AS', 'color_code' => '#8b5cf6', 'seed_position' => 5],
    ['dept_id' => 6, 'dept_name' => 'Medicine', 'dept_abbrev' => 'MED', 'color_code' => '#06b6d4', 'seed_position' => 6],
    ['dept_id' => 7, 'dept_name' => 'Law', 'dept_abbrev' => 'LAW', 'color_code' => '#84cc16', 'seed_position' => 7],
    ['dept_id' => 8, 'dept_name' => 'Agriculture', 'dept_abbrev' => 'AGR', 'color_code' => '#f97316', 'seed_position' => 8]
];

$tournamentFormats = [
    'single_elimination' => [
        'name' => 'Single Elimination',
        'icon' => '🏆',
        'description' => 'Traditional knockout format. Lose once, you\'re out.',
        'details' => ['Fast completion', 'High stakes', 'Best for: 8-64 teams']
    ],
    'double_elimination' => [
        'name' => 'Double Elimination',
        'icon' => '🔄',
        'description' => 'Two chances - winners and losers brackets.',
        'details' => ['More forgiving', 'Longer tournament', 'Best for: 8-32 teams']
    ],
    'round_robin' => [
        'name' => 'Round Robin',
        'icon' => '🔁',
        'description' => 'Every team plays every other team.',
        'details' => ['Most fair', 'Many matches', 'Best for: 4-12 teams']
    ]
];

$message = '';
$messageType = '';
$selectedFormat = '';
$bracketGenerated = false;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'save_format') {
        $selectedFormat = $_POST['tournament_format'] ?? '';
        $message = 'Tournament format saved: ' . ucfirst(str_replace('_', ' ', $selectedFormat));
        $messageType = 'success';
    } elseif ($action === 'generate_bracket') {
        $bracketGenerated = true;
        $message = 'Tournament bracket generated successfully!';
        $messageType = 'success';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tournament Configuration - <?php echo htmlspecialchars($eventSport['sport_name']); ?> | <?php echo APP_NAME; ?></title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f8fafc; }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        
        .header { background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); color: white; padding: 30px; border-radius: 12px; margin-bottom: 30px; }
        .header h1 { font-size: 2.5rem; margin-bottom: 10px; }
        .header p { font-size: 1.1rem; opacity: 0.9; }
        
        .wizard { display: flex; justify-content: center; margin-bottom: 30px; }
        .wizard-steps { display: flex; background: white; padding: 20px; border-radius: 50px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); gap: 20px; }
        .wizard-step { display: flex; align-items: center; gap: 10px; padding: 10px 20px; border-radius: 25px; }
        .wizard-step.active { background: #2563eb; color: white; }
        .step-number { width: 30px; height: 30px; border-radius: 50%; background: currentColor; color: white; display: flex; align-items: center; justify-content: center; font-weight: 600; }
        
        .alert { padding: 15px 20px; border-radius: 8px; margin-bottom: 20px; }
        .alert-success { background: #f0fdf4; color: #166534; border-left: 4px solid #10b981; }
        .alert-error { background: #fef2f2; color: #991b1b; border-left: 4px solid #ef4444; }
        
        .main-content { display: grid; grid-template-columns: 1fr 2fr; gap: 30px; }
        .panel { background: white; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); overflow: hidden; }
        .panel-header { background: #f8fafc; padding: 20px; border-bottom: 1px solid #e2e8f0; }
        .panel-header h3 { font-size: 1.3rem; margin-bottom: 5px; }
        .panel-content { padding: 25px; }
        
        .format-grid { display: grid; grid-template-columns: 1fr; gap: 15px; margin-bottom: 30px; }
        .format-card { border: 2px solid #e2e8f0; border-radius: 12px; padding: 20px; cursor: pointer; transition: all 0.2s; }
        .format-card:hover { border-color: #2563eb; transform: translateY(-2px); }
        .format-card.selected { border-color: #2563eb; background: #2563eb; color: white; }
        .format-header { display: flex; align-items: center; gap: 15px; margin-bottom: 15px; }
        .format-icon { font-size: 2rem; }
        .format-name { font-size: 1.2rem; font-weight: 600; }
        .format-description { margin-bottom: 15px; line-height: 1.5; }
        .format-details { display: flex; flex-wrap: wrap; gap: 8px; }
        .format-tag { background: #f1f5f9; color: #475569; padding: 4px 8px; border-radius: 12px; font-size: 0.8rem; }
        .format-card.selected .format-tag { background: rgba(255,255,255,0.2); color: white; }
        
        .participants-list { max-height: 400px; overflow-y: auto; }
        .participant-item { display: flex; align-items: center; justify-content: space-between; padding: 15px; border: 1px solid #e2e8f0; border-radius: 8px; margin-bottom: 10px; background: white; }
        .participant-info { display: flex; align-items: center; gap: 15px; }
        .seed-number { width: 35px; height: 35px; border-radius: 50%; background: #2563eb; color: white; display: flex; align-items: center; justify-content: center; font-weight: 600; }
        .dept-badge { padding: 6px 12px; border-radius: 15px; font-size: 0.8rem; font-weight: 600; color: white; }
        .participant-name { font-weight: 600; }
        
        .bracket-container { min-height: 400px; display: flex; align-items: center; justify-content: center; }
        .bracket-placeholder { text-align: center; color: #64748b; }
        .bracket-placeholder-icon { font-size: 4rem; margin-bottom: 20px; opacity: 0.5; }
        
        .btn { padding: 12px 24px; border: none; border-radius: 8px; font-weight: 600; cursor: pointer; transition: all 0.2s; text-decoration: none; display: inline-block; }
        .btn-primary { background: #2563eb; color: white; }
        .btn-primary:hover { background: #1d4ed8; }
        .btn-secondary { background: #6b7280; color: white; }
        .btn-secondary:hover { background: #4b5563; }
        
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 8px; font-weight: 600; }
        .form-control { width: 100%; padding: 10px 12px; border: 1px solid #d1d5db; border-radius: 6px; }
        
        .bracket-actions { display: flex; gap: 15px; padding: 20px; border-top: 1px solid #e2e8f0; background: #f8fafc; }
        
        @media (max-width: 1024px) {
            .main-content { grid-template-columns: 1fr; }
            .wizard-steps { flex-direction: column; gap: 10px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🏆 Tournament Configuration</h1>
            <p>Configure brackets for <strong><?php echo htmlspecialchars($eventSport['sport_name']); ?></strong> in <?php echo htmlspecialchars($eventSport['event_name']); ?></p>
        </div>
        
        <!-- Wizard Navigation -->
        <div class="wizard">
            <div class="wizard-steps">
                <div class="wizard-step">
                    <div class="step-number">1</div>
                    <div>Event Setup</div>
                </div>
                <div class="wizard-step active">
                    <div class="step-number">2</div>
                    <div>Bracket Setup</div>
                </div>
                <div class="wizard-step">
                    <div class="step-number">3</div>
                    <div>Scheduling</div>
                </div>
                <div class="wizard-step">
                    <div class="step-number">4</div>
                    <div>Configuration</div>
                </div>
            </div>
        </div>
        
        <!-- Alert Messages -->
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <!-- Main Content -->
        <div class="main-content">
            <!-- Left Panel: Configuration -->
            <div class="panel">
                <div class="panel-header">
                    <h3>🎯 Tournament Configuration</h3>
                    <p>Select format and manage participants</p>
                </div>
                <div class="panel-content">
                    <!-- Format Selection -->
                    <h4>Tournament Format</h4>
                    <form method="POST" id="formatForm">
                        <input type="hidden" name="action" value="save_format">
                        <input type="hidden" name="tournament_format" id="selected_format" value="<?php echo htmlspecialchars($selectedFormat); ?>">
                        
                        <div class="format-grid">
                            <?php foreach ($tournamentFormats as $formatKey => $format): ?>
                                <div class="format-card <?php echo ($selectedFormat === $formatKey) ? 'selected' : ''; ?>" 
                                     onclick="selectFormat('<?php echo $formatKey; ?>')">
                                    <div class="format-header">
                                        <div class="format-icon"><?php echo $format['icon']; ?></div>
                                        <div class="format-name"><?php echo htmlspecialchars($format['name']); ?></div>
                                    </div>
                                    <div class="format-description">
                                        <?php echo htmlspecialchars($format['description']); ?>
                                    </div>
                                    <div class="format-details">
                                        <?php foreach ($format['details'] as $detail): ?>
                                            <span class="format-tag"><?php echo htmlspecialchars($detail); ?></span>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">Save Configuration</button>
                    </form>
                    
                    <!-- Participants -->
                    <h4 style="margin-top: 30px;">Participants (<?php echo count($participants); ?>)</h4>
                    <div class="participants-list">
                        <?php foreach ($participants as $participant): ?>
                            <div class="participant-item">
                                <div class="participant-info">
                                    <div class="seed-number"><?php echo $participant['seed_position']; ?></div>
                                    <div class="dept-badge" style="background-color: <?php echo htmlspecialchars($participant['color_code']); ?>">
                                        <?php echo htmlspecialchars($participant['dept_abbrev']); ?>
                                    </div>
                                    <div class="participant-name">
                                        <?php echo htmlspecialchars($participant['dept_name']); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            
            <!-- Right Panel: Bracket Visualization -->
            <div class="panel">
                <div class="panel-header">
                    <h3>🏆 Tournament Bracket</h3>
                    <p>Visual bracket representation</p>
                </div>
                <div class="panel-content">
                    <div class="bracket-container">
                        <?php if ($bracketGenerated && $selectedFormat): ?>
                            <div style="text-align: center;">
                                <h4><?php echo ucfirst(str_replace('_', ' ', $selectedFormat)); ?> Bracket</h4>
                                <p>Bracket visualization for <?php echo count($participants); ?> participants</p>
                                <div style="margin-top: 20px; padding: 20px; background: #f8fafc; border-radius: 8px;">
                                    <strong>Tournament Structure Generated!</strong><br>
                                    Format: <?php echo ucfirst(str_replace('_', ' ', $selectedFormat)); ?><br>
                                    Participants: <?php echo count($participants); ?><br>
                                    Status: Ready for scheduling
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="bracket-placeholder">
                                <div class="bracket-placeholder-icon">🏆</div>
                                <h4>Bracket Not Generated</h4>
                                <p>Select a tournament format and generate the bracket to see the visual representation.</p>
                                <?php if ($selectedFormat): ?>
                                    <form method="POST" style="margin-top: 20px;">
                                        <input type="hidden" name="action" value="generate_bracket">
                                        <button type="submit" class="btn btn-primary">Generate Bracket</button>
                                    </form>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="bracket-actions">
                    <button type="button" class="btn btn-secondary" onclick="alert('Full view feature')">Full View</button>
                    <button type="button" class="btn btn-secondary" onclick="alert('Export feature')">Export</button>
                    <?php if ($selectedFormat): ?>
                        <form method="POST" style="display: inline;">
                            <input type="hidden" name="action" value="generate_bracket">
                            <button type="submit" class="btn btn-primary">Regenerate</button>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let selectedFormat = '<?php echo $selectedFormat; ?>';
        
        function selectFormat(formatKey) {
            // Remove previous selection
            document.querySelectorAll('.format-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // Add selection to clicked card
            event.currentTarget.classList.add('selected');
            selectedFormat = formatKey;
            
            // Update hidden input
            document.getElementById('selected_format').value = formatKey;
            
            console.log('Selected format:', formatKey);
        }
        
        // Form validation
        document.getElementById('formatForm').addEventListener('submit', function(e) {
            if (!selectedFormat) {
                e.preventDefault();
                alert('Please select a tournament format first!');
            }
        });
        
        console.log('Tournament Configuration Page Loaded Successfully');
        console.log('Current format:', selectedFormat);
    </script>
</body>
</html>
