<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Setup Default Settings
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

declare(strict_types=1);

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication and super admin access
requireAuth();

if (getCurrentUser()['role'] !== 'super_admin') {
    die('Access denied');
}

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Setup Default Settings - SCIMS</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .success { color: #059669; }
        .error { color: #dc2626; }
        .info { color: #2563eb; }
        .setting { margin: 10px 0; padding: 10px; background: #f9f9f9; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🔧 SCIMS Default Settings Setup</h1>
    <p>This script will add default settings to the system_settings table.</p>
";

try {
    // Default settings to add
    $defaultSettings = [
        // General Settings
        'site_description' => [
            'value' => 'Comprehensive intramurals management system for Samar College',
            'description' => 'Website description for meta tags'
        ],
        'contact_email' => [
            'value' => '<EMAIL>',
            'description' => 'Primary contact email address'
        ],
        'contact_phone' => [
            'value' => '+63 (*************',
            'description' => 'Primary contact phone number'
        ],
        'timezone' => [
            'value' => 'Asia/Manila',
            'description' => 'System timezone setting'
        ],
        'date_format' => [
            'value' => 'Y-m-d',
            'description' => 'Default date display format'
        ],
        'time_format' => [
            'value' => 'H:i',
            'description' => 'Default time display format'
        ],
        
        // Security Settings
        'lockout_duration' => [
            'value' => '15',
            'description' => 'Account lockout duration in minutes'
        ],
        'password_min_length' => [
            'value' => '8',
            'description' => 'Minimum password length requirement'
        ],
        'require_password_complexity' => [
            'value' => '1',
            'description' => 'Require complex passwords (1=yes, 0=no)'
        ],
        'enable_two_factor' => [
            'value' => '0',
            'description' => 'Enable two-factor authentication (1=yes, 0=no)'
        ],
        
        // Event Settings
        'default_point_system' => [
            'value' => '{"1st": 15, "2nd": 12, "3rd": 10, "4th": 8, "5th": 6, "participation": 3}',
            'description' => 'Default point distribution for events'
        ],
        'allow_score_updates' => [
            'value' => '1',
            'description' => 'Allow score updates after finalization (1=yes, 0=no)'
        ],
        'auto_advance_rounds' => [
            'value' => '0',
            'description' => 'Automatically advance tournament rounds (1=yes, 0=no)'
        ],
        'require_score_approval' => [
            'value' => '0',
            'description' => 'Require approval for score entries (1=yes, 0=no)'
        ],
        
        // Notification Settings
        'email_notifications' => [
            'value' => '0',
            'description' => 'Enable email notifications (1=yes, 0=no)'
        ],
        'smtp_host' => [
            'value' => '',
            'description' => 'SMTP server hostname'
        ],
        'smtp_port' => [
            'value' => '587',
            'description' => 'SMTP server port'
        ],
        'smtp_username' => [
            'value' => '',
            'description' => 'SMTP authentication username'
        ],
        'smtp_password' => [
            'value' => '',
            'description' => 'SMTP authentication password'
        ],
        'smtp_encryption' => [
            'value' => 'tls',
            'description' => 'SMTP encryption method (tls/ssl)'
        ],
        'notification_from_email' => [
            'value' => '<EMAIL>',
            'description' => 'From email address for notifications'
        ],
        'notification_from_name' => [
            'value' => 'SCIMS Notifications',
            'description' => 'From name for notifications'
        ],
        
        // System Maintenance
        'last_cache_clear' => [
            'value' => date('Y-m-d H:i:s'),
            'description' => 'Last time system cache was cleared'
        ],
        'maintenance_mode' => [
            'value' => '0',
            'description' => 'Enable maintenance mode (1=yes, 0=no)'
        ],
        'maintenance_message' => [
            'value' => 'System is currently under maintenance. Please try again later.',
            'description' => 'Message displayed during maintenance mode'
        ]
    ];
    
    $added = 0;
    $updated = 0;
    $skipped = 0;
    
    foreach ($defaultSettings as $key => $setting) {
        // Check if setting already exists
        $existing = getSetting($key);
        
        if ($existing === null) {
            // Setting doesn't exist, add it
            if (setSetting($key, $setting['value'], $setting['description'])) {
                echo "<div class='setting success'>✅ Added: <strong>{$key}</strong> = {$setting['value']}</div>";
                $added++;
            } else {
                echo "<div class='setting error'>❌ Failed to add: <strong>{$key}</strong></div>";
            }
        } else {
            // Setting exists, check if we should update description
            $sql = "UPDATE system_settings SET description = :description WHERE setting_key = :key AND (description IS NULL OR description = '')";
            $result = executeQuery($sql, [
                'key' => $key,
                'description' => $setting['description']
            ]);
            
            if ($result) {
                echo "<div class='setting info'>ℹ️ Updated description for: <strong>{$key}</strong> (value unchanged: {$existing})</div>";
                $updated++;
            } else {
                echo "<div class='setting'>⏭️ Skipped: <strong>{$key}</strong> (already exists: {$existing})</div>";
                $skipped++;
            }
        }
    }
    
    echo "<hr>";
    echo "<h2>📊 Summary</h2>";
    echo "<div class='success'>✅ Added: {$added} new settings</div>";
    echo "<div class='info'>ℹ️ Updated: {$updated} descriptions</div>";
    echo "<div>⏭️ Skipped: {$skipped} existing settings</div>";
    echo "<div><strong>Total processed: " . count($defaultSettings) . " settings</strong></div>";
    
    echo "<hr>";
    echo "<h2>🔗 Next Steps</h2>";
    echo "<p><a href='index.php'>← Go to Settings Management</a></p>";
    echo "<p><a href='../dashboard.php'>← Return to Dashboard</a></p>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</div>";
    echo "<p>Please check your database connection and try again.</p>";
}

echo "</body></html>";
?>
