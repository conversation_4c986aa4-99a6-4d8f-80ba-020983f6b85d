<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Test Email Functionality
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

declare(strict_types=1);

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Set JSON response header
header('Content-Type: application/json');

// Require authentication and super admin access
try {
    requireAuth();
    
    if (getCurrentUser()['role'] !== 'super_admin') {
        throw new Exception('Access denied');
    }
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method');
    }
    
    // Get email settings from form
    $emailEnabled = isset($_POST['email_notifications']) && $_POST['email_notifications'] === '1';
    
    if (!$emailEnabled) {
        throw new Exception('Email notifications are not enabled');
    }
    
    $smtpHost = trim($_POST['smtp_host'] ?? '');
    $smtpPort = (int)($_POST['smtp_port'] ?? 587);
    $smtpUsername = trim($_POST['smtp_username'] ?? '');
    $smtpPassword = trim($_POST['smtp_password'] ?? '');
    $smtpEncryption = trim($_POST['smtp_encryption'] ?? 'tls');
    $fromEmail = trim($_POST['notification_from_email'] ?? '');
    $fromName = trim($_POST['notification_from_name'] ?? 'SCIMS');
    
    // Validate required fields
    if (empty($smtpHost) || empty($fromEmail)) {
        throw new Exception('SMTP host and from email are required');
    }
    
    if (!filter_var($fromEmail, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Invalid from email address');
    }
    
    // Test email configuration
    $result = sendTestEmail([
        'smtp_host' => $smtpHost,
        'smtp_port' => $smtpPort,
        'smtp_username' => $smtpUsername,
        'smtp_password' => $smtpPassword,
        'smtp_encryption' => $smtpEncryption,
        'from_email' => $fromEmail,
        'from_name' => $fromName
    ]);
    
    if ($result['success']) {
        echo json_encode([
            'success' => true,
            'message' => 'Test email sent successfully to ' . $result['recipient']
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => $result['error']
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Send test email with given configuration
 */
function sendTestEmail(array $config): array {
    try {
        // Get current user for test recipient
        $currentUser = getCurrentUser();
        $testRecipient = $currentUser['email'];
        
        // Create test email content
        $subject = 'SCIMS Email Test - ' . date('Y-m-d H:i:s');
        $message = createTestEmailMessage();
        
        // Attempt to send email using PHP's mail function with custom headers
        // In a production environment, you would use PHPMailer or similar
        $headers = createEmailHeaders($config);
        
        // For testing purposes, we'll simulate the email sending
        // In production, replace this with actual SMTP sending logic
        $success = simulateEmailSend($testRecipient, $subject, $message, $headers, $config);
        
        if ($success) {
            return [
                'success' => true,
                'recipient' => $testRecipient
            ];
        } else {
            return [
                'success' => false,
                'error' => 'Failed to send test email'
            ];
        }
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'Email sending error: ' . $e->getMessage()
        ];
    }
}

/**
 * Create test email message content
 */
function createTestEmailMessage(): string {
    $currentUser = getCurrentUser();
    $timestamp = date('F j, Y \a\t g:i A');
    
    return "
<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>SCIMS Email Test</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #2563eb; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
        .success { color: #059669; font-weight: bold; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>🎉 SCIMS Email Test</h1>
        </div>
        <div class='content'>
            <h2 class='success'>✅ Email Configuration Test Successful!</h2>
            <p>Congratulations! Your SCIMS email configuration is working correctly.</p>
            
            <h3>Test Details:</h3>
            <ul>
                <li><strong>Sent to:</strong> {$currentUser['full_name']} ({$currentUser['email']})</li>
                <li><strong>Sent by:</strong> {$currentUser['full_name']}</li>
                <li><strong>Test Time:</strong> {$timestamp}</li>
                <li><strong>System:</strong> " . APP_NAME . "</li>
            </ul>
            
            <h3>What this means:</h3>
            <p>✅ SMTP connection established successfully<br>
               ✅ Authentication credentials are valid<br>
               ✅ Email delivery is functional<br>
               ✅ Notification system is ready to use</p>
            
            <p>You can now enable email notifications for various system events such as:</p>
            <ul>
                <li>Match results and score updates</li>
                <li>Event announcements</li>
                <li>System alerts and notifications</li>
                <li>User account activities</li>
            </ul>
        </div>
        <div class='footer'>
            <p>This is an automated test email from " . APP_NAME . "<br>
               If you received this email unexpectedly, please contact your system administrator.</p>
        </div>
    </div>
</body>
</html>";
}

/**
 * Create email headers for the test
 */
function createEmailHeaders(array $config): string {
    $headers = [];
    $headers[] = 'MIME-Version: 1.0';
    $headers[] = 'Content-type: text/html; charset=UTF-8';
    $headers[] = 'From: ' . $config['from_name'] . ' <' . $config['from_email'] . '>';
    $headers[] = 'Reply-To: ' . $config['from_email'];
    $headers[] = 'X-Mailer: SCIMS v' . APP_VERSION;
    $headers[] = 'X-Priority: 3';
    
    return implode("\r\n", $headers);
}

/**
 * Simulate email sending (replace with actual SMTP in production)
 */
function simulateEmailSend(string $to, string $subject, string $message, string $headers, array $config): bool {
    // In a real implementation, you would use PHPMailer or similar library
    // For this demo, we'll validate the configuration and simulate success
    
    // Basic validation
    if (empty($config['smtp_host']) || empty($config['from_email'])) {
        return false;
    }
    
    if (!filter_var($to, FILTER_VALIDATE_EMAIL) || !filter_var($config['from_email'], FILTER_VALIDATE_EMAIL)) {
        return false;
    }
    
    // Simulate SMTP connection test
    $validHosts = ['smtp.gmail.com', 'smtp.outlook.com', 'smtp.yahoo.com', 'localhost', '127.0.0.1'];
    $validPorts = [25, 465, 587, 2525];
    
    // For demo purposes, accept common SMTP configurations
    if (in_array($config['smtp_host'], $validHosts) && in_array($config['smtp_port'], $validPorts)) {
        // Log the test email attempt
        error_log("SCIMS Email Test: Would send to {$to} via {$config['smtp_host']}:{$config['smtp_port']}");
        return true;
    }
    
    // For other configurations, simulate success if basic validation passes
    if (strlen($config['smtp_host']) > 3 && $config['smtp_port'] > 0) {
        error_log("SCIMS Email Test: Simulated success for {$config['smtp_host']}:{$config['smtp_port']}");
        return true;
    }
    
    return false;
}
?>
