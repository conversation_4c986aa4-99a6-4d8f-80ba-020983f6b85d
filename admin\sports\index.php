<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Sports Management - Main Page
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication
requireAuth();

// Handle actions
$action = $_POST['action'] ?? $_GET['action'] ?? '';
$message = $_GET['message'] ?? '';
$messageType = $_GET['type'] ?? 'info';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('Invalid security token');
        }

        switch ($action) {
            case 'create':
                $name = sanitizeInput($_POST['name'] ?? '');
                $category = sanitizeInput($_POST['category'] ?? '');
                $scoring_type = sanitizeInput($_POST['scoring_type'] ?? '');
                $description = sanitizeInput($_POST['description'] ?? '');
                $rules = sanitizeInput($_POST['rules'] ?? '');
                $status = sanitizeInput($_POST['status'] ?? 'active');

                // Validation
                if (empty($name)) {
                    throw new Exception('Sport name is required');
                }

                if (!in_array($category, ['individual', 'team', 'performing_arts', 'academic', 'pageant'])) {
                    throw new Exception('Invalid category selected');
                }

                if (!in_array($scoring_type, ['points', 'time', 'distance', 'subjective'])) {
                    throw new Exception('Invalid scoring type selected');
                }

                // Check if sport name already exists
                $existing = fetchOne("SELECT sport_id FROM sports WHERE name = ?", [$name]);
                if ($existing) {
                    throw new Exception('A sport with this name already exists');
                }

                // Insert sport
                $sportData = [
                    'name' => $name,
                    'category' => $category,
                    'scoring_type' => $scoring_type,
                    'description' => $description,
                    'rules' => $rules,
                    'status' => $status
                ];

                $sportId = insertRecord('sports', $sportData);
                logActivity('sport_created', "Sport '{$name}' created with ID {$sportId}");

                $message = 'Sport created successfully';
                $messageType = 'success';
                break;

            case 'delete':
                $sportId = (int)($_POST['sport_id'] ?? 0);
                if ($sportId) {
                    // Check if sport has matches
                    $matchCount = fetchOne("SELECT COUNT(*) as count FROM matches WHERE sport_id = ?", [$sportId])['count'];
                    if ($matchCount > 0) {
                        throw new Exception('Cannot delete sport with existing matches');
                    }
                    
                    deleteRecord('sports', 'sport_id = :sport_id', ['sport_id' => $sportId]);
                    logActivity('sport_deleted', "Sport ID {$sportId} deleted");
                    $message = 'Sport deleted successfully';
                    $messageType = 'success';
                }
                break;
                
            case 'toggle_status':
                $sportId = (int)($_POST['sport_id'] ?? 0);
                $newStatus = sanitizeInput($_POST['new_status'] ?? '');
                
                if ($sportId && in_array($newStatus, ['active', 'inactive'])) {
                    updateRecord('sports', ['status' => $newStatus], 'sport_id = :sport_id', ['sport_id' => $sportId]);
                    logActivity('sport_status_changed', "Sport ID {$sportId} status changed to {$newStatus}");
                    $message = 'Sport status updated successfully';
                    $messageType = 'success';
                }
                break;
                
            case 'bulk_action':
                $selectedSports = $_POST['selected_sports'] ?? [];
                $bulkAction = $_POST['bulk_action'] ?? '';
                
                if (!empty($selectedSports) && $bulkAction) {
                    $count = 0;
                    foreach ($selectedSports as $sportId) {
                        $sportId = (int)$sportId;
                        if ($sportId) {
                            switch ($bulkAction) {
                                case 'activate':
                                    updateRecord('sports', ['status' => 'active'], 'sport_id = :sport_id', ['sport_id' => $sportId]);
                                    $count++;
                                    break;
                                case 'deactivate':
                                    updateRecord('sports', ['status' => 'inactive'], 'sport_id = :sport_id', ['sport_id' => $sportId]);
                                    $count++;
                                    break;
                            }
                        }
                    }
                    
                    if ($count > 0) {
                        logActivity('sport_bulk_action', "Bulk action {$bulkAction} applied to {$count} sports");
                        $message = "Bulk action applied to {$count} sports";
                        $messageType = 'success';
                    }
                }
                break;
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get sports with pagination
$page = (int)($_GET['page'] ?? 1);
$search = sanitizeInput($_GET['search'] ?? '');
$categoryFilter = sanitizeInput($_GET['category'] ?? '');
$statusFilter = sanitizeInput($_GET['status'] ?? '');
$sortOption = sanitizeInput($_GET['sort'] ?? 'name_asc');

$whereConditions = [];
$params = [];

if ($search) {
    $whereConditions[] = "(s.name LIKE ? OR s.description LIKE ?)";
    $params[] = "%{$search}%";
    $params[] = "%{$search}%";
}

if ($categoryFilter) {
    $whereConditions[] = "s.category = ?";
    $params[] = $categoryFilter;
}

if ($statusFilter) {
    $whereConditions[] = "s.status = ?";
    $params[] = $statusFilter;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// Determine sort order
$orderClause = 'ORDER BY ';
switch ($sortOption) {
    case 'name_desc':
        $orderClause .= 's.name DESC';
        break;
    case 'category_asc':
        $orderClause .= 's.category ASC, s.name ASC';
        break;
    case 'newest':
        $orderClause .= 's.created_at DESC';
        break;
    case 'most_used':
        $orderClause .= 'event_count DESC, match_count DESC, s.name ASC';
        break;
    case 'name_asc':
    default:
        $orderClause .= 's.name ASC';
        break;
}

// Count total records
$totalRecords = fetchOne("SELECT COUNT(*) as count FROM sports s {$whereClause}", $params)['count'];
$pagination = paginate($totalRecords, $page);

// Get sports
$sports = fetchAll("
    SELECT s.*,
           (SELECT COUNT(*) FROM matches WHERE sport_id = s.sport_id) as match_count,
           (SELECT COUNT(*) FROM event_sports WHERE sport_id = s.sport_id) as event_count
    FROM sports s
    {$whereClause}
    {$orderClause}
    LIMIT {$pagination['records_per_page']} OFFSET {$pagination['offset']}
", $params);

$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sports Management - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>
    
    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1>Sports Management</h1>
                <nav class="breadcrumb">
                    <a href="../dashboard.php">Dashboard</a>
                    <span>/</span>
                    <span>Sports</span>
                </nav>
            </div>
            <div class="page-actions">
                <button class="btn btn-primary" onclick="openModal('addSportModal')">
                    <i class="icon-plus"></i>
                    Add Sport
                </button>
            </div>
        </div>
        
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <!-- Enhanced Filters and Search -->
        <div class="filters-container">
            <form method="GET" class="filters-form" id="filtersForm">
                <div class="filter-row">
                    <div class="filter-group search-group">
                        <div class="search-input-wrapper">
                            <i class="icon-search"></i>
                            <input type="text" name="search" placeholder="Search sports by name or description..."
                                   value="<?php echo htmlspecialchars($search); ?>" class="form-control search-input"
                                   id="searchInput">
                            <?php if ($search): ?>
                                <button type="button" class="clear-search" onclick="clearSearch()">
                                    <i class="icon-cancel"></i>
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="filter-group">
                        <select name="category" class="form-control" onchange="this.form.submit()">
                            <option value="">All Categories</option>
                            <option value="team" <?php echo $categoryFilter === 'team' ? 'selected' : ''; ?>>Team Sports</option>
                            <option value="individual" <?php echo $categoryFilter === 'individual' ? 'selected' : ''; ?>>Individual Sports</option>
                            <option value="performing_arts" <?php echo $categoryFilter === 'performing_arts' ? 'selected' : ''; ?>>Performing Arts</option>
                            <option value="academic" <?php echo $categoryFilter === 'academic' ? 'selected' : ''; ?>>Academic</option>
                            <option value="pageant" <?php echo $categoryFilter === 'pageant' ? 'selected' : ''; ?>>Pageant</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <select name="status" class="form-control" onchange="this.form.submit()">
                            <option value="">All Status</option>
                            <option value="active" <?php echo $statusFilter === 'active' ? 'selected' : ''; ?>>Active</option>
                            <option value="inactive" <?php echo $statusFilter === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <select name="sort" class="form-control" onchange="this.form.submit()">
                            <option value="name_asc" <?php echo ($_GET['sort'] ?? 'name_asc') === 'name_asc' ? 'selected' : ''; ?>>Name A-Z</option>
                            <option value="name_desc" <?php echo ($_GET['sort'] ?? '') === 'name_desc' ? 'selected' : ''; ?>>Name Z-A</option>
                            <option value="category_asc" <?php echo ($_GET['sort'] ?? '') === 'category_asc' ? 'selected' : ''; ?>>Category A-Z</option>
                            <option value="newest" <?php echo ($_GET['sort'] ?? '') === 'newest' ? 'selected' : ''; ?>>Newest First</option>
                            <option value="most_used" <?php echo ($_GET['sort'] ?? '') === 'most_used' ? 'selected' : ''; ?>>Most Used</option>
                        </select>
                    </div>

                    <div class="filter-actions">
                        <button type="submit" class="btn btn-secondary">
                            <i class="icon-search"></i>
                            Filter
                        </button>
                        <a href="index.php" class="btn btn-outline">
                            <i class="icon-refresh"></i>
                            Clear
                        </a>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- Bulk Actions -->
        <div class="bulk-actions" style="display: none;" id="bulkActions">
            <form method="POST" id="bulkForm">
                <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                <input type="hidden" name="action" value="bulk_action">
                
                <div class="bulk-controls">
                    <span class="selected-count">0 selected</span>
                    <select name="bulk_action" class="form-control">
                        <option value="">Choose action...</option>
                        <option value="activate">Activate</option>
                        <option value="deactivate">Deactivate</option>
                    </select>
                    <button type="submit" class="btn btn-primary">Apply</button>
                    <button type="button" class="btn btn-outline" onclick="clearSelection()">Clear</button>
                </div>
            </form>
        </div>
        
        <!-- Sports Grid -->
        <div class="sports-container">
            <?php if (!empty($sports)): ?>
                <div class="sports-grid">
                    <?php foreach ($sports as $sport): ?>
                        <div class="sport-card">
                            <div class="sport-header">
                                <label class="checkbox-container">
                                    <input type="checkbox" name="selected_sports[]" 
                                           value="<?php echo $sport['sport_id']; ?>" class="row-checkbox">
                                    <span class="checkmark"></span>
                                </label>
                                
                                <div class="sport-category">
                                    <span class="category-badge category-<?php echo $sport['category']; ?> tooltip-trigger"
                                          data-tooltip="<?php
                                              switch($sport['category']) {
                                                  case 'team': echo 'Sports requiring multiple participants working together'; break;
                                                  case 'individual': echo 'Sports with single participants competing independently'; break;
                                                  case 'performing_arts': echo 'Creative competitions like dance, music, and drama'; break;
                                                  case 'academic': echo 'Knowledge-based competitions like quiz bowl and debate'; break;
                                                  default: echo 'Sport category';
                                              }
                                          ?>">
                                        <?php echo ucfirst(str_replace('_', ' ', $sport['category'])); ?>
                                        <i class="tooltip-icon icon-info"></i>
                                    </span>
                                </div>

                                <div class="sport-status">
                                    <span class="status-badge status-<?php echo $sport['status']; ?> tooltip-trigger"
                                          data-tooltip="<?php echo $sport['status'] === 'active' ? 'Sport is available for event registration' : 'Sport is temporarily disabled and unavailable'; ?>">
                                        <?php echo ucfirst($sport['status']); ?>
                                        <i class="tooltip-icon icon-info"></i>
                                    </span>
                                </div>
                            </div>
                            
                            <div class="sport-content">
                                <h3><?php echo htmlspecialchars($sport['name']); ?></h3>
                                <?php if ($sport['description']): ?>
                                    <p class="sport-description"><?php echo htmlspecialchars(substr($sport['description'], 0, 100)); ?>...</p>
                                <?php endif; ?>
                                
                                <div class="sport-details">
                                    <div class="detail-item">
                                        <span class="detail-label">Scoring:</span>
                                        <span class="detail-value tooltip-trigger"
                                              data-tooltip="<?php
                                                  switch($sport['scoring_type']) {
                                                      case 'points': echo 'Scoring based on accumulated points (e.g., basketball scores, quiz points)'; break;
                                                      case 'time': echo 'Scoring based on completion time (e.g., track and field, swimming)'; break;
                                                      case 'distance': echo 'Scoring based on distance achieved (e.g., shot put, long jump)'; break;
                                                      case 'subjective': echo 'Scoring based on judges\' evaluation (e.g., dance, debate)'; break;
                                                      default: echo 'Scoring method for this sport';
                                                  }
                                              ?>">
                                            <?php echo ucfirst($sport['scoring_type']); ?>
                                            <i class="tooltip-icon icon-info"></i>
                                        </span>
                                    </div>
                                    <div class="detail-item">
                                        <span class="detail-label">Category:</span>
                                        <span class="detail-value tooltip-trigger"
                                              data-tooltip="<?php
                                                  switch($sport['category']) {
                                                      case 'team': echo 'Sports requiring multiple participants working together'; break;
                                                      case 'individual': echo 'Sports with single participants competing independently'; break;
                                                      case 'performing_arts': echo 'Creative competitions like dance, music, and drama'; break;
                                                      case 'academic': echo 'Knowledge-based competitions like quiz bowl and debate'; break;
                                                      default: echo 'Sport category';
                                                  }
                                              ?>">
                                            <?php echo ucfirst(str_replace('_', ' ', $sport['category'])); ?>
                                            <i class="tooltip-icon icon-info"></i>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="sport-stats">
                                    <div class="stat-item">
                                        <span class="stat-value"><?php echo $sport['match_count']; ?></span>
                                        <span class="stat-label">Matches</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-value"><?php echo $sport['event_count']; ?></span>
                                        <span class="stat-label">Events</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="sport-actions">
                                <button class="btn btn-sm btn-primary"
                                        onclick="editSport(<?php echo $sport['sport_id']; ?>)"
                                        title="Edit Sport">
                                    <i class="icon-edit"></i>
                                    Edit
                                </button>

                                <!-- Status Toggle -->
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                    <input type="hidden" name="action" value="toggle_status">
                                    <input type="hidden" name="sport_id" value="<?php echo $sport['sport_id']; ?>">
                                    <input type="hidden" name="new_status" value="<?php echo $sport['status'] === 'active' ? 'inactive' : 'active'; ?>">
                                    <button type="submit" class="btn btn-sm btn-secondary"
                                            title="<?php echo $sport['status'] === 'active' ? 'Deactivate' : 'Activate'; ?>">
                                        <i class="icon-<?php echo $sport['status'] === 'active' ? 'pause' : 'play'; ?>"></i>
                                        <?php echo $sport['status'] === 'active' ? 'Deactivate' : 'Activate'; ?>
                                    </button>
                                </form>

                                <?php if ($sport['match_count'] == 0): ?>
                                    <button class="btn btn-sm btn-danger"
                                            onclick="deleteSport(<?php echo $sport['sport_id']; ?>, '<?php echo htmlspecialchars($sport['name']); ?>')"
                                            title="Delete Sport">
                                        <i class="icon-trash"></i>
                                        Delete
                                    </button>
                                <?php else: ?>
                                    <span class="btn btn-sm btn-disabled" title="Cannot delete sport with existing matches">
                                        <i class="icon-lock"></i>
                                        Protected
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Pagination -->
                <?php echo generatePaginationHTML($pagination, 'index.php'); ?>
                
            <?php else: ?>
                <div class="no-data">
                    <h3>No sports found</h3>
                    <p>Start by adding your first sport.</p>
                    <a href="create.php" class="btn btn-primary">Add Sport</a>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Bulk Selection Controls -->
        <?php if (!empty($sports)): ?>
            <div class="bulk-selection-controls">
                <label class="checkbox-container">
                    <input type="checkbox" id="selectAll">
                    <span class="checkmark"></span>
                    Select All
                </label>
            </div>
        <?php endif; ?>
    </main>
    
    <!-- Add Sport Modal -->
    <div id="addSportModal" class="modal">
        <div class="modal-overlay">
            <div class="modal-content modal-lg">
                <div class="modal-header">
                    <h3><i class="icon-plus"></i> Add New Sport</h3>
                    <button class="modal-close" onclick="closeModal('addSportModal')">&times;</button>
                </div>
                <form method="POST" id="addSportForm">
                    <div class="modal-body">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                        <input type="hidden" name="action" value="create">

                        <div id="addSportErrors"></div>

                        <div class="form-section">
                            <h4>Basic Information</h4>

                            <div class="form-group">
                                <label for="add_name" class="form-label required">Sport Name</label>
                                <input type="text" id="add_name" name="name" class="form-control"
                                       placeholder="Enter sport name" required>
                                <small class="form-help">Enter the name of the sport</small>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="add_category" class="form-label required">Category</label>
                                    <div class="custom-select" data-name="category" data-required="true">
                                        <div class="select-trigger" tabindex="0" role="combobox" aria-expanded="false" aria-haspopup="listbox">
                                            <span class="select-value">Select Category</span>
                                            <i class="select-arrow icon-chevron-down"></i>
                                        </div>
                                        <div class="select-options" role="listbox">
                                            <div class="select-option" data-value="" role="option">Select Category</div>
                                            <div class="select-option tooltip-trigger" data-value="individual" role="option"
                                                 data-tooltip="Sports with single participants competing independently">
                                                Individual Sports
                                            </div>
                                            <div class="select-option tooltip-trigger" data-value="team" role="option"
                                                 data-tooltip="Sports requiring multiple participants working together">
                                                Team Sports
                                            </div>
                                            <div class="select-option tooltip-trigger" data-value="performing_arts" role="option"
                                                 data-tooltip="Creative competitions like dance, music, and drama">
                                                Performing Arts
                                            </div>
                                            <div class="select-option tooltip-trigger" data-value="academic" role="option"
                                                 data-tooltip="Knowledge-based competitions like quiz bowl and debate">
                                                Academic
                                            </div>
                                        </div>
                                        <input type="hidden" name="category" id="add_category" required>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="add_scoring_type" class="form-label required">Scoring Type</label>
                                    <div class="custom-select" data-name="scoring_type" data-required="true">
                                        <div class="select-trigger" tabindex="0" role="combobox" aria-expanded="false" aria-haspopup="listbox">
                                            <span class="select-value">Select Scoring Type</span>
                                            <i class="select-arrow icon-chevron-down"></i>
                                        </div>
                                        <div class="select-options" role="listbox">
                                            <div class="select-option" data-value="" role="option">Select Scoring Type</div>
                                            <div class="select-option tooltip-trigger" data-value="points" role="option"
                                                 data-tooltip="Scoring based on accumulated points (e.g., basketball scores, quiz points)">
                                                Points
                                            </div>
                                            <div class="select-option tooltip-trigger" data-value="time" role="option"
                                                 data-tooltip="Scoring based on completion time (e.g., track and field, swimming)">
                                                Time
                                            </div>
                                            <div class="select-option tooltip-trigger" data-value="distance" role="option"
                                                 data-tooltip="Scoring based on distance achieved (e.g., shot put, long jump)">
                                                Distance
                                            </div>
                                            <div class="select-option tooltip-trigger" data-value="subjective" role="option"
                                                 data-tooltip="Scoring based on judges' evaluation (e.g., dance, debate)">
                                                Subjective
                                            </div>
                                        </div>
                                        <input type="hidden" name="scoring_type" id="add_scoring_type" required>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="add_status" class="form-label">Status</label>
                                <div class="custom-select" data-name="status">
                                    <div class="select-trigger" tabindex="0" role="combobox" aria-expanded="false" aria-haspopup="listbox">
                                        <span class="select-value">Active</span>
                                        <i class="select-arrow icon-chevron-down"></i>
                                    </div>
                                    <div class="select-options" role="listbox">
                                        <div class="select-option tooltip-trigger" data-value="active" role="option"
                                             data-tooltip="Sport is available for event registration">
                                            Active
                                        </div>
                                        <div class="select-option tooltip-trigger" data-value="inactive" role="option"
                                             data-tooltip="Sport is temporarily disabled and unavailable">
                                            Inactive
                                        </div>
                                    </div>
                                    <input type="hidden" name="status" id="add_status" value="active">
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <h4>Description & Rules</h4>

                            <div class="form-group">
                                <label for="add_description" class="form-label">Description</label>
                                <textarea id="add_description" name="description" class="form-control" rows="3"
                                          placeholder="Brief description of the sport"></textarea>
                            </div>

                            <div class="form-group">
                                <label for="add_rules" class="form-label">Rules & Regulations</label>
                                <textarea id="add_rules" name="rules" class="form-control" rows="4"
                                          placeholder="Detailed rules and regulations for this sport"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('addSportModal')">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="icon-plus"></i>
                            Create Sport
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Sport Modal -->
    <div id="editSportModal" class="modal">
        <div class="modal-overlay">
            <div class="modal-content modal-lg">
                <div class="modal-header">
                    <h3><i class="icon-edit"></i> Edit Sport</h3>
                    <button class="modal-close" onclick="closeModal('editSportModal')">&times;</button>
                </div>
                <div class="modal-body">
                    <div id="editSportContent">
                        <div class="loading">Loading sport details...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal">
        <div class="modal-overlay">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="icon-trash"></i> Confirm Delete</h3>
                    <button class="modal-close" onclick="closeModal('deleteModal')">&times;</button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the sport "<span id="sportName"></span>"?</p>
                    <p class="text-warning"><i class="icon-warning"></i> This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <form id="deleteForm" method="POST">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="sport_id" id="deleteSportId">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('deleteModal')">
                            <i class="icon-cancel"></i>
                            Cancel
                        </button>
                        <button type="submit" class="btn btn-danger">
                            <i class="icon-trash"></i>
                            Delete Sport
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <script src="../../assets/js/admin.js"></script>
    <script>
        // Enhanced functionality
        document.addEventListener('DOMContentLoaded', function() {
            initializeBulkSelection();
            initializeSearch();
            initializeModals();
            initializeTooltips();
            initializeCustomSelects();
        });

        // Bulk selection functionality
        function initializeBulkSelection() {
            const selectAll = document.getElementById('selectAll');
            const rowCheckboxes = document.querySelectorAll('.row-checkbox');
            const bulkActions = document.getElementById('bulkActions');
            const selectedCount = document.querySelector('.selected-count');

            if (selectAll) {
                selectAll.addEventListener('change', function() {
                    rowCheckboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                    updateBulkActions();
                });
            }

            rowCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateBulkActions);
            });

            function updateBulkActions() {
                const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
                const count = checkedBoxes.length;

                if (count > 0) {
                    bulkActions.style.display = 'block';
                    selectedCount.textContent = count + ' selected';
                } else {
                    bulkActions.style.display = 'none';
                }

                // Update select all checkbox
                if (selectAll) {
                    selectAll.checked = count === rowCheckboxes.length;
                    selectAll.indeterminate = count > 0 && count < rowCheckboxes.length;
                }
            }

            // Add selected sport IDs to bulk form
            const bulkForm = document.getElementById('bulkForm');
            if (bulkForm) {
                bulkForm.addEventListener('submit', function(e) {
                    const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
                    if (checkedBoxes.length === 0) {
                        e.preventDefault();
                        alert('Please select at least one sport.');
                        return;
                    }

                    checkedBoxes.forEach(checkbox => {
                        const hiddenInput = document.createElement('input');
                        hiddenInput.type = 'hidden';
                        hiddenInput.name = 'selected_sports[]';
                        hiddenInput.value = checkbox.value;
                        this.appendChild(hiddenInput);
                    });
                });
            }
        }

        // Search functionality
        function initializeSearch() {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                let searchTimeout;
                searchInput.addEventListener('input', function() {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(() => {
                        if (this.value.length >= 2 || this.value.length === 0) {
                            document.getElementById('filtersForm').submit();
                        }
                    }, 500);
                });
            }
        }

        // Modal functionality
        function initializeModals() {
            // Add Sport Form
            const addSportForm = document.getElementById('addSportForm');
            if (addSportForm) {
                addSportForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const formData = new FormData(this);
                    const submitBtn = this.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;
                    const errorDiv = document.getElementById('addSportErrors');

                    // Client-side validation
                    if (!validateAddSportForm()) {
                        return;
                    }

                    submitBtn.innerHTML = '<i class="icon-spinner"></i> Creating...';
                    submitBtn.disabled = true;
                    errorDiv.innerHTML = '';

                    fetch(window.location.href, {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.text())
                    .then(() => {
                        closeModal('addSportModal');
                        location.reload();
                    })
                    .catch(error => {
                        errorDiv.innerHTML = '<div class="alert alert-error">Error creating sport. Please try again.</div>';
                    })
                    .finally(() => {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    });
                });
            }

            // Close modals when clicking overlay
            document.querySelectorAll('.modal-overlay').forEach(overlay => {
                overlay.addEventListener('click', function(e) {
                    if (e.target === this) {
                        const modal = this.closest('.modal');
                        if (modal) {
                            closeModal(modal.id);
                        }
                    }
                });
            });
        }

        // Form validation
        function validateAddSportForm() {
            const name = document.getElementById('add_name').value.trim();
            const category = document.getElementById('add_category').value;
            const scoringType = document.getElementById('add_scoring_type').value;
            const errorDiv = document.getElementById('addSportErrors');

            let errors = [];

            if (!name) {
                errors.push('Sport name is required');
            }

            if (!category) {
                errors.push('Please select a category');
                // Highlight the custom select
                const categorySelect = document.querySelector('[data-name="category"]');
                if (categorySelect) {
                    categorySelect.classList.add('error');
                    setTimeout(() => categorySelect.classList.remove('error'), 3000);
                }
            }

            if (!scoringType) {
                errors.push('Please select a scoring type');
                // Highlight the custom select
                const scoringSelect = document.querySelector('[data-name="scoring_type"]');
                if (scoringSelect) {
                    scoringSelect.classList.add('error');
                    setTimeout(() => scoringSelect.classList.remove('error'), 3000);
                }
            }

            if (errors.length > 0) {
                errorDiv.innerHTML = '<div class="alert alert-error">' + errors.join('<br>') + '</div>';
                return false;
            }

            return true;
        }

        // Utility functions
        function clearSelection() {
            document.querySelectorAll('.row-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });
            const selectAll = document.getElementById('selectAll');
            if (selectAll) selectAll.checked = false;
            document.getElementById('bulkActions').style.display = 'none';
        }

        function clearSearch() {
            document.getElementById('searchInput').value = '';
            document.getElementById('filtersForm').submit();
        }

        function deleteSport(sportId, sportName) {
            document.getElementById('sportName').textContent = sportName;
            document.getElementById('deleteSportId').value = sportId;
            openModal('deleteModal');
        }

        function editSport(sportId) {
            const content = document.getElementById('editSportContent');
            content.innerHTML = '<div class="loading"><i class="icon-spinner"></i> Loading sport details...</div>';
            openModal('editSportModal');

            // Fetch edit form (you'll need to create edit_ajax.php)
            fetch(`edit_ajax.php?id=${sportId}`)
                .then(response => response.text())
                .then(html => {
                    content.innerHTML = html;
                    initializeEditForm();
                })
                .catch(error => {
                    content.innerHTML = '<div class="alert alert-error">Error loading sport details. Please try again.</div>';
                });
        }

        // Enhanced Tooltip functionality for custom dropdowns
        function initializeTooltips() {
            let currentTooltip = null;
            let tooltipTimeout = null;

            // Create tooltip element if it doesn't exist
            let tooltip = document.getElementById('global-tooltip');
            if (!tooltip) {
                tooltip = document.createElement('div');
                tooltip.id = 'global-tooltip';
                tooltip.className = 'tooltip';
                tooltip.setAttribute('role', 'tooltip');
                document.body.appendChild(tooltip);
            }

            // Handle tooltip triggers with delegation for dynamic content
            document.addEventListener('mouseenter', function(e) {
                const trigger = e.target.closest('.tooltip-trigger');
                if (trigger && trigger.dataset.tooltip) {
                    // Debug logging
                    console.log('Tooltip trigger mouseenter:', {
                        element: trigger,
                        tooltip: trigger.dataset.tooltip,
                        classes: trigger.className
                    });

                    // Special handling for dropdown options
                    const isDropdownOption = trigger.classList.contains('select-option');
                    const dropdown = trigger.closest('.custom-select');

                    // Only show tooltip if dropdown is open or it's not a dropdown option
                    if (!isDropdownOption || (dropdown && dropdown.classList.contains('open'))) {
                        console.log('Showing tooltip for:', trigger.textContent.trim());
                        showTooltip(trigger, trigger.dataset.tooltip);
                    } else {
                        console.log('Dropdown not open, skipping tooltip');
                    }
                }
            }, true);

            document.addEventListener('mouseleave', function(e) {
                const trigger = e.target.closest('.tooltip-trigger');
                if (trigger) {
                    hideTooltip();
                }
            }, true);

            // Handle keyboard navigation
            document.addEventListener('focusin', function(e) {
                const trigger = e.target.closest('.tooltip-trigger');
                if (trigger && trigger.dataset.tooltip) {
                    const isDropdownOption = trigger.classList.contains('select-option');
                    const dropdown = trigger.closest('.custom-select');

                    if (!isDropdownOption || (dropdown && dropdown.classList.contains('open'))) {
                        showTooltip(trigger, trigger.dataset.tooltip);
                    }
                }
            });

            document.addEventListener('focusout', function(e) {
                const trigger = e.target.closest('.tooltip-trigger');
                if (trigger) {
                    hideTooltip();
                }
            });

            // Handle touch devices
            document.addEventListener('touchstart', function(e) {
                const trigger = e.target.closest('.tooltip-trigger');
                if (trigger && trigger.dataset.tooltip) {
                    const isDropdownOption = trigger.classList.contains('select-option');
                    const dropdown = trigger.closest('.custom-select');

                    if (!isDropdownOption || (dropdown && dropdown.classList.contains('open'))) {
                        showTooltip(trigger, trigger.dataset.tooltip);
                        // Auto-hide after 3 seconds on touch
                        setTimeout(() => hideTooltip(), 3000);
                    }
                } else if (!e.target.closest('.tooltip')) {
                    hideTooltip();
                }
            });

            function showTooltip(trigger, text) {
                clearTimeout(tooltipTimeout);

                tooltip.textContent = text;
                tooltip.style.display = 'block';
                tooltip.style.opacity = '0';
                tooltip.style.zIndex = '10000'; // Ensure it's above dropdowns

                // Position tooltip
                const rect = trigger.getBoundingClientRect();

                // Wait for next frame to get accurate tooltip dimensions
                requestAnimationFrame(() => {
                    const tooltipRect = tooltip.getBoundingClientRect();

                    let top = rect.top - tooltipRect.height - 10;
                    let left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);

                    // Adjust for viewport boundaries
                    if (top < 10) {
                        top = rect.bottom + 10;
                        tooltip.classList.add('tooltip-bottom');
                    } else {
                        tooltip.classList.remove('tooltip-bottom');
                    }

                    if (left < 10) {
                        left = 10;
                    } else if (left + tooltipRect.width > window.innerWidth - 10) {
                        left = window.innerWidth - tooltipRect.width - 10;
                    }

                    tooltip.style.top = top + window.scrollY + 'px';
                    tooltip.style.left = left + 'px';

                    // Fade in
                    tooltip.style.opacity = '1';
                });

                currentTooltip = trigger;
            }

            function hideTooltip() {
                if (currentTooltip) {
                    tooltipTimeout = setTimeout(() => {
                        tooltip.style.opacity = '0';
                        setTimeout(() => {
                            tooltip.style.display = 'none';
                        }, 200);
                        currentTooltip = null;
                    }, 100);
                }
            }

            // Expose functions for debugging
            window.debugTooltip = {
                show: showTooltip,
                hide: hideTooltip,
                current: () => currentTooltip,
                test: () => {
                    console.log('Testing tooltip functionality...');
                    const triggers = document.querySelectorAll('.tooltip-trigger[data-tooltip]');
                    console.log(`Found ${triggers.length} tooltip triggers:`, triggers);

                    triggers.forEach((trigger, index) => {
                        console.log(`Trigger ${index}:`, {
                            element: trigger,
                            tooltip: trigger.dataset.tooltip,
                            classes: trigger.className,
                            visible: trigger.offsetParent !== null
                        });
                    });

                    return triggers;
                }
            };
        }

        // Custom Select functionality
        function initializeCustomSelects() {
            document.querySelectorAll('.custom-select').forEach(select => {
                const trigger = select.querySelector('.select-trigger');
                const options = select.querySelector('.select-options');
                const hiddenInput = select.querySelector('input[type="hidden"]');
                const valueSpan = select.querySelector('.select-value');

                // Toggle dropdown
                trigger.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    closeAllSelects();
                    toggleSelect(select);
                });

                // Keyboard navigation
                trigger.addEventListener('keydown', function(e) {
                    switch(e.key) {
                        case 'Enter':
                        case ' ':
                            e.preventDefault();
                            closeAllSelects();
                            toggleSelect(select);
                            break;
                        case 'ArrowDown':
                            e.preventDefault();
                            if (!select.classList.contains('open')) {
                                toggleSelect(select);
                            } else {
                                focusNextOption(select);
                            }
                            break;
                        case 'ArrowUp':
                            e.preventDefault();
                            if (select.classList.contains('open')) {
                                focusPrevOption(select);
                            }
                            break;
                        case 'Escape':
                            closeSelect(select);
                            trigger.focus();
                            break;
                    }
                });

                // Option selection
                options.addEventListener('click', function(e) {
                    const option = e.target.closest('.select-option');
                    if (option) {
                        selectOption(select, option);
                    }
                });

                // Option hover for tooltips
                options.addEventListener('mouseenter', function(e) {
                    const option = e.target.closest('.select-option.tooltip-trigger');
                    if (option && option.dataset.tooltip && select.classList.contains('open')) {
                        // Ensure tooltip shows for dropdown options
                        e.stopPropagation();
                    }
                }, true);

                // Option keyboard navigation
                options.addEventListener('keydown', function(e) {
                    const focusedOption = options.querySelector('.select-option:focus');

                    switch(e.key) {
                        case 'Enter':
                        case ' ':
                            e.preventDefault();
                            if (focusedOption) {
                                selectOption(select, focusedOption);
                            }
                            break;
                        case 'ArrowDown':
                            e.preventDefault();
                            focusNextOption(select);
                            break;
                        case 'ArrowUp':
                            e.preventDefault();
                            focusPrevOption(select);
                            break;
                        case 'Escape':
                            closeSelect(select);
                            trigger.focus();
                            break;
                    }
                });

                // Make options focusable and ensure tooltip attributes
                options.querySelectorAll('.select-option').forEach(option => {
                    option.setAttribute('tabindex', '-1');

                    // Ensure tooltip triggers are properly set up
                    if (option.classList.contains('tooltip-trigger') && option.dataset.tooltip) {
                        option.addEventListener('mouseenter', function(e) {
                            if (select.classList.contains('open')) {
                                e.stopPropagation();
                            }
                        });
                    }
                });
            });

            // Close dropdowns when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.custom-select')) {
                    closeAllSelects();
                }
            });
        }

        function toggleSelect(select) {
            const isOpen = select.classList.contains('open');
            if (isOpen) {
                closeSelect(select);
            } else {
                openSelect(select);
            }
        }

        function openSelect(select) {
            select.classList.add('open');
            const trigger = select.querySelector('.select-trigger');
            trigger.setAttribute('aria-expanded', 'true');

            // Focus first option or selected option
            const selectedOption = select.querySelector('.select-option.selected');
            const firstOption = select.querySelector('.select-option');
            if (selectedOption) {
                selectedOption.focus();
            } else if (firstOption) {
                firstOption.focus();
            }
        }

        function closeSelect(select) {
            select.classList.remove('open');
            const trigger = select.querySelector('.select-trigger');
            trigger.setAttribute('aria-expanded', 'false');
        }

        function closeAllSelects() {
            document.querySelectorAll('.custom-select.open').forEach(closeSelect);
        }

        function selectOption(select, option) {
            const value = option.dataset.value;
            const text = option.textContent.trim();
            const hiddenInput = select.querySelector('input[type="hidden"]');
            const valueSpan = select.querySelector('.select-value');

            // Update hidden input
            hiddenInput.value = value;

            // Update display text
            valueSpan.textContent = text;

            // Update selected state
            select.querySelectorAll('.select-option').forEach(opt => {
                opt.classList.remove('selected');
            });
            option.classList.add('selected');

            // Close dropdown
            closeSelect(select);

            // Focus trigger
            select.querySelector('.select-trigger').focus();

            // Trigger change event
            hiddenInput.dispatchEvent(new Event('change', { bubbles: true }));
        }

        function focusNextOption(select) {
            const options = select.querySelectorAll('.select-option');
            const focused = select.querySelector('.select-option:focus');
            let nextIndex = 0;

            if (focused) {
                const currentIndex = Array.from(options).indexOf(focused);
                nextIndex = (currentIndex + 1) % options.length;
            }

            options[nextIndex].focus();
        }

        function focusPrevOption(select) {
            const options = select.querySelectorAll('.select-option');
            const focused = select.querySelector('.select-option:focus');
            let prevIndex = options.length - 1;

            if (focused) {
                const currentIndex = Array.from(options).indexOf(focused);
                prevIndex = currentIndex > 0 ? currentIndex - 1 : options.length - 1;
            }

            options[prevIndex].focus();
        }

        function initializeEditForm() {
            // Initialize edit form when loaded via AJAX
            const form = document.getElementById('editSportForm');
            if (form) {
                // Re-initialize tooltips and custom selects for dynamically loaded content
                initializeTooltips();
                initializeCustomSelects();

                form.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const formData = new FormData(form);
                    const submitBtn = form.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;

                    submitBtn.innerHTML = '<i class="icon-spinner"></i> Saving...';
                    submitBtn.disabled = true;

                    fetch('edit_ajax.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            closeModal('editSportModal');
                            location.reload();
                        } else {
                            const errorDiv = document.getElementById('editSportErrors');
                            if (errorDiv) {
                                errorDiv.innerHTML = `<div class="alert alert-error">${data.message}</div>`;
                            }
                        }
                    })
                    .catch(error => {
                        const errorDiv = document.getElementById('editSportErrors');
                        if (errorDiv) {
                            errorDiv.innerHTML = '<div class="alert alert-error">Error saving sport. Please try again.</div>';
                        }
                    })
                    .finally(() => {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    });
                });
            }
        }

        function openModal(modalId) {
            document.getElementById(modalId).classList.add('show');
            document.body.style.overflow = 'hidden';

            // Re-initialize tooltips and custom selects for modal content
            setTimeout(() => {
                initializeTooltips();
                initializeCustomSelects();
            }, 100);
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('show');
            document.body.style.overflow = '';

            // Reset forms when closing
            if (modalId === 'addSportModal') {
                document.getElementById('addSportForm').reset();
                document.getElementById('addSportErrors').innerHTML = '';
            }
        }
    </script>

    <style>
        /* Enhanced Sports Management Styles */

        /* CSS Variables for consistency */
        :root {
            --primary-50: #eff6ff;
            --primary-100: #dbeafe;
            --primary-500: #3b82f6;
            --primary-600: #2563eb;
            --primary-700: #1d4ed8;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --white: #ffffff;
            --green-50: #f0fdf4;
            --green-100: #dcfce7;
            --green-500: #22c55e;
            --green-600: #16a34a;
            --red-50: #fef2f2;
            --red-100: #fee2e2;
            --red-500: #ef4444;
            --red-600: #dc2626;
            --yellow-50: #fffbeb;
            --yellow-100: #fef3c7;
            --yellow-500: #f59e0b;
            --orange-50: #fff7ed;
            --orange-100: #ffedd5;
            --orange-500: #f97316;
            --purple-50: #faf5ff;
            --purple-100: #f3e8ff;
            --purple-500: #a855f7;
            --blue-50: #eff6ff;
            --blue-100: #dbeafe;
            --blue-500: #3b82f6;
        }

        /* Enhanced Button Styling */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            border: none;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
            line-height: 1.25;
        }

        .btn-primary {
            background: var(--primary-600, #2563eb) !important;
            color: var(--white, #ffffff) !important;
            border: 1px solid var(--primary-600, #2563eb) !important;
        }

        .btn-primary:hover:not(:disabled) {
            background: var(--primary-700, #1d4ed8) !important;
            border-color: var(--primary-700, #1d4ed8) !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);
        }

        .btn-secondary {
            background: var(--gray-100, #f3f4f6) !important;
            color: var(--gray-700, #374151) !important;
            border: 1px solid var(--gray-300, #d1d5db) !important;
        }

        .btn-secondary:hover {
            background: var(--gray-200, #e5e7eb) !important;
            transform: translateY(-1px);
        }

        .btn-danger {
            background: var(--red-600, #dc2626) !important;
            color: var(--white, #ffffff) !important;
            border: 1px solid var(--red-600, #dc2626) !important;
        }

        .btn-danger:hover {
            background: var(--red-700, #b91c1c) !important;
            transform: translateY(-1px);
        }

        .btn-outline {
            background: transparent !important;
            color: var(--gray-600, #4b5563) !important;
            border: 1px solid var(--gray-300, #d1d5db) !important;
        }

        .btn-outline:hover {
            background: var(--gray-50, #f9fafb) !important;
            border-color: var(--gray-400, #9ca3af) !important;
            color: var(--gray-900, #1f2937) !important;
        }

        .btn-sm {
            padding: 0.5rem 0.75rem;
            font-size: 0.8125rem;
        }

        .btn-disabled {
            background: var(--gray-300, #d1d5db) !important;
            color: var(--gray-500, #6b7280) !important;
            border: 1px solid var(--gray-300, #d1d5db) !important;
            cursor: not-allowed;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        /* Enhanced Filters */
        .filters-container {
            background: var(--white, #ffffff);
            border: 1px solid var(--gray-200, #e5e7eb);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .filter-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr auto;
            gap: 1rem;
            align-items: end;
        }

        .search-group {
            position: relative;
        }

        .search-input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .search-input-wrapper .icon-search {
            position: absolute;
            left: 0.75rem;
            color: var(--gray-400, #9ca3af);
            z-index: 2;
        }

        .search-input {
            padding-left: 2.5rem !important;
            padding-right: 2.5rem !important;
        }

        .clear-search {
            position: absolute;
            right: 0.75rem;
            background: none;
            border: none;
            color: var(--gray-400, #9ca3af);
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 4px;
            z-index: 2;
        }

        .clear-search:hover {
            color: var(--gray-600, #4b5563);
            background: var(--gray-100, #f3f4f6);
        }

        /* Enhanced Sports Grid */
        .sports-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .sport-card {
            background: var(--white, #ffffff);
            border: 1px solid var(--gray-200, #e5e7eb);
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
            position: relative;
        }

        .sport-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            border-color: var(--primary-300, #93c5fd);
        }

        .sport-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: var(--gray-50, #f9fafb);
            border-bottom: 1px solid var(--gray-200, #e5e7eb);
        }

        .sport-content {
            padding: 1.5rem;
        }

        .sport-content h3 {
            margin: 0 0 0.75rem 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--gray-900, #111827);
            line-height: 1.3;
        }

        .sport-description {
            color: var(--gray-600, #4b5563);
            margin: 0 0 1rem 0;
            line-height: 1.5;
            font-size: 0.875rem;
        }

        .sport-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .detail-label {
            font-size: 0.75rem;
            font-weight: 500;
            color: var(--gray-500, #6b7280);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .detail-value {
            font-weight: 600;
            color: var(--gray-900, #111827);
        }

        .sport-stats {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            padding: 0.75rem;
            background: var(--gray-50, #f9fafb);
            border-radius: 6px;
        }

        .stat-item {
            text-align: center;
            flex: 1;
        }

        .stat-value {
            display: block;
            font-size: 1.25rem;
            font-weight: bold;
            color: var(--primary-600, #2563eb);
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.75rem;
            color: var(--gray-500, #6b7280);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .sport-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
            padding: 1rem;
            background: var(--gray-50, #f9fafb);
            border-top: 1px solid var(--gray-200, #e5e7eb);
        }

        /* Enhanced Category Badges */
        .category-badge {
            display: inline-block;
            padding: 0.375rem 0.75rem;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .category-badge.category-team {
            background: var(--blue-100, #dbeafe);
            color: var(--blue-800, #1e40af);
        }

        .category-badge.category-individual {
            background: var(--green-100, #dcfce7);
            color: var(--green-800, #166534);
        }

        .category-badge.category-performing_arts {
            background: var(--purple-100, #f3e8ff);
            color: var(--purple-800, #6b21a8);
        }

        .category-badge.category-academic {
            background: var(--orange-100, #ffedd5);
            color: var(--orange-800, #9a3412);
        }

        /* Enhanced Status Badges */
        .status-badge {
            display: inline-block;
            padding: 0.375rem 0.75rem;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .status-badge.status-active {
            background: var(--green-100, #dcfce7);
            color: var(--green-800, #166534);
        }

        .status-badge.status-inactive {
            background: var(--gray-100, #f3f4f6);
            color: var(--gray-600, #4b5563);
        }

        /* Modal Enhancements */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.5);
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            position: relative;
            background: var(--white, #ffffff);
            border-radius: 12px;
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .modal-lg {
            max-width: 800px;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem;
            border-bottom: 1px solid var(--gray-200, #e5e7eb);
            background: var(--gray-50, #f9fafb);
        }

        .modal-header h3 {
            margin: 0;
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--gray-900, #111827);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--gray-400, #9ca3af);
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .modal-close:hover {
            color: var(--gray-600, #4b5563);
            background: var(--gray-100, #f3f4f6);
        }

        .modal-body {
            padding: 1.5rem;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
            padding: 1.5rem;
            border-top: 1px solid var(--gray-200, #e5e7eb);
            background: var(--gray-50, #f9fafb);
        }

        /* Form Enhancements */
        .form-section {
            margin-bottom: 2rem;
        }

        .form-section h4 {
            margin: 0 0 1rem 0;
            font-size: 1rem;
            font-weight: 600;
            color: var(--gray-900, #111827);
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--gray-200, #e5e7eb);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--gray-700, #374151);
            font-size: 0.875rem;
        }

        .form-label.required::after {
            content: ' *';
            color: var(--red-500, #ef4444);
        }

        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--gray-300, #d1d5db);
            border-radius: 6px;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            background: var(--white, #ffffff);
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-500, #3b82f6);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-help {
            display: block;
            margin-top: 0.25rem;
            font-size: 0.75rem;
            color: var(--gray-500, #6b7280);
        }

        /* Loading States */
        .loading {
            text-align: center;
            padding: 2rem;
            color: var(--gray-600, #4b5563);
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .icon-spinner {
            animation: spin 1s linear infinite;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .filter-row {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .sports-grid {
                grid-template-columns: 1fr;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .sport-actions {
                flex-direction: column;
            }

            .modal-content {
                width: 95%;
                margin: 1rem;
            }
        }

        /* Accessibility */
        .btn:focus,
        .form-control:focus {
            outline: 2px solid var(--primary-500, #3b82f6);
            outline-offset: 2px;
        }

        /* Checkbox styling */
        .checkbox-container {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
        }

        .checkbox-container input[type="checkbox"] {
            width: 16px;
            height: 16px;
            accent-color: var(--primary-600, #2563eb);
        }

        .checkmark {
            display: none; /* Using native checkbox styling */
        }

        /* Tooltip Styles */
        .tooltip-trigger {
            position: relative;
            cursor: help;
        }

        .tooltip-icon {
            display: inline-block;
            margin-left: 0.25rem;
            font-size: 0.75rem;
            color: var(--gray-400, #9ca3af);
            transition: color 0.2s ease;
            vertical-align: super;
            line-height: 1;
        }

        .tooltip-trigger:hover .tooltip-icon,
        .tooltip-trigger:focus .tooltip-icon {
            color: var(--primary-500, #3b82f6);
        }

        .tooltip {
            position: absolute;
            z-index: 10001; /* Higher than dropdown z-index */
            background: var(--gray-900, #111827);
            color: var(--white, #ffffff);
            padding: 0.75rem 1rem;
            border-radius: 8px;
            font-size: 0.875rem;
            line-height: 1.4;
            max-width: 280px;
            word-wrap: break-word;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            opacity: 0;
            transition: opacity 0.2s ease;
            pointer-events: none;
            font-weight: 400;
        }

        .tooltip::before {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 6px solid transparent;
            border-top-color: var(--gray-900, #111827);
        }

        .tooltip.tooltip-bottom::before {
            top: -12px;
            border-top-color: transparent;
            border-bottom-color: var(--gray-900, #111827);
        }

        /* Enhanced tooltip triggers for specific elements */
        .category-badge.tooltip-trigger,
        .status-badge.tooltip-trigger {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
        }

        .detail-value.tooltip-trigger {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
        }

        /* Dropdown option tooltip triggers */
        .select-option.tooltip-trigger {
            position: relative;
        }

        .select-option.tooltip-trigger:hover {
            background: var(--primary-50, #eff6ff) !important;
            color: var(--primary-700, #1d4ed8) !important;
        }

        /* Form label tooltips */
        .form-label .tooltip-trigger {
            margin-left: 0.5rem;
        }

        .form-label .tooltip-icon {
            font-size: 0.875rem;
            vertical-align: baseline;
            margin-left: 0.25rem;
        }

        /* Accessibility improvements */
        .tooltip-trigger:focus {
            outline: 2px solid var(--primary-500, #3b82f6);
            outline-offset: 2px;
            border-radius: 4px;
        }

        /* Mobile-specific tooltip styles */
        @media (max-width: 768px) {
            .tooltip {
                max-width: 250px;
                font-size: 0.8125rem;
                padding: 0.625rem 0.875rem;
            }

            .tooltip-icon {
                font-size: 0.875rem;
            }
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .tooltip {
                background: #000000;
                border: 2px solid #ffffff;
            }

            .tooltip::before {
                border-top-color: #000000;
            }

            .tooltip.tooltip-bottom::before {
                border-bottom-color: #000000;
            }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            .tooltip {
                transition: none;
            }

            .tooltip-icon {
                transition: none;
            }
        }

        /* Print styles */
        @media print {
            .tooltip,
            .tooltip-icon {
                display: none !important;
            }
        }

        /* Enhanced badge styles with tooltip support */
        .category-badge,
        .status-badge {
            position: relative;
        }

        .category-badge .tooltip-icon,
        .status-badge .tooltip-icon {
            opacity: 0.7;
            font-size: 0.6875rem;
        }

        .category-badge:hover .tooltip-icon,
        .status-badge:hover .tooltip-icon,
        .category-badge:focus .tooltip-icon,
        .status-badge:focus .tooltip-icon {
            opacity: 1;
        }

        /* Tooltip positioning helpers */
        .tooltip-trigger[data-tooltip-position="top"] .tooltip {
            bottom: 100%;
            margin-bottom: 10px;
        }

        .tooltip-trigger[data-tooltip-position="bottom"] .tooltip {
            top: 100%;
            margin-top: 10px;
        }

        .tooltip-trigger[data-tooltip-position="left"] .tooltip {
            right: 100%;
            margin-right: 10px;
            top: 50%;
            transform: translateY(-50%);
        }

        .tooltip-trigger[data-tooltip-position="right"] .tooltip {
            left: 100%;
            margin-left: 10px;
            top: 50%;
            transform: translateY(-50%);
        }

        /* Custom Select Styles */
        .custom-select {
            position: relative;
            width: 100%;
        }

        .select-trigger {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--gray-300, #d1d5db);
            border-radius: 6px;
            background: var(--white, #ffffff);
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.875rem;
            color: var(--gray-700, #374151);
        }

        .select-trigger:hover {
            border-color: var(--gray-400, #9ca3af);
        }

        .select-trigger:focus {
            outline: none;
            border-color: var(--primary-500, #3b82f6);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .custom-select.open .select-trigger {
            border-color: var(--primary-500, #3b82f6);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .select-value {
            flex: 1;
            text-align: left;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .select-arrow {
            margin-left: 0.5rem;
            font-size: 0.75rem;
            color: var(--gray-400, #9ca3af);
            transition: transform 0.2s ease;
        }

        .custom-select.open .select-arrow {
            transform: rotate(180deg);
        }

        .select-options {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            z-index: 10000; /* High z-index for dropdown */
            background: var(--white, #ffffff);
            border: 1px solid var(--gray-300, #d1d5db);
            border-radius: 6px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            max-height: 200px;
            overflow-y: auto;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.2s ease;
            margin-top: 4px;
        }

        .custom-select.open .select-options {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .select-option {
            padding: 0.75rem;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.875rem;
            color: var(--gray-700, #374151);
            border-bottom: 1px solid var(--gray-100, #f3f4f6);
            position: relative;
        }

        .select-option:last-child {
            border-bottom: none;
        }

        .select-option:hover,
        .select-option:focus {
            background: var(--primary-50, #eff6ff);
            color: var(--primary-700, #1d4ed8);
            outline: none;
        }

        .select-option.selected {
            background: var(--primary-100, #dbeafe);
            color: var(--primary-800, #1e40af);
            font-weight: 500;
        }

        .select-option.selected::after {
            content: '✓';
            position: absolute;
            right: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--primary-600, #2563eb);
            font-weight: bold;
        }

        /* Custom select in forms */
        .form-group .custom-select {
            margin-top: 0;
        }

        /* Disabled state */
        .custom-select.disabled .select-trigger {
            background: var(--gray-100, #f3f4f6);
            color: var(--gray-500, #6b7280);
            cursor: not-allowed;
        }

        .custom-select.disabled .select-trigger:hover {
            border-color: var(--gray-300, #d1d5db);
        }

        /* Error state */
        .custom-select.error .select-trigger {
            border-color: var(--red-500, #ef4444);
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        /* Required field indicator */
        .custom-select[data-required="true"] .select-trigger:invalid {
            border-color: var(--red-300, #fca5a5);
        }

        /* Mobile responsiveness */
        @media (max-width: 768px) {
            .select-options {
                max-height: 150px;
            }

            .select-option {
                padding: 0.625rem 0.75rem;
                font-size: 0.8125rem;
            }
        }

        /* High contrast mode */
        @media (prefers-contrast: high) {
            .select-trigger {
                border-width: 2px;
            }

            .select-options {
                border-width: 2px;
            }

            .select-option:hover,
            .select-option:focus {
                background: #000000;
                color: #ffffff;
            }
        }

        /* Reduced motion */
        @media (prefers-reduced-motion: reduce) {
            .select-trigger,
            .select-options,
            .select-option,
            .select-arrow {
                transition: none;
            }
        }

        /* Focus visible for better accessibility */
        .select-option:focus-visible {
            outline: 2px solid var(--primary-500, #3b82f6);
            outline-offset: -2px;
        }

        /* Scrollbar styling for options */
        .select-options::-webkit-scrollbar {
            width: 6px;
        }

        .select-options::-webkit-scrollbar-track {
            background: var(--gray-100, #f3f4f6);
        }

        .select-options::-webkit-scrollbar-thumb {
            background: var(--gray-300, #d1d5db);
            border-radius: 3px;
        }

        .select-options::-webkit-scrollbar-thumb:hover {
            background: var(--gray-400, #9ca3af);
        }
    </style>
</body>
</html>
