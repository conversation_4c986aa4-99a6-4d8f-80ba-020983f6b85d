<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Events Management - View Event AJAX Handler
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication and permission
requireAuth();
requirePermission('manage_events');

$eventId = (int)($_GET['id'] ?? 0);

if (!$eventId) {
    echo '<div class="alert alert-error">Invalid event ID</div>';
    exit;
}

// Get event details
$event = fetchOne("
    SELECT e.*, 
           au.full_name as created_by_name,
           (SELECT COUNT(*) FROM matches WHERE event_id = e.event_id) as match_count,
           (SELECT COUNT(*) FROM department_standings WHERE event_id = e.event_id) as dept_count
    FROM events e
    LEFT JOIN admin_users au ON e.created_by = au.admin_id
    WHERE e.event_id = ?
", [$eventId]);

if (!$event) {
    echo '<div class="alert alert-error">Event not found</div>';
    exit;
}

// Get event matches with participants
$matches = fetchAll("
    SELECT m.*,
           v.name as venue_name,
           s.name as sport_name,
           GROUP_CONCAT(DISTINCT d.name ORDER BY mp.participant_id SEPARATOR ' vs ') as participants
    FROM matches m
    LEFT JOIN venues v ON m.venue_id = v.venue_id
    LEFT JOIN sports s ON m.sport_id = s.sport_id
    LEFT JOIN match_participants mp ON m.match_id = mp.match_id
    LEFT JOIN departments d ON mp.dept_id = d.dept_id
    WHERE m.event_id = ?
    GROUP BY m.match_id
    ORDER BY m.match_date, m.match_time
", [$eventId]);

// Get participating departments
$departments = fetchAll("
    SELECT d.name, ds.total_points as points, ds.rank_position as position
    FROM department_standings ds
    JOIN departments d ON ds.dept_id = d.dept_id
    WHERE ds.event_id = ?
    ORDER BY ds.rank_position ASC, ds.total_points DESC
", [$eventId]);
?>

<div class="event-details">
    <!-- Event Header -->
    <div class="event-header">
        <div class="event-title">
            <h2><?php echo htmlspecialchars($event['name']); ?></h2>
            <span class="status-badge status-<?php echo $event['status']; ?>">
                <?php echo ucfirst($event['status']); ?>
            </span>
        </div>
        <?php if ($event['description']): ?>
            <p class="event-description"><?php echo htmlspecialchars($event['description']); ?></p>
        <?php endif; ?>
    </div>

    <!-- Event Information Grid -->
    <div class="event-info-grid">
        <div class="info-card">
            <div class="info-header">
                <i class="icon-calendar"></i>
                <h3>Event Dates</h3>
            </div>
            <div class="info-content">
                <div class="date-range">
                    <div class="start-date">
                        <label>Start Date:</label>
                        <span><?php echo formatDate($event['start_date']); ?></span>
                    </div>
                    <div class="end-date">
                        <label>End Date:</label>
                        <span><?php echo formatDate($event['end_date']); ?></span>
                    </div>
                </div>
            </div>
        </div>

        <div class="info-card">
            <div class="info-header">
                <i class="icon-trophy"></i>
                <h3>Event Statistics</h3>
            </div>
            <div class="info-content">
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-value"><?php echo $event['match_count']; ?></span>
                        <span class="stat-label">Total Matches</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value"><?php echo $event['dept_count']; ?></span>
                        <span class="stat-label">Participating Departments</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="info-card">
            <div class="info-header">
                <i class="icon-user"></i>
                <h3>Event Management</h3>
            </div>
            <div class="info-content">
                <div class="management-info">
                    <div class="created-by">
                        <label>Created By:</label>
                        <span><?php echo htmlspecialchars($event['created_by_name'] ?? 'System'); ?></span>
                    </div>
                    <div class="created-date">
                        <label>Created On:</label>
                        <span><?php echo formatDateTime($event['created_at']); ?></span>
                    </div>
                    <?php if ($event['updated_at']): ?>
                        <div class="updated-date">
                            <label>Last Updated:</label>
                            <span><?php echo formatDateTime($event['updated_at']); ?></span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Participating Departments -->
    <?php if (!empty($departments)): ?>
        <div class="section">
            <h3><i class="icon-users"></i> Participating Departments</h3>
            <div class="departments-table">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Position</th>
                            <th>Department</th>
                            <th>Points</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($departments as $dept): ?>
                            <tr>
                                <td>
                                    <?php if ($dept['position']): ?>
                                        <span class="position-badge position-<?php echo $dept['position']; ?>">
                                            #<?php echo $dept['position']; ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="position-badge">-</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo htmlspecialchars($dept['name']); ?></td>
                                <td><strong><?php echo $dept['points']; ?> pts</strong></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    <?php endif; ?>

    <!-- Event Matches -->
    <?php if (!empty($matches)): ?>
        <div class="section">
            <h3><i class="icon-calendar"></i> Event Matches</h3>
            <div class="matches-table">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Date & Time</th>
                            <th>Sport</th>
                            <th>Participants</th>
                            <th>Venue</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($matches as $match): ?>
                            <tr>
                                <td>
                                    <div class="match-datetime">
                                        <div><?php echo formatDate($match['match_date']); ?></div>
                                        <div class="time"><?php echo formatTime($match['match_time']); ?></div>
                                    </div>
                                </td>
                                <td>
                                    <span class="sport-name"><?php echo htmlspecialchars($match['sport_name'] ?? 'TBD'); ?></span>
                                </td>
                                <td>
                                    <div class="match-participants">
                                        <?php echo htmlspecialchars($match['participants'] ?? 'No participants'); ?>
                                    </div>
                                </td>
                                <td><?php echo htmlspecialchars($match['venue_name'] ?? 'TBD'); ?></td>
                                <td>
                                    <span class="status-badge status-<?php echo $match['status']; ?>">
                                        <?php echo ucfirst($match['status']); ?>
                                    </span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    <?php else: ?>
        <div class="section">
            <div class="no-data">
                <i class="icon-calendar"></i>
                <h4>No Matches Scheduled</h4>
                <p>No matches have been scheduled for this event yet.</p>
            </div>
        </div>
    <?php endif; ?>
</div>

<style>
.event-details {
    padding: 1rem 0;
}

.event-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--gray-200);
}

.event-title {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.5rem;
}

.event-title h2 {
    margin: 0;
    color: var(--gray-900);
}

.event-description {
    color: var(--gray-600);
    margin: 0;
    line-height: 1.5;
}

.event-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.info-card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: 8px;
    overflow: hidden;
}

.info-header {
    background: var(--gray-50);
    padding: 1rem;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.info-header h3 {
    margin: 0;
    font-size: 1rem;
    color: var(--gray-900);
}

.info-content {
    padding: 1rem;
}

.date-range > div {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.date-range label {
    font-weight: 500;
    color: var(--gray-600);
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.stat-item {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-600);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--gray-600);
}

.management-info > div {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.management-info label {
    font-weight: 500;
    color: var(--gray-600);
}

.section {
    margin-bottom: 2rem;
}

.section h3 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    color: var(--gray-900);
}

.table {
    width: 100%;
    border-collapse: collapse;
    background: var(--white);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table th,
.table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--gray-200);
}

.table th {
    background: var(--gray-50);
    font-weight: 600;
    color: var(--gray-900);
}

.position-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 600;
    background: var(--gray-100);
    color: var(--gray-700);
}

.position-badge.position-1 {
    background: var(--yellow-100);
    color: var(--yellow-800);
}

.position-badge.position-2 {
    background: var(--gray-100);
    color: var(--gray-800);
}

.position-badge.position-3 {
    background: var(--orange-100);
    color: var(--orange-800);
}

.match-datetime .time {
    font-size: 0.875rem;
    color: var(--gray-600);
}

.match-participants {
    font-weight: 500;
}

.sport-name {
    font-weight: 500;
    color: var(--primary-600);
}

.no-data {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--gray-500);
}

.no-data i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.no-data h4 {
    margin: 0 0 0.5rem 0;
    color: var(--gray-700);
}

.no-data p {
    margin: 0;
}
</style>
