<?php
/**
 * Admin Sidebar Include
 */
if (!defined('SCIMS_ACCESS')) {
    die('Direct access not permitted');
}

$currentUser = getCurrentUser();
$currentPage = basename($_SERVER['PHP_SELF'], '.php');
$currentDir = basename(dirname($_SERVER['PHP_SELF']));

// Determine the base path for navigation links based on directory depth
$basePath = '';
$currentPath = $_SERVER['PHP_SELF'];

// Calculate how many levels deep we are from the admin root
$adminPos = strpos($currentPath, '/admin/');
if ($adminPos !== false) {
    $pathAfterAdmin = substr($currentPath, $adminPos + 7); // 7 = length of '/admin/'
    $depth = substr_count($pathAfterAdmin, '/');

    // Generate the appropriate number of '../' based on depth
    if ($depth > 0) {
        $basePath = str_repeat('../', $depth);
    }
}
?>
<aside class="admin-sidebar" id="adminSidebar">
    <div class="sidebar-header">
        <a href="<?php echo $basePath; ?>dashboard.php" class="sidebar-logo">
            <div class="logo-placeholder">
                <i class="icon-trophy"></i>
            </div>
            <h1>SCIMS</h1>
        </a>
    </div>

    <nav class="sidebar-nav">
        <div class="nav-section">
            <div class="nav-section-title">Main</div>
            <a href="<?php echo $basePath; ?>dashboard.php" class="nav-item <?php echo $currentPage === 'dashboard' ? 'active' : ''; ?>">
                <i class="icon-dashboard"></i>
                Dashboard
            </a>
        </div>
        
        <?php if (hasPermission('manage_events')): ?>
        <div class="nav-section">
            <div class="nav-section-title">Event Management</div>
            <a href="<?php echo $basePath; ?>events/" class="nav-item <?php echo $currentDir === 'events' ? 'active' : ''; ?>">
                <i class="icon-calendar"></i>
                Events
            </a>
            <a href="<?php echo $basePath; ?>sports/" class="nav-item <?php echo $currentDir === 'sports' ? 'active' : ''; ?>">
                <i class="icon-trophy"></i>
                Sports
            </a>
            <a href="<?php echo $basePath; ?>venues/" class="nav-item <?php echo $currentDir === 'venues' ? 'active' : ''; ?>">
                <i class="icon-location"></i>
                Venues
            </a>
        </div>
        <?php endif; ?>
        
        <?php if (hasPermission('manage_matches')): ?>
        <div class="nav-section">
            <div class="nav-section-title">Competition</div>
            <a href="<?php echo $basePath; ?>matches/" class="nav-item <?php echo $currentDir === 'matches' ? 'active' : ''; ?>">
                <i class="icon-play"></i>
                Matches
            </a>
            <a href="<?php echo $basePath; ?>scores/" class="nav-item <?php echo $currentDir === 'scores' ? 'active' : ''; ?>">
                <i class="icon-edit"></i>
                Score Entry
            </a>
            <a href="<?php echo $basePath; ?>officials/" class="nav-item <?php echo $currentDir === 'officials' ? 'active' : ''; ?>">
                <i class="icon-users"></i>
                Officials
            </a>
        </div>
        <?php endif; ?>
        
        <div class="nav-section">
            <div class="nav-section-title">Participants</div>
            <a href="<?php echo $basePath; ?>departments/" class="nav-item <?php echo $currentDir === 'departments' ? 'active' : ''; ?>">
                <i class="icon-building"></i>
                Departments
            </a>
            <a href="<?php echo $basePath; ?>participants/" class="nav-item <?php echo $currentDir === 'participants' ? 'active' : ''; ?>">
                <i class="icon-user-group"></i>
                Participants
            </a>
        </div>
        
        <?php if (hasPermission('view_reports')): ?>
        <div class="nav-section">
            <div class="nav-section-title">Reports</div>
            <a href="<?php echo $basePath; ?>reports/" class="nav-item <?php echo $currentDir === 'reports' && $currentPage === 'index' ? 'active' : ''; ?>">
                <i class="icon-chart"></i>
                Reports
            </a>
            <a href="<?php echo $basePath; ?>reports/standings.php" class="nav-item <?php echo $currentDir === 'reports' && $currentPage === 'standings' ? 'active' : ''; ?>">
                <i class="icon-trophy"></i>
                Standings
            </a>
            <a href="<?php echo $basePath; ?>system_health.php" class="nav-item <?php echo $currentPage === 'system_health' ? 'active' : ''; ?>">
                <i class="icon-heart"></i>
                System Health
            </a>
        </div>
        <?php endif; ?>
        

        
        <?php if ($currentUser['role'] === 'super_admin'): ?>
        <div class="nav-section">
            <div class="nav-section-title">System</div>
            <a href="<?php echo $basePath; ?>users/" class="nav-item <?php echo $currentDir === 'users' ? 'active' : ''; ?>">
                <i class="icon-admin"></i>
                Admin Users
            </a>
            <a href="<?php echo $basePath; ?>settings/" class="nav-item <?php echo $currentDir === 'settings' ? 'active' : ''; ?>">
                <i class="icon-settings"></i>
                Settings
            </a>
            <a href="<?php echo $basePath; ?>backup/" class="nav-item <?php echo $currentDir === 'backup' ? 'active' : ''; ?>">
                <i class="icon-database"></i>
                Backup
            </a>
            <a href="<?php echo $basePath; ?>logs/" class="nav-item <?php echo $currentDir === 'logs' ? 'active' : ''; ?>">
                <i class="icon-file"></i>
                Activity Logs
            </a>
        </div>
        <?php endif; ?>
    </nav>
    
    <div class="sidebar-footer">
        <div class="sidebar-user">
            <div class="user-avatar">
                <?php echo strtoupper(substr($currentUser['full_name'], 0, 1)); ?>
            </div>
            <div class="user-info">
                <div class="user-name"><?php echo htmlspecialchars($currentUser['full_name']); ?></div>
                <div class="user-role"><?php echo ucfirst(str_replace('_', ' ', $currentUser['role'])); ?></div>
            </div>
        </div>
    </div>
</aside>
