<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Public Homepage
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Get current event
$currentEvent = fetchOne("
    SELECT * FROM events 
    WHERE status IN ('upcoming', 'ongoing') 
    ORDER BY start_date ASC 
    LIMIT 1
");

// Get statistics
$totalDepartments = fetchOne("SELECT COUNT(*) as count FROM departments WHERE status = 'active'")['count'];
$totalSports = fetchOne("SELECT COUNT(*) as count FROM sports WHERE status = 'active'")['count'];
$totalMatches = 0;
$ongoingMatches = 0;

if ($currentEvent) {
    $totalMatches = fetchOne("SELECT COUNT(*) as count FROM matches WHERE event_id = ?", [$currentEvent['event_id']])['count'];
    $ongoingMatches = fetchOne("SELECT COUNT(*) as count FROM matches WHERE event_id = ? AND status = 'ongoing'", [$currentEvent['event_id']])['count'];
}

// Get recent results (last 5 completed matches)
$recentResults = fetchAll("
    SELECT m.match_id, m.match_date, m.match_time, m.status,
           s.name as sport_name, v.name as venue_name,
           GROUP_CONCAT(
               CONCAT(d.abbreviation, ': ', mp.participant_name, 
                      CASE WHEN sc.points IS NOT NULL THEN CONCAT(' (', sc.points, ')') ELSE '' END)
               ORDER BY sc.points DESC SEPARATOR ' vs '
           ) as match_result
    FROM matches m
    JOIN sports s ON m.sport_id = s.sport_id
    LEFT JOIN venues v ON m.venue_id = v.venue_id
    LEFT JOIN match_participants mp ON m.match_id = mp.match_id
    LEFT JOIN departments d ON mp.dept_id = d.dept_id
    LEFT JOIN scores sc ON mp.participant_id = sc.participant_id AND sc.is_final = 1
    WHERE m.status = 'completed'
    GROUP BY m.match_id
    ORDER BY m.match_date DESC, m.match_time DESC
    LIMIT 5
");

// Get upcoming matches (next 5)
$upcomingMatches = fetchAll("
    SELECT m.match_id, m.match_date, m.match_time, m.status,
           s.name as sport_name, v.name as venue_name,
           GROUP_CONCAT(CONCAT(d.abbreviation, ': ', mp.participant_name) SEPARATOR ' vs ') as participants
    FROM matches m
    JOIN sports s ON m.sport_id = s.sport_id
    LEFT JOIN venues v ON m.venue_id = v.venue_id
    LEFT JOIN match_participants mp ON m.match_id = mp.match_id
    LEFT JOIN departments d ON mp.dept_id = d.dept_id
    WHERE m.status = 'scheduled' AND m.match_date >= CURDATE()
    GROUP BY m.match_id
    ORDER BY m.match_date ASC, m.match_time ASC
    LIMIT 5
");

// Get top 3 departments
$topDepartments = fetchAll("
    SELECT d.name, d.abbreviation, d.color_code,
           COALESCE(ds.total_points, 0) as total_points,
           COALESCE(ds.medals_gold, 0) as gold,
           COALESCE(ds.medals_silver, 0) as silver,
           COALESCE(ds.medals_bronze, 0) as bronze
    FROM departments d
    LEFT JOIN department_standings ds ON d.dept_id = ds.dept_id
    WHERE d.status = 'active'
    ORDER BY ds.total_points DESC, ds.medals_gold DESC
    LIMIT 3
");

// Get latest announcements
$announcements = fetchAll("
    SELECT title, content, date_posted, priority
    FROM announcements
    WHERE status = 'active' AND (expires_at IS NULL OR expires_at > NOW())
    ORDER BY priority DESC, date_posted DESC
    LIMIT 3
");
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - Home</title>
    <meta name="description" content="Samar College Intramurals Management System - Live scores, schedules, and standings for college sports events.">
    <meta name="keywords" content="Samar College, intramurals, sports, competition, live scores, schedule">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo APP_URL; ?>">
    <meta property="og:title" content="<?php echo APP_NAME; ?>">
    <meta property="og:description" content="Live scores, schedules, and standings for Samar College intramural sports.">
    <meta property="og:image" content="<?php echo APP_URL; ?>/assets/images/og-image.jpg">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="<?php echo APP_URL; ?>">
    <meta property="twitter:title" content="<?php echo APP_NAME; ?>">
    <meta property="twitter:description" content="Live scores, schedules, and standings for Samar College intramural sports.">
    <meta property="twitter:image" content="<?php echo APP_URL; ?>/assets/images/og-image.jpg">
    
    <link rel="stylesheet" href="../assets/css/index.css">
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
    <link rel="manifest" href="../manifest.json">
    <meta name="theme-color" content="#14b8a6">
</head>
<body>
    <!-- Header -->
    <header class="site-header">
        <div class="container">
            <div class="header-content">
                <a href="index.php" class="site-logo">
                    <img src="../assets/images/logo.png" alt="SCIMS Logo" onerror="this.style.display='none'">
                    <h1>SCIMS</h1>
                </a>
                
                <nav class="main-nav">
                    <ul class="nav-menu">
                        <li class="nav-item"><a href="index.php" class="active">Home</a></li>
                        <li class="nav-item"><a href="schedule.php">Schedule</a></li>
                        <li class="nav-item"><a href="standings.php">Standings</a></li>
                        <li class="nav-item"><a href="results.php">Results</a></li>
                    </ul>
                    
                    <button class="mobile-menu-toggle" aria-label="Toggle mobile menu">
                        ☰
                    </button>
                </nav>
            </div>
        </div>
    </header>
    
    <!-- Live Ticker -->
    <?php if ($ongoingMatches > 0): ?>
    <div class="live-ticker">
        <div class="container">
            <div class="ticker-content" data-live-update="/api/live-ticker.php">
                <span class="ticker-item">🔴 LIVE: Loading live scores...</span>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="hero-content">
                <?php if ($currentEvent): ?>
                    <h1><?php echo htmlspecialchars($currentEvent['name']); ?></h1>
                    <p>
                        <?php echo formatDate($currentEvent['start_date']); ?> - 
                        <?php echo formatDate($currentEvent['end_date']); ?>
                    </p>
                    
                    <?php if ($currentEvent['status'] === 'upcoming'): ?>
                        <div class="countdown" data-target="<?php echo $currentEvent['start_date']; ?> 08:00:00">
                            <!-- Countdown will be populated by JavaScript -->
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <h1>Samar College Intramurals</h1>
                    <p>Excellence in Sports, Unity in Competition</p>
                <?php endif; ?>
                
                <div class="hero-stats">
                    <div class="hero-stat">
                        <h3><?php echo $totalDepartments; ?></h3>
                        <p>Participating Departments</p>
                    </div>
                    <div class="hero-stat">
                        <h3><?php echo $totalSports; ?></h3>
                        <p>Sports Events</p>
                    </div>
                    <div class="hero-stat">
                        <h3><?php echo $totalMatches; ?></h3>
                        <p>Total Matches</p>
                    </div>
                    <div class="hero-stat">
                        <h3><?php echo $ongoingMatches; ?></h3>
                        <p>Live Matches</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Main Content -->
    <main>
        <!-- Announcements Section -->
        <?php if (!empty($announcements)): ?>
        <section class="section">
            <div class="container">
                <div class="section-header">
                    <h2>Latest Announcements</h2>
                </div>
                
                <div class="card-grid">
                    <?php foreach ($announcements as $announcement): ?>
                    <div class="card">
                        <div class="card-header">
                            <h3><?php echo htmlspecialchars($announcement['title']); ?></h3>
                            <span class="priority-badge priority-<?php echo $announcement['priority']; ?>">
                                <?php echo ucfirst($announcement['priority']); ?>
                            </span>
                        </div>
                        <div class="card-content">
                            <p><?php echo nl2br(htmlspecialchars($announcement['content'])); ?></p>
                        </div>
                        <div class="card-footer">
                            <small><?php echo formatDateTime($announcement['date_posted']); ?></small>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>
        <?php endif; ?>
        
        <!-- Quick Stats Section -->
        <section class="section" style="background: var(--gray-50);">
            <div class="container">
                <div class="section-header">
                    <h2>Current Standings</h2>
                    <p>Top performing departments in the current event</p>
                </div>
                
                <?php if (!empty($topDepartments)): ?>
                <div class="card-grid">
                    <?php foreach ($topDepartments as $index => $dept): ?>
                    <div class="card">
                        <div class="card-header">
                            <div style="display: flex; align-items: center; gap: 1rem;">
                                <div class="rank-badge rank-<?php echo $index + 1; ?>">
                                    <?php echo $index + 1; ?>
                                </div>
                                <div>
                                    <h3><?php echo htmlspecialchars($dept['abbreviation']); ?></h3>
                                    <small><?php echo htmlspecialchars($dept['name']); ?></small>
                                </div>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="dept-stats">
                                <div class="stat">
                                    <strong><?php echo number_format($dept['total_points'], 1); ?></strong>
                                    <span>Points</span>
                                </div>
                                <div class="medals">
                                    <span class="medal gold"><?php echo $dept['gold']; ?></span>
                                    <span class="medal silver"><?php echo $dept['silver']; ?></span>
                                    <span class="medal bronze"><?php echo $dept['bronze']; ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <div style="text-align: center; margin-top: 2rem;">
                    <a href="standings.php" class="btn btn-primary">View Full Standings</a>
                </div>
                <?php else: ?>
                <p style="text-align: center; color: var(--gray-500);">No standings data available yet.</p>
                <?php endif; ?>
            </div>
        </section>
        
        <!-- Recent Results & Upcoming Matches -->
        <section class="section">
            <div class="container">
                <div class="card-grid">
                    <!-- Recent Results -->
                    <div class="card">
                        <div class="card-header">
                            <h2>Recent Results</h2>
                            <a href="results.php" class="btn btn-outline">View All</a>
                        </div>
                        <div class="card-content">
                            <?php if (!empty($recentResults)): ?>
                                <?php foreach ($recentResults as $result): ?>
                                <div class="match-result">
                                    <h4><?php echo htmlspecialchars($result['sport_name']); ?></h4>
                                    <p><?php echo htmlspecialchars($result['match_result'] ?? 'Result pending'); ?></p>
                                    <small>
                                        <?php echo formatDate($result['match_date']); ?> at 
                                        <?php echo formatTime($result['match_time']); ?>
                                        <?php if ($result['venue_name']): ?>
                                            - <?php echo htmlspecialchars($result['venue_name']); ?>
                                        <?php endif; ?>
                                    </small>
                                </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p class="no-data">No recent results available.</p>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Upcoming Matches -->
                    <div class="card">
                        <div class="card-header">
                            <h2>Upcoming Matches</h2>
                            <a href="schedule.php" class="btn btn-outline">View Schedule</a>
                        </div>
                        <div class="card-content">
                            <?php if (!empty($upcomingMatches)): ?>
                                <?php foreach ($upcomingMatches as $match): ?>
                                <div class="match-preview">
                                    <h4><?php echo htmlspecialchars($match['sport_name']); ?></h4>
                                    <p><?php echo htmlspecialchars($match['participants'] ?? 'TBD'); ?></p>
                                    <small>
                                        <?php echo formatDate($match['match_date']); ?> at 
                                        <?php echo formatTime($match['match_time']); ?>
                                        <?php if ($match['venue_name']): ?>
                                            - <?php echo htmlspecialchars($match['venue_name']); ?>
                                        <?php endif; ?>
                                    </small>
                                </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <p class="no-data">No upcoming matches scheduled.</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
    
    <!-- Footer -->
    <footer class="site-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>SCIMS</h3>
                    <p>Samar College Intramurals Management System - Your gateway to live sports action and competition updates.</p>
                </div>
                
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="schedule.php">Match Schedule</a></li>
                        <li><a href="standings.php">Department Standings</a></li>
                        <li><a href="results.php">Match Results</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3>Contact</h3>
                    <ul>
                        <li>Samar College</li>
                        <li>Catbalogan City, Samar</li>
                        <li>Email: <EMAIL></li>
                        <li>Phone: (*************</li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; <?php echo date('Y'); ?> Samar College. All rights reserved. | Powered by SCIMS v<?php echo APP_VERSION; ?></p>
            </div>
        </div>
    </footer>
    
    <script src="../assets/js/main.js"></script>
</body>
</html>
