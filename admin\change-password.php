<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Change Password Page
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

declare(strict_types=1);

define('SCIMS_ACCESS', true);
require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// Require authentication
requireAuth();

$currentUser = getCurrentUser();
$message = '';
$messageType = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('Invalid security token');
        }
        
        $currentPassword = $_POST['current_password'] ?? '';
        $newPassword = $_POST['new_password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        
        // Validation
        if (empty($currentPassword)) {
            throw new Exception('Current password is required');
        }
        
        if (!password_verify($currentPassword, $currentUser['password_hash'])) {
            throw new Exception('Current password is incorrect');
        }
        
        if (empty($newPassword)) {
            throw new Exception('New password is required');
        }
        
        if (strlen($newPassword) < 8) {
            throw new Exception('New password must be at least 8 characters long');
        }
        
        // Check password complexity if required
        $requireComplexity = getSetting('require_password_complexity', '0') === '1';
        if ($requireComplexity) {
            if (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/', $newPassword)) {
                throw new Exception('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character');
            }
        }
        
        if ($newPassword !== $confirmPassword) {
            throw new Exception('Password confirmation does not match');
        }
        
        // Check if new password is different from current
        if (password_verify($newPassword, $currentUser['password_hash'])) {
            throw new Exception('New password must be different from current password');
        }
        
        // Update password
        $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);
        updateRecord('admin_users', [
            'password_hash' => $passwordHash,
            'updated_at' => date('Y-m-d H:i:s')
        ], 'admin_id = ?', [$currentUser['admin_id']]);
        
        logActivity('password_changed', 'Password changed successfully');
        
        $message = 'Password changed successfully';
        $messageType = 'success';
        
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get password policy settings
$minLength = (int)getSetting('password_min_length', '8');
$requireComplexity = getSetting('require_password_complexity', '0') === '1';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Change Password - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body class="admin-page">
    <?php include 'includes/header.php'; ?>
    <?php include 'includes/sidebar.php'; ?>
    
    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1>Change Password</h1>
                <nav class="breadcrumb">
                    <a href="dashboard.php">Dashboard</a>
                    <span>/</span>
                    <span>Profile</span>
                </nav>
            </div>
        </div>

        <!-- Profile Tab Navigation -->
        <div class="profile-tabs-container">
            <nav class="profile-tabs">
                <a href="profile.php" class="profile-tab">
                    <i class="icon-user"></i>
                    Profile Settings
                </a>
                <a href="change-password.php" class="profile-tab active">
                    <i class="icon-lock"></i>
                    Change Password
                </a>
            </nav>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?>">
                <i class="icon-<?php echo $messageType === 'success' ? 'check' : 'warning'; ?>"></i>
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <div class="change-password-container">
            <div class="password-card">
                <div class="card-header">
                    <h2><i class="icon-lock"></i> Change Your Password</h2>
                    <p>Ensure your account security by using a strong password</p>
                </div>
                
                <form method="POST" class="password-form" id="changePasswordForm">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <div class="form-group">
                        <label for="current_password">Current Password</label>
                        <div class="password-input-wrapper">
                            <input type="password" id="current_password" name="current_password" required>
                            <button type="button" class="password-toggle" data-target="current_password">
                                <i class="icon-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="new_password">New Password</label>
                        <div class="password-input-wrapper">
                            <input type="password" id="new_password" name="new_password" 
                                   required minlength="<?php echo $minLength; ?>">
                            <button type="button" class="password-toggle" data-target="new_password">
                                <i class="icon-eye"></i>
                            </button>
                        </div>
                        <div class="password-requirements">
                            <h4>Password Requirements:</h4>
                            <ul>
                                <li class="requirement" data-requirement="length">
                                    <i class="icon-circle"></i>
                                    At least <?php echo $minLength; ?> characters long
                                </li>
                                <?php if ($requireComplexity): ?>
                                <li class="requirement" data-requirement="uppercase">
                                    <i class="icon-circle"></i>
                                    At least one uppercase letter (A-Z)
                                </li>
                                <li class="requirement" data-requirement="lowercase">
                                    <i class="icon-circle"></i>
                                    At least one lowercase letter (a-z)
                                </li>
                                <li class="requirement" data-requirement="number">
                                    <i class="icon-circle"></i>
                                    At least one number (0-9)
                                </li>
                                <li class="requirement" data-requirement="special">
                                    <i class="icon-circle"></i>
                                    At least one special character (@$!%*?&)
                                </li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="confirm_password">Confirm New Password</label>
                        <div class="password-input-wrapper">
                            <input type="password" id="confirm_password" name="confirm_password" required>
                            <button type="button" class="password-toggle" data-target="confirm_password">
                                <i class="icon-eye"></i>
                            </button>
                        </div>
                        <div class="password-match-indicator" id="passwordMatch"></div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="icon-lock"></i>
                            Change Password
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- Security Tips -->
            <div class="security-tips">
                <h3><i class="icon-shield"></i> Password Security Tips</h3>
                <ul>
                    <li>Use a unique password that you don't use elsewhere</li>
                    <li>Include a mix of letters, numbers, and special characters</li>
                    <li>Avoid using personal information like names or birthdays</li>
                    <li>Consider using a password manager</li>
                    <li>Change your password regularly</li>
                    <li>Never share your password with others</li>
                </ul>
            </div>
        </div>
    </main>
    
    <script src="../assets/js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Profile tab navigation enhancement
            const profileTabs = document.querySelectorAll('.profile-tab');
            profileTabs.forEach(tab => {
                tab.addEventListener('click', function(e) {
                    // Add loading state for better UX
                    if (!this.classList.contains('active')) {
                        this.style.opacity = '0.7';
                        this.innerHTML += ' <i class="icon-spinner" style="animation: spin 1s linear infinite;"></i>';
                    }
                });
            });

            // Add CSS for spinner animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
            const newPasswordInput = document.getElementById('new_password');
            const confirmPasswordInput = document.getElementById('confirm_password');
            const passwordMatchIndicator = document.getElementById('passwordMatch');
            const submitBtn = document.getElementById('submitBtn');
            const requireComplexity = <?php echo $requireComplexity ? 'true' : 'false'; ?>;
            const minLength = <?php echo $minLength; ?>;
            
            // Password toggle functionality
            document.querySelectorAll('.password-toggle').forEach(button => {
                button.addEventListener('click', function() {
                    const targetId = this.dataset.target;
                    const targetInput = document.getElementById(targetId);
                    const icon = this.querySelector('i');
                    
                    if (targetInput.type === 'password') {
                        targetInput.type = 'text';
                        icon.className = 'icon-eye-off';
                    } else {
                        targetInput.type = 'password';
                        icon.className = 'icon-eye';
                    }
                });
            });
            
            // Password requirements validation
            function validatePassword(password) {
                const requirements = {
                    length: password.length >= minLength,
                    uppercase: /[A-Z]/.test(password),
                    lowercase: /[a-z]/.test(password),
                    number: /\d/.test(password),
                    special: /[@$!%*?&]/.test(password)
                };
                
                // Update requirement indicators
                Object.keys(requirements).forEach(req => {
                    const element = document.querySelector(`[data-requirement="${req}"]`);
                    if (element) {
                        const icon = element.querySelector('i');
                        if (requirements[req]) {
                            element.classList.add('met');
                            icon.className = 'icon-check';
                        } else {
                            element.classList.remove('met');
                            icon.className = 'icon-circle';
                        }
                    }
                });
                
                // Check if all required validations pass
                let isValid = requirements.length;
                if (requireComplexity) {
                    isValid = isValid && requirements.uppercase && requirements.lowercase && 
                             requirements.number && requirements.special;
                }
                
                return isValid;
            }
            
            // Password match validation
            function validatePasswordMatch() {
                const newPassword = newPasswordInput.value;
                const confirmPassword = confirmPasswordInput.value;
                
                if (confirmPassword) {
                    if (newPassword === confirmPassword) {
                        passwordMatchIndicator.innerHTML = '<i class="icon-check"></i> Passwords match';
                        passwordMatchIndicator.className = 'password-match-indicator match';
                        return true;
                    } else {
                        passwordMatchIndicator.innerHTML = '<i class="icon-x"></i> Passwords do not match';
                        passwordMatchIndicator.className = 'password-match-indicator no-match';
                        return false;
                    }
                } else {
                    passwordMatchIndicator.innerHTML = '';
                    passwordMatchIndicator.className = 'password-match-indicator';
                    return false;
                }
            }
            
            // Real-time validation
            newPasswordInput.addEventListener('input', function() {
                validatePassword(this.value);
                validatePasswordMatch();
            });
            
            confirmPasswordInput.addEventListener('input', validatePasswordMatch);
            
            // Form submission validation
            document.getElementById('changePasswordForm').addEventListener('submit', function(e) {
                const newPassword = newPasswordInput.value;
                const isPasswordValid = validatePassword(newPassword);
                const isMatchValid = validatePasswordMatch();
                
                if (!isPasswordValid || !isMatchValid) {
                    e.preventDefault();
                    alert('Please ensure your password meets all requirements and passwords match');
                }
            });
        });
    </script>
</body>
</html>
