/**
 * Samar College Intramurals Management System (SCIMS)
 * Admin Panel JavaScript
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

'use strict';

// Global SCIMS Admin object
const SCIMSAdmin = {
    // Configuration
    config: {
        updateInterval: 30000, // 30 seconds
        sessionTimeout: 1800000, // 30 minutes
        maxRetries: 3,
        retryDelay: 1000
    },
    
    // State management
    state: {
        isOnline: navigator.onLine,
        lastActivity: Date.now(),
        retryCount: 0
    },
    
    // Initialize the admin panel
    init() {
        this.setupEventListeners();
        this.initializeComponents();
        this.startActivityMonitoring();
        this.checkOnlineStatus();
        console.log('SCIMS Admin initialized');
    },
    
    // Setup global event listeners
    setupEventListeners() {
        // User menu toggle
        const userMenuToggle = document.querySelector('.user-menu-toggle');
        const userMenuDropdown = document.querySelector('.user-menu-dropdown');
        
        if (userMenuToggle && userMenuDropdown) {
            userMenuToggle.addEventListener('click', (e) => {
                e.stopPropagation();
                userMenuDropdown.classList.toggle('show');
            });
            
            // Close dropdown when clicking outside
            document.addEventListener('click', () => {
                userMenuDropdown.classList.remove('show');
            });
        }
        
        // Mobile menu toggle
        const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
        const sidebar = document.querySelector('.admin-sidebar');
        
        if (mobileMenuToggle && sidebar) {
            mobileMenuToggle.addEventListener('click', () => {
                sidebar.classList.toggle('mobile-open');
            });
        }
        
        // Form validation
        this.setupFormValidation();
        
        // AJAX form handling
        this.setupAjaxForms();
        
        // Activity tracking
        this.trackUserActivity();
        
        // Online/offline status
        window.addEventListener('online', () => this.handleOnlineStatus(true));
        window.addEventListener('offline', () => this.handleOnlineStatus(false));
    },
    
    // Initialize components
    initializeComponents() {
        // Initialize data tables
        this.initializeDataTables();
        
        // Initialize modals
        this.initializeModals();
        
        // Initialize tooltips
        this.initializeTooltips();
        
        // Initialize date pickers
        this.initializeDatePickers();
        
        // Initialize live updates
        this.initializeLiveUpdates();
    },
    
    // Setup form validation
    setupFormValidation() {
        const forms = document.querySelectorAll('form[data-validate]');
        
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                if (!this.validateForm(form)) {
                    e.preventDefault();
                    return false;
                }
            });
            
            // Real-time validation
            const inputs = form.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('blur', () => {
                    this.validateField(input);
                });
            });
        });
    },
    
    // Validate form
    validateForm(form) {
        let isValid = true;
        const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
        
        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isValid = false;
            }
        });
        
        return isValid;
    },
    
    // Validate individual field
    validateField(field) {
        const value = field.value.trim();
        const type = field.type;
        let isValid = true;
        let message = '';
        
        // Required field validation
        if (field.hasAttribute('required') && !value) {
            isValid = false;
            message = 'This field is required';
        }
        
        // Email validation
        else if (type === 'email' && value && !this.isValidEmail(value)) {
            isValid = false;
            message = 'Please enter a valid email address';
        }
        
        // Password validation
        else if (field.name === 'password' && value && !this.isStrongPassword(value)) {
            isValid = false;
            message = 'Password must be at least 8 characters with uppercase, lowercase, number, and special character';
        }
        
        // Phone validation
        else if (field.name === 'phone' && value && !this.isValidPhone(value)) {
            isValid = false;
            message = 'Please enter a valid phone number';
        }
        
        this.showFieldValidation(field, isValid, message);
        return isValid;
    },
    
    // Show field validation result
    showFieldValidation(field, isValid, message) {
        const errorElement = field.parentNode.querySelector('.field-error');
        
        if (errorElement) {
            errorElement.remove();
        }
        
        field.classList.remove('error', 'success');
        
        if (!isValid) {
            field.classList.add('error');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'field-error';
            errorDiv.textContent = message;
            field.parentNode.appendChild(errorDiv);
        } else if (field.value.trim()) {
            field.classList.add('success');
        }
    },
    
    // Setup AJAX forms
    setupAjaxForms() {
        const ajaxForms = document.querySelectorAll('form[data-ajax]');
        
        ajaxForms.forEach(form => {
            form.addEventListener('submit', async (e) => {
                e.preventDefault();
                await this.handleAjaxForm(form);
            });
        });
    },
    
    // Handle AJAX form submission
    async handleAjaxForm(form) {
        const submitButton = form.querySelector('button[type="submit"]');
        const originalText = submitButton.textContent;
        
        try {
            // Show loading state
            submitButton.disabled = true;
            submitButton.textContent = 'Processing...';
            
            const formData = new FormData(form);
            const response = await fetch(form.action, {
                method: form.method || 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showAlert(result.message || 'Operation completed successfully', 'success');
                
                // Handle redirect
                if (result.redirect) {
                    setTimeout(() => {
                        window.location.href = result.redirect;
                    }, 1000);
                }
                
                // Reset form if specified
                if (result.reset) {
                    form.reset();
                }
                
                // Refresh data if specified
                if (result.refresh) {
                    this.refreshData(result.refresh);
                }
            } else {
                this.showAlert(result.message || 'An error occurred', 'error');
            }
            
        } catch (error) {
            console.error('AJAX form error:', error);
            this.showAlert('Network error. Please try again.', 'error');
        } finally {
            // Restore button state
            submitButton.disabled = false;
            submitButton.textContent = originalText;
        }
    },
    
    // Initialize data tables
    initializeDataTables() {
        const tables = document.querySelectorAll('.data-table');
        
        tables.forEach(table => {
            this.enhanceTable(table);
        });
    },
    
    // Enhance table with sorting and filtering
    enhanceTable(table) {
        const headers = table.querySelectorAll('th[data-sortable]');
        
        headers.forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', () => {
                this.sortTable(table, header);
            });
        });
    },
    
    // Sort table
    sortTable(table, header) {
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        const columnIndex = Array.from(header.parentNode.children).indexOf(header);
        const isAscending = !header.classList.contains('sort-asc');
        
        // Clear previous sort indicators
        table.querySelectorAll('th').forEach(th => {
            th.classList.remove('sort-asc', 'sort-desc');
        });
        
        // Add sort indicator
        header.classList.add(isAscending ? 'sort-asc' : 'sort-desc');
        
        // Sort rows
        rows.sort((a, b) => {
            const aValue = a.children[columnIndex].textContent.trim();
            const bValue = b.children[columnIndex].textContent.trim();
            
            // Try to parse as numbers
            const aNum = parseFloat(aValue);
            const bNum = parseFloat(bValue);
            
            if (!isNaN(aNum) && !isNaN(bNum)) {
                return isAscending ? aNum - bNum : bNum - aNum;
            }
            
            // String comparison
            return isAscending ? 
                aValue.localeCompare(bValue) : 
                bValue.localeCompare(aValue);
        });
        
        // Reorder rows
        rows.forEach(row => tbody.appendChild(row));
    },
    
    // Initialize modals
    initializeModals() {
        const modalTriggers = document.querySelectorAll('[data-modal]');
        
        modalTriggers.forEach(trigger => {
            trigger.addEventListener('click', (e) => {
                e.preventDefault();
                const modalId = trigger.getAttribute('data-modal');
                this.openModal(modalId);
            });
        });
        
        // Close modal handlers
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-overlay')) {
                this.closeModal(e.target.querySelector('.modal'));
            }
        });
        
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal.show');
                if (openModal) {
                    this.closeModal(openModal);
                }
            }
        });
    },
    
    // Open modal
    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('show');
            document.body.style.overflow = 'hidden';
            
            // Focus first input
            const firstInput = modal.querySelector('input, select, textarea');
            if (firstInput) {
                setTimeout(() => firstInput.focus(), 100);
            }
        }
    },
    
    // Close modal
    closeModal(modal) {
        if (modal) {
            modal.classList.remove('show');
            document.body.style.overflow = '';
        }
    },
    
    // Initialize tooltips
    initializeTooltips() {
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        
        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                this.showTooltip(e.target);
            });
            
            element.addEventListener('mouseleave', () => {
                this.hideTooltip();
            });
        });
    },
    
    // Show tooltip
    showTooltip(element) {
        const text = element.getAttribute('data-tooltip');
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = text;
        
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
        
        setTimeout(() => tooltip.classList.add('show'), 10);
    },
    
    // Hide tooltip
    hideTooltip() {
        const tooltip = document.querySelector('.tooltip');
        if (tooltip) {
            tooltip.remove();
        }
    },
    
    // Initialize date pickers
    initializeDatePickers() {
        const dateInputs = document.querySelectorAll('input[type="date"], input[type="datetime-local"]');
        
        dateInputs.forEach(input => {
            // Set minimum date to today for future events
            if (input.hasAttribute('data-min-today')) {
                const today = new Date().toISOString().split('T')[0];
                input.min = today;
            }
        });
    },
    
    // Initialize live updates
    initializeLiveUpdates() {
        if (document.querySelector('[data-live-update]')) {
            this.startLiveUpdates();
        }
    },
    
    // Start live updates
    startLiveUpdates() {
        setInterval(() => {
            if (this.state.isOnline && !document.hidden) {
                this.updateLiveData();
            }
        }, this.config.updateInterval);
    },
    
    // Update live data
    async updateLiveData() {
        const liveElements = document.querySelectorAll('[data-live-update]');
        
        for (const element of liveElements) {
            const endpoint = element.getAttribute('data-live-update');
            try {
                const response = await fetch(endpoint);
                const data = await response.json();
                
                if (data.success) {
                    this.updateElement(element, data.content);
                }
            } catch (error) {
                console.error('Live update error:', error);
            }
        }
    },
    
    // Update element content
    updateElement(element, content) {
        if (element.innerHTML !== content) {
            element.innerHTML = content;
            element.classList.add('updated');
            setTimeout(() => element.classList.remove('updated'), 1000);
        }
    },
    
    // Utility functions
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },
    
    isStrongPassword(password) {
        return password.length >= 8 &&
               /[A-Z]/.test(password) &&
               /[a-z]/.test(password) &&
               /[0-9]/.test(password) &&
               /[^A-Za-z0-9]/.test(password);
    },
    
    isValidPhone(phone) {
        const phoneRegex = /^(\+63|0)?[0-9]{10}$/;
        return phoneRegex.test(phone.replace(/\s/g, ''));
    },
    
    // Show alert message
    showAlert(message, type = 'info', duration = 5000) {
        const alertContainer = document.querySelector('.alert-container') || this.createAlertContainer();
        
        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.innerHTML = `
            <i class="icon-${type}"></i>
            <span>${message}</span>
            <button class="alert-close" onclick="this.parentElement.remove()">×</button>
        `;
        
        alertContainer.appendChild(alert);
        
        // Auto-remove after duration
        setTimeout(() => {
            if (alert.parentElement) {
                alert.remove();
            }
        }, duration);
    },
    
    // Create alert container
    createAlertContainer() {
        const container = document.createElement('div');
        container.className = 'alert-container';
        document.body.appendChild(container);
        return container;
    },
    
    // Track user activity
    trackUserActivity() {
        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
        
        events.forEach(event => {
            document.addEventListener(event, () => {
                this.state.lastActivity = Date.now();
            }, { passive: true });
        });
    },
    
    // Start activity monitoring
    startActivityMonitoring() {
        setInterval(() => {
            const inactiveTime = Date.now() - this.state.lastActivity;
            
            if (inactiveTime > this.config.sessionTimeout) {
                this.handleSessionTimeout();
            }
        }, 60000); // Check every minute
    },
    
    // Handle session timeout
    handleSessionTimeout() {
        this.showAlert('Your session has expired. Please log in again.', 'warning');
        setTimeout(() => {
            window.location.href = '/admin/index.php';
        }, 3000);
    },
    
    // Handle online/offline status
    handleOnlineStatus(isOnline) {
        this.state.isOnline = isOnline;
        
        if (isOnline) {
            this.showAlert('Connection restored', 'success', 3000);
        } else {
            this.showAlert('Connection lost. Some features may not work.', 'warning');
        }
    },
    
    // Check online status
    checkOnlineStatus() {
        this.state.isOnline = navigator.onLine;
    },
    
    // Refresh data
    refreshData(target) {
        const element = document.querySelector(target);
        if (element) {
            // Add loading state
            element.classList.add('loading');
            
            // Simulate refresh (implement actual refresh logic)
            setTimeout(() => {
                element.classList.remove('loading');
                location.reload();
            }, 1000);
        }
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    SCIMSAdmin.init();
});

// Export for global access
window.SCIMSAdmin = SCIMSAdmin;
