<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Authentication Functions
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

// Prevent direct access
if (!defined('SCIMS_ACCESS')) {
    die('Direct access not permitted');
}

/**
 * Authenticate user login
 */
function authenticateUser($username, $password, $rememberMe = false) {
    try {
        // Rate limiting check
        $ipAddress = $_SERVER['REMOTE_ADDR'];
        $rateLimitIdentifier = "login_" . $ipAddress;

        if (!checkRateLimit($rateLimitIdentifier, 5, 300)) { // 5 attempts per 5 minutes
            logSecurityEvent('rate_limit_exceeded', "Rate limit exceeded for IP: {$ipAddress}", 'high');
            throw new Exception('Too many login attempts. Please try again later.');
        }

        // Check if user is locked out
        if (isUserLockedOut($username)) {
            logSecurityEvent('account_lockout_attempt', "Login attempt on locked account: {$username}", 'high');
            throw new Exception('Account is temporarily locked due to multiple failed login attempts');
        }

        // Get user from database
        $sql = "SELECT admin_id, username, password_hash, email, full_name, role, status, failed_login_attempts
                FROM admin_users
                WHERE username = :username AND status = 'active'";
        $user = fetchOne($sql, ['username' => $username]);

        if (!$user) {
            recordFailedLogin($username);
            logSecurityEvent('invalid_username', "Login attempt with invalid username: {$username}", 'medium');
            throw new Exception('Invalid username or password');
        }

        // Verify password
        if (!password_verify($password, $user['password_hash'])) {
            recordFailedLogin($username);
            logSecurityEvent('invalid_password', "Invalid password for username: {$username}", 'medium');
            throw new Exception('Invalid username or password');
        }

        // Reset failed login attempts on successful login
        resetFailedLoginAttempts($username);

        // Update last login
        updateLastLogin($user['admin_id']);

        // Set session variables with enhanced security
        session_regenerate_id(true);
        $_SESSION['admin_id'] = $user['admin_id'];
        $_SESSION['admin_username'] = $user['username'];
        $_SESSION['admin_email'] = $user['email'];
        $_SESSION['admin_name'] = $user['full_name'];
        $_SESSION['admin_role'] = $user['role'];
        $_SESSION['login_time'] = time();
        $_SESSION['ip_address'] = $_SERVER['REMOTE_ADDR'];
        $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'];
        $_SESSION['created_at'] = time();
        $_SESSION['last_regeneration'] = time();

        // Handle remember me functionality
        if ($rememberMe) {
            createRememberToken($user['admin_id']);
        }

        // Log successful login
        logActivity('login', "User {$username} logged in successfully" . ($rememberMe ? ' (with remember me)' : ''));
        logSecurityEvent('successful_login', "Successful login for user: {$username}", 'low');

        return true;

    } catch (Exception $e) {
        logActivity('login_failed', "Failed login attempt for username: {$username} - " . $e->getMessage());
        throw $e;
    }
}

/**
 * Check if user is locked out
 */
function isUserLockedOut($username) {
    $sql = "SELECT failed_login_attempts, locked_until 
            FROM admin_users 
            WHERE username = :username";
    $user = fetchOne($sql, ['username' => $username]);
    
    if (!$user) return false;
    
    // Check if user is currently locked
    if ($user['locked_until'] && strtotime($user['locked_until']) > time()) {
        return true;
    }
    
    // Check if user has exceeded max attempts
    if ($user['failed_login_attempts'] >= MAX_LOGIN_ATTEMPTS) {
        // Lock the user
        lockUser($username);
        return true;
    }
    
    return false;
}

/**
 * Record failed login attempt
 */
function recordFailedLogin($username) {
    $sql = "UPDATE admin_users 
            SET failed_login_attempts = failed_login_attempts + 1 
            WHERE username = :username";
    executeQuery($sql, ['username' => $username]);
    
    // Check if user should be locked
    $user = fetchOne("SELECT failed_login_attempts FROM admin_users WHERE username = :username", 
                     ['username' => $username]);
    
    if ($user && $user['failed_login_attempts'] >= MAX_LOGIN_ATTEMPTS) {
        lockUser($username);
    }
}

/**
 * Lock user account
 */
function lockUser($username) {
    $lockUntil = date('Y-m-d H:i:s', time() + LOCKOUT_DURATION);
    $sql = "UPDATE admin_users 
            SET locked_until = :locked_until 
            WHERE username = :username";
    executeQuery($sql, ['locked_until' => $lockUntil, 'username' => $username]);
    
    logActivity('account_locked', "Account locked for username: {$username}");
}

/**
 * Reset failed login attempts
 */
function resetFailedLoginAttempts($username) {
    $sql = "UPDATE admin_users 
            SET failed_login_attempts = 0, locked_until = NULL 
            WHERE username = :username";
    executeQuery($sql, ['username' => $username]);
}

/**
 * Update last login timestamp
 */
function updateLastLogin($adminId) {
    $sql = "UPDATE admin_users 
            SET last_login = CURRENT_TIMESTAMP 
            WHERE admin_id = :admin_id";
    executeQuery($sql, ['admin_id' => $adminId]);
}

/**
 * Logout user
 */
function logoutUser($clearRememberToken = false) {
    if (isLoggedIn()) {
        $username = $_SESSION['admin_username'];
        $adminId = $_SESSION['admin_id'];

        // Clear remember token if requested or if cookie exists
        if ($clearRememberToken || isset($_COOKIE['remember_token'])) {
            deleteRememberToken(null, $adminId);
        }

        logActivity('logout', "User {$username} logged out");

        // Clear session
        session_unset();
        session_destroy();

        // Start new session
        session_start();
        session_regenerate_id(true);
    }
}

/**
 * Validate session security
 */
function validateSession() {
    if (!isLoggedIn()) {
        return false;
    }
    
    // Check session timeout
    if (isset($_SESSION['last_activity']) && 
        (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT)) {
        logoutUser();
        return false;
    }
    
    // Validate IP address (optional - can be disabled for dynamic IPs)
    if (isset($_SESSION['ip_address']) && 
        $_SESSION['ip_address'] !== $_SERVER['REMOTE_ADDR']) {
        logActivity('session_hijack_attempt', 
                   "IP mismatch - Session: {$_SESSION['ip_address']}, Current: {$_SERVER['REMOTE_ADDR']}");
        logoutUser();
        return false;
    }
    
    // Validate user agent
    if (isset($_SESSION['user_agent']) && 
        $_SESSION['user_agent'] !== $_SERVER['HTTP_USER_AGENT']) {
        logActivity('session_hijack_attempt', 'User agent mismatch');
        logoutUser();
        return false;
    }
    
    // Update last activity
    $_SESSION['last_activity'] = time();
    
    return true;
}

/**
 * Require authentication
 */
function requireAuth() {
    if (!validateSession()) {
        redirect('/admin/index.php', 'Please log in to continue', 'warning');
    }
}

/**
 * Change user password
 */
function changePassword($adminId, $currentPassword, $newPassword) {
    // Get current password hash
    $sql = "SELECT password_hash FROM admin_users WHERE admin_id = :admin_id";
    $user = fetchOne($sql, ['admin_id' => $adminId]);
    
    if (!$user) {
        throw new Exception('User not found');
    }
    
    // Verify current password
    if (!password_verify($currentPassword, $user['password_hash'])) {
        throw new Exception('Current password is incorrect');
    }
    
    // Validate new password strength
    if (!isStrongPassword($newPassword)) {
        throw new Exception('New password does not meet security requirements');
    }
    
    // Hash new password
    $newPasswordHash = password_hash($newPassword, PASSWORD_DEFAULT);
    
    // Update password
    $sql = "UPDATE admin_users 
            SET password_hash = :password_hash, updated_at = CURRENT_TIMESTAMP 
            WHERE admin_id = :admin_id";
    executeQuery($sql, ['password_hash' => $newPasswordHash, 'admin_id' => $adminId]);
    
    logActivity('password_changed', "Password changed for admin ID: {$adminId}");
    
    return true;
}

/**
 * Validate password strength
 */
function isStrongPassword($password) {
    // Minimum 8 characters
    if (strlen($password) < 8) return false;
    
    // At least one uppercase letter
    if (!preg_match('/[A-Z]/', $password)) return false;
    
    // At least one lowercase letter
    if (!preg_match('/[a-z]/', $password)) return false;
    
    // At least one number
    if (!preg_match('/[0-9]/', $password)) return false;
    
    // At least one special character
    if (!preg_match('/[^A-Za-z0-9]/', $password)) return false;
    
    return true;
}

/**
 * Generate password reset token
 */
function generatePasswordResetToken($email) {
    // Check if user exists
    $sql = "SELECT admin_id, username FROM admin_users WHERE email = :email AND status = 'active'";
    $user = fetchOne($sql, ['email' => $email]);
    
    if (!$user) {
        throw new Exception('Email address not found');
    }
    
    // Generate token
    $token = bin2hex(random_bytes(32));
    $expires = date('Y-m-d H:i:s', time() + 3600); // 1 hour
    
    // Store token (you might want to create a password_reset_tokens table)
    $sql = "INSERT INTO password_reset_tokens (admin_id, token, expires_at) 
            VALUES (:admin_id, :token, :expires_at)
            ON DUPLICATE KEY UPDATE 
            token = :token, expires_at = :expires_at, created_at = CURRENT_TIMESTAMP";
    
    executeQuery($sql, [
        'admin_id' => $user['admin_id'],
        'token' => $token,
        'expires_at' => $expires
    ]);
    
    logActivity('password_reset_requested', "Password reset requested for: {$email}");
    
    return $token;
}

/**
 * Verify password reset token
 */
function verifyPasswordResetToken($token) {
    $sql = "SELECT prt.admin_id, au.username, au.email 
            FROM password_reset_tokens prt
            JOIN admin_users au ON prt.admin_id = au.admin_id
            WHERE prt.token = :token AND prt.expires_at > NOW() AND prt.used = 0";
    
    return fetchOne($sql, ['token' => $token]);
}

/**
 * Reset password with token
 */
function resetPasswordWithToken($token, $newPassword) {
    $tokenData = verifyPasswordResetToken($token);
    
    if (!$tokenData) {
        throw new Exception('Invalid or expired reset token');
    }
    
    if (!isStrongPassword($newPassword)) {
        throw new Exception('Password does not meet security requirements');
    }
    
    // Hash new password
    $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);
    
    // Update password
    $sql = "UPDATE admin_users 
            SET password_hash = :password_hash, updated_at = CURRENT_TIMESTAMP 
            WHERE admin_id = :admin_id";
    executeQuery($sql, ['password_hash' => $passwordHash, 'admin_id' => $tokenData['admin_id']]);
    
    // Mark token as used
    $sql = "UPDATE password_reset_tokens SET used = 1 WHERE token = :token";
    executeQuery($sql, ['token' => $token]);
    
    logActivity('password_reset_completed', "Password reset completed for: {$tokenData['email']}");
    
    return true;
}

/**
 * Create remember me token
 */
function createRememberToken($adminId) {
    // Create remember_tokens table if it doesn't exist
    try {
        $checkTable = "SHOW TABLES LIKE 'remember_tokens'";
        $tableExists = fetchOne($checkTable);

        if (!$tableExists) {
            $createTable = "
                CREATE TABLE remember_tokens (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    admin_id INT NOT NULL,
                    token VARCHAR(64) NOT NULL UNIQUE,
                    expires_at TIMESTAMP NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (admin_id) REFERENCES admin_users(admin_id) ON DELETE CASCADE,
                    INDEX idx_token (token),
                    INDEX idx_expires (expires_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            executeQuery($createTable);
        }
    } catch (Exception $e) {
        error_log("Error creating remember_tokens table: " . $e->getMessage());
    }

    // Generate secure token
    $token = bin2hex(random_bytes(32));
    $expires = date('Y-m-d H:i:s', time() + (30 * 24 * 3600)); // 30 days

    // Clean up old tokens for this user
    $sql = "DELETE FROM remember_tokens WHERE admin_id = :admin_id";
    executeQuery($sql, ['admin_id' => $adminId]);

    // Insert new token
    $sql = "INSERT INTO remember_tokens (admin_id, token, expires_at)
            VALUES (:admin_id, :token, :expires_at)";
    executeQuery($sql, [
        'admin_id' => $adminId,
        'token' => $token,
        'expires_at' => $expires
    ]);

    // Set cookie (30 days)
    setcookie('remember_token', $token, time() + (30 * 24 * 3600), '/', '', true, true);

    logActivity('remember_token_created', "Remember me token created for admin ID: {$adminId}");

    return $token;
}

/**
 * Verify remember me token
 */
function verifyRememberToken($token) {
    $sql = "SELECT rt.admin_id, au.username, au.email, au.full_name, au.role, au.status
            FROM remember_tokens rt
            JOIN admin_users au ON rt.admin_id = au.admin_id
            WHERE rt.token = :token AND rt.expires_at > NOW() AND au.status = 'active'";

    return fetchOne($sql, ['token' => $token]);
}

/**
 * Delete remember me token
 */
function deleteRememberToken($token = null, $adminId = null) {
    if ($token) {
        $sql = "DELETE FROM remember_tokens WHERE token = :token";
        executeQuery($sql, ['token' => $token]);
    } elseif ($adminId) {
        $sql = "DELETE FROM remember_tokens WHERE admin_id = :admin_id";
        executeQuery($sql, ['admin_id' => $adminId]);
    }

    // Clear cookie
    setcookie('remember_token', '', time() - 3600, '/', '', true, true);
}

/**
 * Clean up expired remember tokens
 */
function cleanupExpiredRememberTokens() {
    $sql = "DELETE FROM remember_tokens WHERE expires_at < NOW()";
    executeQuery($sql);
}

/**
 * Rate limiting functionality
 */
function checkRateLimit($identifier, $maxAttempts = 5, $timeWindow = 300) {
    // Create rate_limits table if it doesn't exist
    try {
        $checkTable = "SHOW TABLES LIKE 'rate_limits'";
        $tableExists = fetchOne($checkTable);

        if (!$tableExists) {
            $createTable = "
                CREATE TABLE rate_limits (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    identifier VARCHAR(255) NOT NULL,
                    attempts INT DEFAULT 1,
                    first_attempt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_attempt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    blocked_until TIMESTAMP NULL,
                    UNIQUE KEY unique_identifier (identifier),
                    INDEX idx_blocked_until (blocked_until)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            executeQuery($createTable);
        }
    } catch (Exception $e) {
        error_log("Error creating rate_limits table: " . $e->getMessage());
        return true; // Allow access if table creation fails
    }

    $currentTime = time();

    // Clean up old entries
    $sql = "DELETE FROM rate_limits WHERE first_attempt < DATE_SUB(NOW(), INTERVAL :time_window SECOND) AND blocked_until IS NULL";
    executeQuery($sql, ['time_window' => $timeWindow]);

    // Check if currently blocked
    $sql = "SELECT blocked_until FROM rate_limits WHERE identifier = :identifier AND blocked_until > NOW()";
    $blocked = fetchOne($sql, ['identifier' => $identifier]);

    if ($blocked) {
        return false; // Still blocked
    }

    // Get current attempts
    $sql = "SELECT attempts, first_attempt FROM rate_limits WHERE identifier = :identifier";
    $record = fetchOne($sql, ['identifier' => $identifier]);

    if (!$record) {
        // First attempt
        $sql = "INSERT INTO rate_limits (identifier, attempts) VALUES (:identifier, 1)";
        executeQuery($sql, ['identifier' => $identifier]);
        return true;
    }

    // Check if within time window
    $firstAttempt = strtotime($record['first_attempt']);
    if (($currentTime - $firstAttempt) > $timeWindow) {
        // Reset counter
        $sql = "UPDATE rate_limits SET attempts = 1, first_attempt = NOW() WHERE identifier = :identifier";
        executeQuery($sql, ['identifier' => $identifier]);
        return true;
    }

    // Increment attempts
    $newAttempts = $record['attempts'] + 1;

    if ($newAttempts > $maxAttempts) {
        // Block for time window
        $sql = "UPDATE rate_limits SET attempts = :attempts, blocked_until = DATE_ADD(NOW(), INTERVAL :time_window SECOND) WHERE identifier = :identifier";
        executeQuery($sql, [
            'attempts' => $newAttempts,
            'time_window' => $timeWindow,
            'identifier' => $identifier
        ]);
        return false;
    }

    // Update attempts
    $sql = "UPDATE rate_limits SET attempts = :attempts WHERE identifier = :identifier";
    executeQuery($sql, ['attempts' => $newAttempts, 'identifier' => $identifier]);

    return true;
}

/**
 * Get rate limit info
 */
function getRateLimitInfo($identifier) {
    $sql = "SELECT attempts, blocked_until FROM rate_limits WHERE identifier = :identifier";
    $record = fetchOne($sql, ['identifier' => $identifier]);

    if (!$record) {
        return ['attempts' => 0, 'blocked_until' => null];
    }

    return $record;
}

/**
 * Enhanced security headers
 */
function setSecurityHeaders() {
    // Prevent clickjacking
    header('X-Frame-Options: DENY');

    // Prevent MIME type sniffing
    header('X-Content-Type-Options: nosniff');

    // XSS protection
    header('X-XSS-Protection: 1; mode=block');

    // Referrer policy
    header('Referrer-Policy: strict-origin-when-cross-origin');

    // Content Security Policy
    header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self'; connect-src 'self'");

    // HTTPS enforcement (if using HTTPS)
    if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
        header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
    }
}

/**
 * Validate session security
 */
function validateSessionSecurity() {
    // Check for session hijacking attempts
    if (!isset($_SESSION['created_at'])) {
        $_SESSION['created_at'] = time();
    }

    // Regenerate session ID periodically
    if (!isset($_SESSION['last_regeneration'])) {
        $_SESSION['last_regeneration'] = time();
    }

    // Regenerate every 30 minutes
    if ((time() - $_SESSION['last_regeneration']) > 1800) {
        session_regenerate_id(true);
        $_SESSION['last_regeneration'] = time();
    }

    // Check for suspicious activity
    if (isset($_SESSION['ip_address']) && $_SESSION['ip_address'] !== $_SERVER['REMOTE_ADDR']) {
        logActivity('security_violation', 'IP address mismatch detected');
        logoutUser(true);
        return false;
    }

    return true;
}

/**
 * Log security events
 */
function logSecurityEvent($event, $details, $severity = 'medium') {
    $logData = [
        'timestamp' => date('Y-m-d H:i:s'),
        'event' => $event,
        'details' => $details,
        'severity' => $severity,
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'session_id' => session_id(),
        'user_id' => $_SESSION['admin_id'] ?? null
    ];

    // Log to file
    $logFile = dirname(__DIR__) . '/logs/security.log';
    $logDir = dirname($logFile);

    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }

    file_put_contents($logFile, json_encode($logData) . "\n", FILE_APPEND | LOCK_EX);

    // Also log to activity log
    logActivity($event, $details);
}

/**
 * Auto-login with remember token
 */
function autoLoginWithRememberToken() {
    if (!isset($_COOKIE['remember_token'])) {
        return false;
    }

    $token = $_COOKIE['remember_token'];
    $userData = verifyRememberToken($token);

    if (!$userData) {
        // Invalid token, clear cookie
        deleteRememberToken($token);
        return false;
    }

    // Set session variables
    $_SESSION['admin_id'] = $userData['admin_id'];
    $_SESSION['admin_username'] = $userData['username'];
    $_SESSION['admin_email'] = $userData['email'];
    $_SESSION['admin_name'] = $userData['full_name'];
    $_SESSION['admin_role'] = $userData['role'];
    $_SESSION['login_time'] = time();
    $_SESSION['ip_address'] = $_SERVER['REMOTE_ADDR'];
    $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'];
    $_SESSION['auto_login'] = true; // Flag to indicate auto-login

    // Update last login
    updateLastLogin($userData['admin_id']);

    // Log auto-login
    logActivity('auto_login', "Auto-login successful for user: {$userData['username']}");

    return true;
}

/**
 * Create organizer access token
 */
function createOrganizerToken($matchId, $expiresInHours = 24) {
    $token = bin2hex(random_bytes(32));
    $expires = date('Y-m-d H:i:s', time() + ($expiresInHours * 3600));

    $sql = "INSERT INTO organizer_tokens (match_id, token, expires_at)
            VALUES (:match_id, :token, :expires_at)";

    executeQuery($sql, [
        'match_id' => $matchId,
        'token' => $token,
        'expires_at' => $expires
    ]);

    return $token;
}

/**
 * Verify organizer access token
 */
function verifyOrganizerToken($token) {
    $sql = "SELECT ot.match_id, m.sport_id, m.match_date, m.match_time, s.name as sport_name
            FROM organizer_tokens ot
            JOIN matches m ON ot.match_id = m.match_id
            JOIN sports s ON m.sport_id = s.sport_id
            WHERE ot.token = :token AND ot.expires_at > NOW() AND ot.used = 0";

    return fetchOne($sql, ['token' => $token]);
}
?>
