<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Authentication Functions
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

// Prevent direct access
if (!defined('SCIMS_ACCESS')) {
    die('Direct access not permitted');
}

/**
 * Authenticate user login
 */
function authenticateUser($username, $password) {
    try {
        // Check if user is locked out
        if (isUserLockedOut($username)) {
            throw new Exception('Account is temporarily locked due to multiple failed login attempts');
        }
        
        // Get user from database
        $sql = "SELECT admin_id, username, password_hash, email, full_name, role, status, failed_login_attempts 
                FROM admin_users 
                WHERE username = :username AND status = 'active'";
        $user = fetchOne($sql, ['username' => $username]);
        
        if (!$user) {
            recordFailedLogin($username);
            throw new Exception('Invalid username or password');
        }
        
        // Verify password
        if (!password_verify($password, $user['password_hash'])) {
            recordFailedLogin($username);
            throw new Exception('Invalid username or password');
        }
        
        // Reset failed login attempts on successful login
        resetFailedLoginAttempts($username);
        
        // Update last login
        updateLastLogin($user['admin_id']);
        
        // Set session variables
        $_SESSION['admin_id'] = $user['admin_id'];
        $_SESSION['admin_username'] = $user['username'];
        $_SESSION['admin_email'] = $user['email'];
        $_SESSION['admin_name'] = $user['full_name'];
        $_SESSION['admin_role'] = $user['role'];
        $_SESSION['login_time'] = time();
        $_SESSION['ip_address'] = $_SERVER['REMOTE_ADDR'];
        $_SESSION['user_agent'] = $_SERVER['HTTP_USER_AGENT'];
        
        // Log successful login
        logActivity('login', "User {$username} logged in successfully");
        
        return true;
        
    } catch (Exception $e) {
        logActivity('login_failed', "Failed login attempt for username: {$username} - " . $e->getMessage());
        throw $e;
    }
}

/**
 * Check if user is locked out
 */
function isUserLockedOut($username) {
    $sql = "SELECT failed_login_attempts, locked_until 
            FROM admin_users 
            WHERE username = :username";
    $user = fetchOne($sql, ['username' => $username]);
    
    if (!$user) return false;
    
    // Check if user is currently locked
    if ($user['locked_until'] && strtotime($user['locked_until']) > time()) {
        return true;
    }
    
    // Check if user has exceeded max attempts
    if ($user['failed_login_attempts'] >= MAX_LOGIN_ATTEMPTS) {
        // Lock the user
        lockUser($username);
        return true;
    }
    
    return false;
}

/**
 * Record failed login attempt
 */
function recordFailedLogin($username) {
    $sql = "UPDATE admin_users 
            SET failed_login_attempts = failed_login_attempts + 1 
            WHERE username = :username";
    executeQuery($sql, ['username' => $username]);
    
    // Check if user should be locked
    $user = fetchOne("SELECT failed_login_attempts FROM admin_users WHERE username = :username", 
                     ['username' => $username]);
    
    if ($user && $user['failed_login_attempts'] >= MAX_LOGIN_ATTEMPTS) {
        lockUser($username);
    }
}

/**
 * Lock user account
 */
function lockUser($username) {
    $lockUntil = date('Y-m-d H:i:s', time() + LOCKOUT_DURATION);
    $sql = "UPDATE admin_users 
            SET locked_until = :locked_until 
            WHERE username = :username";
    executeQuery($sql, ['locked_until' => $lockUntil, 'username' => $username]);
    
    logActivity('account_locked', "Account locked for username: {$username}");
}

/**
 * Reset failed login attempts
 */
function resetFailedLoginAttempts($username) {
    $sql = "UPDATE admin_users 
            SET failed_login_attempts = 0, locked_until = NULL 
            WHERE username = :username";
    executeQuery($sql, ['username' => $username]);
}

/**
 * Update last login timestamp
 */
function updateLastLogin($adminId) {
    $sql = "UPDATE admin_users 
            SET last_login = CURRENT_TIMESTAMP 
            WHERE admin_id = :admin_id";
    executeQuery($sql, ['admin_id' => $adminId]);
}

/**
 * Logout user
 */
function logoutUser() {
    if (isLoggedIn()) {
        $username = $_SESSION['admin_username'];
        logActivity('logout', "User {$username} logged out");
        
        // Clear session
        session_unset();
        session_destroy();
        
        // Start new session
        session_start();
        session_regenerate_id(true);
    }
}

/**
 * Validate session security
 */
function validateSession() {
    if (!isLoggedIn()) {
        return false;
    }
    
    // Check session timeout
    if (isset($_SESSION['last_activity']) && 
        (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT)) {
        logoutUser();
        return false;
    }
    
    // Validate IP address (optional - can be disabled for dynamic IPs)
    if (isset($_SESSION['ip_address']) && 
        $_SESSION['ip_address'] !== $_SERVER['REMOTE_ADDR']) {
        logActivity('session_hijack_attempt', 
                   "IP mismatch - Session: {$_SESSION['ip_address']}, Current: {$_SERVER['REMOTE_ADDR']}");
        logoutUser();
        return false;
    }
    
    // Validate user agent
    if (isset($_SESSION['user_agent']) && 
        $_SESSION['user_agent'] !== $_SERVER['HTTP_USER_AGENT']) {
        logActivity('session_hijack_attempt', 'User agent mismatch');
        logoutUser();
        return false;
    }
    
    // Update last activity
    $_SESSION['last_activity'] = time();
    
    return true;
}

/**
 * Require authentication
 */
function requireAuth() {
    if (!validateSession()) {
        redirect('/admin/index.php', 'Please log in to continue', 'warning');
    }
}

/**
 * Change user password
 */
function changePassword($adminId, $currentPassword, $newPassword) {
    // Get current password hash
    $sql = "SELECT password_hash FROM admin_users WHERE admin_id = :admin_id";
    $user = fetchOne($sql, ['admin_id' => $adminId]);
    
    if (!$user) {
        throw new Exception('User not found');
    }
    
    // Verify current password
    if (!password_verify($currentPassword, $user['password_hash'])) {
        throw new Exception('Current password is incorrect');
    }
    
    // Validate new password strength
    if (!isStrongPassword($newPassword)) {
        throw new Exception('New password does not meet security requirements');
    }
    
    // Hash new password
    $newPasswordHash = password_hash($newPassword, PASSWORD_DEFAULT);
    
    // Update password
    $sql = "UPDATE admin_users 
            SET password_hash = :password_hash, updated_at = CURRENT_TIMESTAMP 
            WHERE admin_id = :admin_id";
    executeQuery($sql, ['password_hash' => $newPasswordHash, 'admin_id' => $adminId]);
    
    logActivity('password_changed', "Password changed for admin ID: {$adminId}");
    
    return true;
}

/**
 * Validate password strength
 */
function isStrongPassword($password) {
    // Minimum 8 characters
    if (strlen($password) < 8) return false;
    
    // At least one uppercase letter
    if (!preg_match('/[A-Z]/', $password)) return false;
    
    // At least one lowercase letter
    if (!preg_match('/[a-z]/', $password)) return false;
    
    // At least one number
    if (!preg_match('/[0-9]/', $password)) return false;
    
    // At least one special character
    if (!preg_match('/[^A-Za-z0-9]/', $password)) return false;
    
    return true;
}

/**
 * Generate password reset token
 */
function generatePasswordResetToken($email) {
    // Check if user exists
    $sql = "SELECT admin_id, username FROM admin_users WHERE email = :email AND status = 'active'";
    $user = fetchOne($sql, ['email' => $email]);
    
    if (!$user) {
        throw new Exception('Email address not found');
    }
    
    // Generate token
    $token = bin2hex(random_bytes(32));
    $expires = date('Y-m-d H:i:s', time() + 3600); // 1 hour
    
    // Store token (you might want to create a password_reset_tokens table)
    $sql = "INSERT INTO password_reset_tokens (admin_id, token, expires_at) 
            VALUES (:admin_id, :token, :expires_at)
            ON DUPLICATE KEY UPDATE 
            token = :token, expires_at = :expires_at, created_at = CURRENT_TIMESTAMP";
    
    executeQuery($sql, [
        'admin_id' => $user['admin_id'],
        'token' => $token,
        'expires_at' => $expires
    ]);
    
    logActivity('password_reset_requested', "Password reset requested for: {$email}");
    
    return $token;
}

/**
 * Verify password reset token
 */
function verifyPasswordResetToken($token) {
    $sql = "SELECT prt.admin_id, au.username, au.email 
            FROM password_reset_tokens prt
            JOIN admin_users au ON prt.admin_id = au.admin_id
            WHERE prt.token = :token AND prt.expires_at > NOW() AND prt.used = 0";
    
    return fetchOne($sql, ['token' => $token]);
}

/**
 * Reset password with token
 */
function resetPasswordWithToken($token, $newPassword) {
    $tokenData = verifyPasswordResetToken($token);
    
    if (!$tokenData) {
        throw new Exception('Invalid or expired reset token');
    }
    
    if (!isStrongPassword($newPassword)) {
        throw new Exception('Password does not meet security requirements');
    }
    
    // Hash new password
    $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);
    
    // Update password
    $sql = "UPDATE admin_users 
            SET password_hash = :password_hash, updated_at = CURRENT_TIMESTAMP 
            WHERE admin_id = :admin_id";
    executeQuery($sql, ['password_hash' => $passwordHash, 'admin_id' => $tokenData['admin_id']]);
    
    // Mark token as used
    $sql = "UPDATE password_reset_tokens SET used = 1 WHERE token = :token";
    executeQuery($sql, ['token' => $token]);
    
    logActivity('password_reset_completed', "Password reset completed for: {$tokenData['email']}");
    
    return true;
}

/**
 * Create organizer access token
 */
function createOrganizerToken($matchId, $expiresInHours = 24) {
    $token = bin2hex(random_bytes(32));
    $expires = date('Y-m-d H:i:s', time() + ($expiresInHours * 3600));
    
    $sql = "INSERT INTO organizer_tokens (match_id, token, expires_at) 
            VALUES (:match_id, :token, :expires_at)";
    
    executeQuery($sql, [
        'match_id' => $matchId,
        'token' => $token,
        'expires_at' => $expires
    ]);
    
    return $token;
}

/**
 * Verify organizer access token
 */
function verifyOrganizerToken($token) {
    $sql = "SELECT ot.match_id, m.sport_id, m.match_date, m.match_time, s.name as sport_name
            FROM organizer_tokens ot
            JOIN matches m ON ot.match_id = m.match_id
            JOIN sports s ON m.sport_id = s.sport_id
            WHERE ot.token = :token AND ot.expires_at > NOW() AND ot.used = 0";
    
    return fetchOne($sql, ['token' => $token]);
}
?>
