<?php
/**
 * AJAX endpoint for tournament configuration operations
 * Handles score updates, scheduling, and real-time bracket updates
 */

require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Ensure user is authenticated
requireAuth();

// Set JSON response header
header('Content-Type: application/json');

// CSRF protection
if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Invalid security token']);
    exit;
}

$action = $_POST['action'] ?? '';

try {
    switch ($action) {
        case 'save_match_score':
            handleSaveMatchScore();
            break;
            
        case 'schedule_match':
            handleScheduleMatch();
            break;
            
        case 'update_match_status':
            handleUpdateMatchStatus();
            break;

        case 'get_live_updates':
            handleGetLiveUpdates();
            break;

        case 'bulk_update_scores':
            handleBulkUpdateScores();
            break;

        case 'test_connection':
            handleTestConnection();
            break;

        default:
            throw new Exception('Invalid action specified');
    }
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Handle saving match scores
 */
function handleSaveMatchScore() {
    global $pdo;
    
    $matchId = $_POST['match_id'] ?? '';
    $team1Score = (int)($_POST['team1_score'] ?? 0);
    $team2Score = (int)($_POST['team2_score'] ?? 0);
    
    if (empty($matchId)) {
        throw new Exception('Match ID is required');
    }
    
    if ($team1Score < 0 || $team2Score < 0) {
        throw new Exception('Scores cannot be negative');
    }
    
    if ($team1Score === 0 && $team2Score === 0) {
        throw new Exception('Please enter valid scores for both teams');
    }
    
    // Begin transaction
    $pdo->beginTransaction();
    
    try {
        // For tournament bracket matches, we need to update the bracket data
        // First, get the current tournament configuration
        $eventSportId = $_SESSION['current_event_sport_id'] ?? null;
        if (!$eventSportId) {
            throw new Exception('No active tournament session');
        }
        
        $tournamentConfig = fetchOne("
            SELECT * FROM tournament_configs 
            WHERE event_sport_id = ?
        ", [$eventSportId]);
        
        if (!$tournamentConfig) {
            throw new Exception('Tournament configuration not found');
        }
        
        // Decode bracket data
        $bracketData = json_decode($tournamentConfig['bracket_data'], true);
        if (!$bracketData) {
            throw new Exception('Invalid bracket data');
        }
        
        // Find and update the match in bracket data
        $matchUpdated = false;
        $winnerId = null;
        $team1Id = null;
        $team2Id = null;
        
        foreach ($bracketData['rounds'] as &$round) {
            foreach ($round['matches'] as &$match) {
                if ($match['match_id'] === $matchId) {
                    // Update scores
                    $match['team1_score'] = $team1Score;
                    $match['team2_score'] = $team2Score;
                    
                    // Determine winner
                    if ($team1Score > $team2Score) {
                        $winnerId = $match['team1_id'] ?? null;
                        $match['winner_id'] = $winnerId;
                    } elseif ($team2Score > $team1Score) {
                        $winnerId = $match['team2_id'] ?? null;
                        $match['winner_id'] = $winnerId;
                    }
                    
                    $team1Id = $match['team1_id'] ?? null;
                    $team2Id = $match['team2_id'] ?? null;
                    
                    $match['status'] = 'completed';
                    $matchUpdated = true;
                    break 2;
                }
            }
        }
        
        if (!$matchUpdated) {
            throw new Exception('Match not found in bracket data');
        }
        
        // Update tournament configuration with new bracket data
        updateRecord('tournament_configs', [
            'bracket_data' => json_encode($bracketData),
            'updated_at' => date('Y-m-d H:i:s')
        ], 'event_sport_id = :event_sport_id', ['event_sport_id' => $eventSportId]);
        
        // Log the score update
        logActivity('match_score_updated', "Match {$matchId} score updated: {$team1Score}-{$team2Score}");
        
        $pdo->commit();
        
        // Return success response with updated match data
        echo json_encode([
            'success' => true,
            'message' => 'Score saved successfully',
            'match_data' => [
                'match_id' => $matchId,
                'team1_score' => $team1Score,
                'team2_score' => $team2Score,
                'winner_id' => $winnerId,
                'team1_id' => $team1Id,
                'team2_id' => $team2Id,
                'status' => 'completed'
            ],
            'bracket_updated' => true
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
}

/**
 * Handle scheduling a match
 */
function handleScheduleMatch() {
    global $pdo;
    
    $matchId = $_POST['match_id'] ?? '';
    $matchDate = $_POST['match_date'] ?? '';
    $matchTime = $_POST['match_time'] ?? '';
    $venueId = $_POST['venue_id'] ?? null;
    
    if (empty($matchId)) {
        throw new Exception('Match ID is required');
    }
    
    if (empty($matchDate) || empty($matchTime)) {
        throw new Exception('Date and time are required');
    }
    
    // Validate date format
    $dateObj = DateTime::createFromFormat('Y-m-d', $matchDate);
    if (!$dateObj || $dateObj->format('Y-m-d') !== $matchDate) {
        throw new Exception('Invalid date format');
    }
    
    // Validate time format
    $timeObj = DateTime::createFromFormat('H:i', $matchTime);
    if (!$timeObj || $timeObj->format('H:i') !== $matchTime) {
        throw new Exception('Invalid time format');
    }
    
    // Begin transaction
    $pdo->beginTransaction();
    
    try {
        // Get tournament configuration
        $eventSportId = $_SESSION['current_event_sport_id'] ?? null;
        if (!$eventSportId) {
            throw new Exception('No active tournament session');
        }
        
        $tournamentConfig = fetchOne("
            SELECT * FROM tournament_configs 
            WHERE event_sport_id = ?
        ", [$eventSportId]);
        
        if (!$tournamentConfig) {
            throw new Exception('Tournament configuration not found');
        }
        
        // Decode and update bracket data
        $bracketData = json_decode($tournamentConfig['bracket_data'], true);
        if (!$bracketData) {
            throw new Exception('Invalid bracket data');
        }
        
        // Find and update the match
        $matchUpdated = false;
        foreach ($bracketData['rounds'] as &$round) {
            foreach ($round['matches'] as &$match) {
                if ($match['match_id'] === $matchId) {
                    $match['match_date'] = $matchDate;
                    $match['match_time'] = $matchTime;
                    if ($venueId) {
                        $match['venue_id'] = $venueId;
                        
                        // Get venue name
                        $venue = fetchOne("SELECT name FROM venues WHERE venue_id = ?", [$venueId]);
                        if ($venue) {
                            $match['venue_name'] = $venue['name'];
                        }
                    }
                    $match['status'] = 'scheduled';
                    $matchUpdated = true;
                    break 2;
                }
            }
        }
        
        if (!$matchUpdated) {
            throw new Exception('Match not found in bracket data');
        }
        
        // Update tournament configuration
        updateRecord('tournament_configs', [
            'bracket_data' => json_encode($bracketData),
            'updated_at' => date('Y-m-d H:i:s')
        ], 'event_sport_id = :event_sport_id', ['event_sport_id' => $eventSportId]);
        
        // Log the scheduling update
        logActivity('match_scheduled', "Match {$matchId} scheduled for {$matchDate} at {$matchTime}");
        
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Match scheduled successfully',
            'match_data' => [
                'match_id' => $matchId,
                'match_date' => $matchDate,
                'match_time' => $matchTime,
                'venue_id' => $venueId,
                'status' => 'scheduled'
            ]
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
}

/**
 * Handle updating match status
 */
function handleUpdateMatchStatus() {
    global $pdo;
    
    $matchId = $_POST['match_id'] ?? '';
    $status = $_POST['status'] ?? '';
    
    if (empty($matchId) || empty($status)) {
        throw new Exception('Match ID and status are required');
    }
    
    $validStatuses = ['pending', 'scheduled', 'in_progress', 'completed', 'cancelled'];
    if (!in_array($status, $validStatuses)) {
        throw new Exception('Invalid status');
    }
    
    // Begin transaction
    $pdo->beginTransaction();
    
    try {
        // Get tournament configuration
        $eventSportId = $_SESSION['current_event_sport_id'] ?? null;
        if (!$eventSportId) {
            throw new Exception('No active tournament session');
        }
        
        $tournamentConfig = fetchOne("
            SELECT * FROM tournament_configs 
            WHERE event_sport_id = ?
        ", [$eventSportId]);
        
        if (!$tournamentConfig) {
            throw new Exception('Tournament configuration not found');
        }
        
        // Decode and update bracket data
        $bracketData = json_decode($tournamentConfig['bracket_data'], true);
        if (!$bracketData) {
            throw new Exception('Invalid bracket data');
        }
        
        // Find and update the match
        $matchUpdated = false;
        foreach ($bracketData['rounds'] as &$round) {
            foreach ($round['matches'] as &$match) {
                if ($match['match_id'] === $matchId) {
                    $match['status'] = $status;
                    $matchUpdated = true;
                    break 2;
                }
            }
        }
        
        if (!$matchUpdated) {
            throw new Exception('Match not found in bracket data');
        }
        
        // Update tournament configuration
        updateRecord('tournament_configs', [
            'bracket_data' => json_encode($bracketData),
            'updated_at' => date('Y-m-d H:i:s')
        ], 'event_sport_id = :event_sport_id', ['event_sport_id' => $eventSportId]);
        
        // Log the status update
        logActivity('match_status_updated', "Match {$matchId} status updated to {$status}");
        
        $pdo->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Match status updated successfully',
            'match_data' => [
                'match_id' => $matchId,
                'status' => $status
            ]
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
}

/**
 * Handle getting live updates for matches
 */
function handleGetLiveUpdates() {
    global $pdo;

    $eventSportId = $_SESSION['current_event_sport_id'] ?? null;
    if (!$eventSportId) {
        throw new Exception('No active tournament session');
    }

    // Get tournament configuration
    $tournamentConfig = fetchOne("
        SELECT * FROM tournament_configs
        WHERE event_sport_id = ?
    ", [$eventSportId]);

    if (!$tournamentConfig) {
        throw new Exception('Tournament configuration not found');
    }

    // Decode bracket data
    $bracketData = json_decode($tournamentConfig['bracket_data'], true);
    if (!$bracketData) {
        throw new Exception('Invalid bracket data');
    }

    // Collect all matches with their current status
    $matches = [];
    foreach ($bracketData['rounds'] as $round) {
        foreach ($round['matches'] as $match) {
            $matches[] = [
                'match_id' => $match['match_id'],
                'status' => $match['status'] ?? 'pending',
                'team1_score' => $match['team1_score'] ?? null,
                'team2_score' => $match['team2_score'] ?? null,
                'winner_id' => $match['winner_id'] ?? null,
                'match_date' => $match['match_date'] ?? null,
                'match_time' => $match['match_time'] ?? null,
                'venue_name' => $match['venue_name'] ?? null,
                'last_updated' => $tournamentConfig['updated_at']
            ];
        }
    }

    echo json_encode([
        'success' => true,
        'matches' => $matches,
        'last_updated' => $tournamentConfig['updated_at'],
        'tournament_status' => $tournamentConfig['status'] ?? 'active'
    ]);
}

/**
 * Handle bulk score updates
 */
function handleBulkUpdateScores() {
    global $pdo;

    $scores = $_POST['scores'] ?? [];

    if (empty($scores) || !is_array($scores)) {
        throw new Exception('No scores provided');
    }

    $eventSportId = $_SESSION['current_event_sport_id'] ?? null;
    if (!$eventSportId) {
        throw new Exception('No active tournament session');
    }

    // Begin transaction
    $pdo->beginTransaction();

    try {
        // Get tournament configuration
        $tournamentConfig = fetchOne("
            SELECT * FROM tournament_configs
            WHERE event_sport_id = ?
        ", [$eventSportId]);

        if (!$tournamentConfig) {
            throw new Exception('Tournament configuration not found');
        }

        // Decode bracket data
        $bracketData = json_decode($tournamentConfig['bracket_data'], true);
        if (!$bracketData) {
            throw new Exception('Invalid bracket data');
        }

        $updatedMatches = [];

        // Process each score update
        foreach ($scores as $scoreUpdate) {
            $matchId = $scoreUpdate['match_id'] ?? '';
            $team1Score = (int)($scoreUpdate['team1_score'] ?? 0);
            $team2Score = (int)($scoreUpdate['team2_score'] ?? 0);

            if (empty($matchId)) continue;

            // Find and update the match in bracket data
            foreach ($bracketData['rounds'] as &$round) {
                foreach ($round['matches'] as &$match) {
                    if ($match['match_id'] === $matchId) {
                        // Update scores
                        $match['team1_score'] = $team1Score;
                        $match['team2_score'] = $team2Score;

                        // Determine winner
                        if ($team1Score > $team2Score) {
                            $match['winner_id'] = $match['team1_id'] ?? null;
                        } elseif ($team2Score > $team1Score) {
                            $match['winner_id'] = $match['team2_id'] ?? null;
                        } else {
                            $match['winner_id'] = null; // Tie
                        }

                        $match['status'] = 'completed';
                        $updatedMatches[] = $matchId;
                        break 2;
                    }
                }
            }
        }

        // Update tournament configuration with new bracket data
        updateRecord('tournament_configs', [
            'bracket_data' => json_encode($bracketData),
            'updated_at' => date('Y-m-d H:i:s')
        ], 'event_sport_id = :event_sport_id', ['event_sport_id' => $eventSportId]);

        // Log the bulk update
        logActivity('bulk_scores_updated', "Bulk score update for matches: " . implode(', ', $updatedMatches));

        $pdo->commit();

        echo json_encode([
            'success' => true,
            'message' => 'Bulk scores updated successfully',
            'updated_matches' => $updatedMatches,
            'total_updated' => count($updatedMatches)
        ]);

    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
}

/**
 * Handle test connection request
 */
function handleTestConnection() {
    echo json_encode([
        'success' => true,
        'message' => 'AJAX backend connected successfully',
        'timestamp' => date('Y-m-d H:i:s'),
        'features' => [
            'score_updates' => true,
            'match_scheduling' => true,
            'status_updates' => true,
            'live_updates' => true,
            'bulk_operations' => true
        ]
    ]);
}
?>
