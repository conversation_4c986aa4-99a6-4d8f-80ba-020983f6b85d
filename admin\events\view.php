<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Events Management - View Event Details
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication and permission
requireAuth();
requirePermission('manage_events');

$eventId = (int)($_GET['id'] ?? 0);

if (!$eventId) {
    header('Location: index.php?message=Invalid event ID&type=error');
    exit;
}

// Get event details
$event = fetchOne("
    SELECT e.*, 
           au.full_name as created_by_name,
           (SELECT COUNT(*) FROM matches WHERE event_id = e.event_id) as match_count,
           (SELECT COUNT(*) FROM department_standings WHERE event_id = e.event_id) as dept_count
    FROM events e
    LEFT JOIN admin_users au ON e.created_by = au.admin_id
    WHERE e.event_id = ?
", [$eventId]);

if (!$event) {
    header('Location: index.php?message=Event not found&type=error');
    exit;
}

// Get event matches with participants
$matches = fetchAll("
    SELECT m.*,
           v.name as venue_name,
           s.name as sport_name,
           GROUP_CONCAT(DISTINCT d.name ORDER BY mp.participant_id SEPARATOR ' vs ') as participants
    FROM matches m
    LEFT JOIN venues v ON m.venue_id = v.venue_id
    LEFT JOIN sports s ON m.sport_id = s.sport_id
    LEFT JOIN match_participants mp ON m.match_id = mp.match_id
    LEFT JOIN departments d ON mp.dept_id = d.dept_id
    WHERE m.event_id = ?
    GROUP BY m.match_id
    ORDER BY m.match_date, m.match_time
", [$eventId]);

// Get participating departments
$departments = fetchAll("
    SELECT d.name, ds.points, ds.position
    FROM department_standings ds
    JOIN departments d ON ds.department_id = d.department_id
    WHERE ds.event_id = ?
    ORDER BY ds.position ASC, ds.points DESC
", [$eventId]);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($event['name']); ?> - Event Details - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
    <style>
        .event-view-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .event-header {
            background: var(--white);
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--primary-600);
        }
        
        .event-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }
        
        .event-title h1 {
            margin: 0;
            color: var(--gray-900);
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .event-actions {
            display: flex;
            gap: 0.75rem;
        }
        
        .event-description {
            color: var(--gray-600);
            line-height: 1.6;
            margin: 0;
        }
        
        .event-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .info-card {
            background: var(--white);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--gray-200);
        }
        
        .info-card-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--gray-200);
        }
        
        .info-card-header i {
            font-size: 1.25rem;
            color: var(--primary-600);
        }
        
        .info-card-header h2 {
            margin: 0;
            font-size: 1.125rem;
            color: var(--gray-900);
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid var(--gray-100);
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 500;
            color: var(--gray-600);
        }
        
        .info-value {
            color: var(--gray-900);
            font-weight: 500;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1.5rem;
        }
        
        .stat-card {
            text-align: center;
            padding: 1rem;
            background: var(--gray-50);
            border-radius: 8px;
        }
        
        .stat-value {
            display: block;
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-600);
            margin-bottom: 0.25rem;
        }
        
        .stat-label {
            font-size: 0.875rem;
            color: var(--gray-600);
            font-weight: 500;
        }
        
        .section {
            background: var(--white);
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .section-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--gray-200);
        }
        
        .section-header i {
            font-size: 1.25rem;
            color: var(--primary-600);
        }
        
        .section-header h2 {
            margin: 0;
            color: var(--gray-900);
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .data-table th,
        .data-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--gray-200);
        }
        
        .data-table th {
            background: var(--gray-50);
            font-weight: 600;
            color: var(--gray-900);
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .data-table tr:hover {
            background: var(--gray-50);
        }
        
        .position-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            font-weight: bold;
            font-size: 0.875rem;
        }
        
        .position-badge.position-1 {
            background: var(--yellow-100);
            color: var(--yellow-800);
        }
        
        .position-badge.position-2 {
            background: var(--gray-200);
            color: var(--gray-800);
        }
        
        .position-badge.position-3 {
            background: var(--orange-100);
            color: var(--orange-800);
        }
        
        .position-badge:not(.position-1):not(.position-2):not(.position-3) {
            background: var(--gray-100);
            color: var(--gray-600);
        }
        
        .match-teams {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .match-teams .vs {
            color: var(--gray-500);
            font-weight: 500;
            font-size: 0.875rem;
        }
        
        .match-score {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-weight: 600;
            color: var(--gray-900);
        }
        
        .match-datetime {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }
        
        .match-time {
            font-size: 0.875rem;
            color: var(--gray-600);
        }
        
        .no-data {
            text-align: center;
            padding: 3rem 1rem;
            color: var(--gray-500);
        }
        
        .no-data i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        
        .no-data h3 {
            margin: 0 0 0.5rem 0;
            color: var(--gray-700);
        }
        
        .no-data p {
            margin: 0;
        }
        
        @media (max-width: 768px) {
            .event-title {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }
            
            .event-actions {
                width: 100%;
                justify-content: flex-start;
            }
            
            .event-info-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .data-table {
                font-size: 0.875rem;
            }
            
            .data-table th,
            .data-table td {
                padding: 0.75rem 0.5rem;
            }
        }
    </style>
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>
    
    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1>Event Details</h1>
                <nav class="breadcrumb">
                    <a href="../dashboard.php">Dashboard</a>
                    <span>/</span>
                    <a href="index.php">Events</a>
                    <span>/</span>
                    <span>View Event</span>
                </nav>
            </div>
        </div>
        
        <div class="event-view-container">
            <!-- Event Header -->
            <div class="event-header">
                <div class="event-title">
                    <h1>
                        <i class="icon-trophy"></i>
                        <?php echo htmlspecialchars($event['name']); ?>
                        <span class="status-badge status-<?php echo $event['status']; ?>">
                            <?php echo ucfirst($event['status']); ?>
                        </span>
                    </h1>
                    <div class="event-actions">
                        <a href="index.php" class="btn btn-secondary">
                            <i class="icon-arrow-left"></i>
                            Back to Events
                        </a>
                        <button class="btn btn-primary" onclick="editEvent(<?php echo $event['event_id']; ?>)">
                            <i class="icon-edit"></i>
                            Edit Event
                        </button>
                    </div>
                </div>
                <?php if ($event['description']): ?>
                    <p class="event-description"><?php echo htmlspecialchars($event['description']); ?></p>
                <?php endif; ?>
            </div>
            
            <!-- Event Information Grid -->
            <div class="event-info-grid">
                <!-- Event Details -->
                <div class="info-card">
                    <div class="info-card-header">
                        <i class="icon-calendar"></i>
                        <h2>Event Details</h2>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Start Date</span>
                        <span class="info-value"><?php echo formatDate($event['start_date']); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">End Date</span>
                        <span class="info-value"><?php echo formatDate($event['end_date']); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Status</span>
                        <span class="status-badge status-<?php echo $event['status']; ?>">
                            <?php echo ucfirst($event['status']); ?>
                        </span>
                    </div>
                </div>
                
                <!-- Statistics -->
                <div class="info-card">
                    <div class="info-card-header">
                        <i class="icon-bar-chart"></i>
                        <h2>Statistics</h2>
                    </div>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <span class="stat-value"><?php echo $event['match_count']; ?></span>
                            <span class="stat-label">Total Matches</span>
                        </div>
                        <div class="stat-card">
                            <span class="stat-value"><?php echo $event['dept_count']; ?></span>
                            <span class="stat-label">Participating Departments</span>
                        </div>
                    </div>
                </div>
                
                <!-- Management Info -->
                <div class="info-card">
                    <div class="info-card-header">
                        <i class="icon-user"></i>
                        <h2>Management</h2>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Created By</span>
                        <span class="info-value"><?php echo htmlspecialchars($event['created_by_name'] ?? 'System'); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Created On</span>
                        <span class="info-value"><?php echo formatDateTime($event['created_at']); ?></span>
                    </div>
                    <?php if ($event['updated_at']): ?>
                        <div class="info-item">
                            <span class="info-label">Last Updated</span>
                            <span class="info-value"><?php echo formatDateTime($event['updated_at']); ?></span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Participating Departments -->
            <?php if (!empty($departments)): ?>
                <div class="section">
                    <div class="section-header">
                        <i class="icon-users"></i>
                        <h2>Participating Departments</h2>
                    </div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Position</th>
                                <th>Department</th>
                                <th>Points</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($departments as $dept): ?>
                                <tr>
                                    <td>
                                        <?php if ($dept['position']): ?>
                                            <span class="position-badge position-<?php echo $dept['position']; ?>">
                                                <?php echo $dept['position']; ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="position-badge">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($dept['name']); ?></td>
                                    <td><strong><?php echo $dept['points']; ?> pts</strong></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
            
            <!-- Event Matches -->
            <?php if (!empty($matches)): ?>
                <div class="section">
                    <div class="section-header">
                        <i class="icon-calendar"></i>
                        <h2>Event Matches</h2>
                    </div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Date & Time</th>
                                <th>Teams</th>
                                <th>Venue</th>
                                <th>Status</th>
                                <th>Score</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($matches as $match): ?>
                                <tr>
                                    <td>
                                        <div class="match-datetime">
                                            <div><?php echo formatDate($match['match_date']); ?></div>
                                            <div class="match-time"><?php echo formatTime($match['match_time']); ?></div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="match-participants">
                                            <?php echo htmlspecialchars($match['participants'] ?? 'No participants'); ?>
                                        </div>
                                    </td>
                                    <td><?php echo htmlspecialchars($match['venue_name'] ?? 'TBD'); ?></td>
                                    <td>
                                        <span class="status-badge status-<?php echo $match['status']; ?>">
                                            <?php echo ucfirst($match['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($match['status'] === 'completed' && ($match['score1'] !== null || $match['score2'] !== null)): ?>
                                            <div class="match-score">
                                                <span><?php echo $match['score1'] ?? 0; ?></span>
                                                <span>-</span>
                                                <span><?php echo $match['score2'] ?? 0; ?></span>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="section">
                    <div class="no-data">
                        <i class="icon-calendar"></i>
                        <h3>No Matches Scheduled</h3>
                        <p>No matches have been scheduled for this event yet.</p>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </main>
    
    <!-- Edit Event Modal -->
    <div id="editModal" class="modal">
        <div class="modal-overlay">
            <div class="modal-content modal-lg">
                <div class="modal-header">
                    <h3><i class="icon-edit"></i> Edit Event</h3>
                    <button class="modal-close" onclick="closeModal('editModal')">&times;</button>
                </div>
                <div class="modal-body">
                    <div id="editEventContent">
                        <div class="loading">Loading event form...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="../../assets/js/admin.js"></script>
    <script>
        // Edit Event Function
        function editEvent(eventId) {
            const content = document.getElementById('editEventContent');
            content.innerHTML = '<div class="loading"><i class="icon-spinner"></i> Loading event form...</div>';
            openModal('editModal');
            
            // Fetch edit form
            fetch(`edit_ajax.php?id=${eventId}`)
                .then(response => response.text())
                .then(html => {
                    content.innerHTML = html;
                    // Initialize form validation and handlers
                    initializeEditForm();
                })
                .catch(error => {
                    content.innerHTML = '<div class="alert alert-error">Error loading edit form. Please try again.</div>';
                });
        }
        
        // Initialize Edit Form
        function initializeEditForm() {
            const form = document.getElementById('editEventForm');
            if (form) {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    const formData = new FormData(form);
                    const submitBtn = form.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;
                    
                    submitBtn.innerHTML = '<i class="icon-spinner"></i> Saving...';
                    submitBtn.disabled = true;
                    
                    fetch('edit_ajax.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            closeModal('editModal');
                            location.reload(); // Refresh the page to show updated data
                        } else {
                            // Show error message
                            const errorDiv = document.getElementById('editFormErrors');
                            if (errorDiv) {
                                errorDiv.innerHTML = `<div class="alert alert-error">${data.message}</div>`;
                            }
                        }
                    })
                    .catch(error => {
                        const errorDiv = document.getElementById('editFormErrors');
                        if (errorDiv) {
                            errorDiv.innerHTML = '<div class="alert alert-error">Error saving event. Please try again.</div>';
                        }
                    })
                    .finally(() => {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    });
                });
            }
        }
        
        // Modal Functions
        function openModal(modalId) {
            document.getElementById(modalId).classList.add('show');
            document.body.style.overflow = 'hidden';
        }
        
        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('show');
            document.body.style.overflow = '';
        }
        
        // Initialize functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Close modals when clicking overlay
            document.querySelectorAll('.modal-overlay').forEach(overlay => {
                overlay.addEventListener('click', function(e) {
                    if (e.target === this) {
                        const modal = this.closest('.modal');
                        if (modal) {
                            closeModal(modal.id);
                        }
                    }
                });
            });
            
            // Add spinner animation CSS
            const style = document.createElement('style');
            style.textContent = `
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
                .icon-spinner {
                    animation: spin 1s linear infinite;
                }
                .loading {
                    text-align: center;
                    padding: 2rem;
                    color: var(--gray-600);
                }
                .modal-lg {
                    max-width: 800px;
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>
