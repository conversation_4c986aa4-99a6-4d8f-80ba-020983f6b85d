<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Admin Profile Management
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

declare(strict_types=1);

define('SCIMS_ACCESS', true);
require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// Require authentication
requireAuth();

$currentUser = getCurrentUser();
$message = '';
$messageType = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('Invalid security token');
        }
        
        $action = $_POST['action'] ?? '';
        
        if ($action === 'update_profile') {
            // Update profile information
            $fullName = sanitizeInput($_POST['full_name'] ?? '');
            $email = sanitizeInput($_POST['email'] ?? '');
            $phone = sanitizeInput($_POST['phone'] ?? '');
            
            // Validation
            if (empty($fullName)) {
                throw new Exception('Full name is required');
            }
            
            if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Valid email address is required');
            }
            
            // Check if email is already taken by another user
            $existingUser = fetchOne(
                "SELECT admin_id FROM admin_users WHERE email = ? AND admin_id != ?",
                [$email, $currentUser['admin_id']]
            );
            
            if ($existingUser) {
                throw new Exception('Email address is already in use');
            }
            
            // Update user profile
            $updateData = [
                'full_name' => $fullName,
                'email' => $email,
                'phone' => $phone,
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            updateRecord('admin_users', $updateData, 'admin_id = ?', [$currentUser['admin_id']]);
            
            // Update session data
            $_SESSION['user']['full_name'] = $fullName;
            $_SESSION['user']['email'] = $email;
            $_SESSION['user']['phone'] = $phone;
            
            logActivity('profile_updated', 'Profile information updated');
            
            $message = 'Profile updated successfully';
            $messageType = 'success';
            
            // Refresh current user data
            $currentUser = getCurrentUser();
            
        } elseif ($action === 'change_password') {
            // Change password
            $currentPassword = $_POST['current_password'] ?? '';
            $newPassword = $_POST['new_password'] ?? '';
            $confirmPassword = $_POST['confirm_password'] ?? '';
            
            // Validation
            if (empty($currentPassword)) {
                throw new Exception('Current password is required');
            }
            
            if (!password_verify($currentPassword, $currentUser['password_hash'])) {
                throw new Exception('Current password is incorrect');
            }
            
            if (empty($newPassword)) {
                throw new Exception('New password is required');
            }
            
            if (strlen($newPassword) < 8) {
                throw new Exception('New password must be at least 8 characters long');
            }
            
            if ($newPassword !== $confirmPassword) {
                throw new Exception('Password confirmation does not match');
            }
            
            // Update password
            $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);
            updateRecord('admin_users', ['password_hash' => $passwordHash], 'admin_id = ?', [$currentUser['admin_id']]);
            
            logActivity('password_changed', 'Password changed successfully');
            
            $message = 'Password changed successfully';
            $messageType = 'success';
        }
        
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Profile - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body class="admin-page">
    <?php include 'includes/header.php'; ?>
    <?php include 'includes/sidebar.php'; ?>
    
    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1>My Profile</h1>
                <nav class="breadcrumb">
                    <a href="dashboard.php">Dashboard</a>
                    <span>/</span>
                    <span>Profile</span>
                </nav>
            </div>
        </div>

        <!-- Profile Tab Navigation -->
        <div class="profile-tabs-container">
            <nav class="profile-tabs">
                <a href="profile.php" class="profile-tab active">
                    <i class="icon-user"></i>
                    Profile Settings
                </a>
                <a href="change-password.php" class="profile-tab">
                    <i class="icon-lock"></i>
                    Change Password
                </a>
            </nav>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?>">
                <i class="icon-<?php echo $messageType === 'success' ? 'check' : 'warning'; ?>"></i>
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <div class="profile-container">
            <div class="profile-grid">
                <!-- Profile Information -->
                <div class="profile-card">
                    <div class="card-header">
                        <h2><i class="icon-user"></i> Profile Information</h2>
                    </div>
                    
                    <form method="POST" class="profile-form">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="update_profile">
                        
                        <div class="form-group">
                            <label for="username">Username</label>
                            <input type="text" id="username" value="<?php echo htmlspecialchars($currentUser['username']); ?>" disabled>
                            <small>Username cannot be changed</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="full_name">Full Name</label>
                            <input type="text" id="full_name" name="full_name" 
                                   value="<?php echo htmlspecialchars($currentUser['full_name']); ?>" 
                                   required maxlength="100">
                        </div>
                        
                        <div class="form-group">
                            <label for="email">Email Address</label>
                            <input type="email" id="email" name="email" 
                                   value="<?php echo htmlspecialchars($currentUser['email']); ?>" 
                                   required maxlength="100">
                        </div>
                        
                        <div class="form-group">
                            <label for="phone">Phone Number</label>
                            <input type="tel" id="phone" name="phone" 
                                   value="<?php echo htmlspecialchars($currentUser['phone'] ?? ''); ?>" 
                                   maxlength="20">
                        </div>
                        
                        <div class="form-group">
                            <label for="role">Role</label>
                            <input type="text" id="role" value="<?php echo ucfirst(str_replace('_', ' ', $currentUser['role'])); ?>" disabled>
                            <small>Role is assigned by system administrator</small>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="icon-save"></i>
                                Update Profile
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Change Password -->
                <div class="profile-card">
                    <div class="card-header">
                        <h2><i class="icon-lock"></i> Change Password</h2>
                    </div>
                    
                    <form method="POST" class="password-form">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="action" value="change_password">
                        
                        <div class="form-group">
                            <label for="current_password">Current Password</label>
                            <input type="password" id="current_password" name="current_password" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="new_password">New Password</label>
                            <input type="password" id="new_password" name="new_password" required minlength="8">
                            <small>Password must be at least 8 characters long</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="confirm_password">Confirm New Password</label>
                            <input type="password" id="confirm_password" name="confirm_password" required>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="icon-lock"></i>
                                Change Password
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Account Information -->
                <div class="profile-card">
                    <div class="card-header">
                        <h2><i class="icon-info"></i> Account Information</h2>
                    </div>
                    
                    <div class="info-grid">
                        <div class="info-item">
                            <label>Account Created:</label>
                            <span><?php echo date('F j, Y', strtotime($currentUser['created_at'])); ?></span>
                        </div>
                        
                        <div class="info-item">
                            <label>Last Login:</label>
                            <span><?php echo $currentUser['last_login'] ? date('F j, Y g:i A', strtotime($currentUser['last_login'])) : 'Never'; ?></span>
                        </div>
                        
                        <div class="info-item">
                            <label>Failed Login Attempts:</label>
                            <span><?php echo number_format($currentUser['failed_login_attempts'] ?? 0); ?></span>
                        </div>
                        
                        <div class="info-item">
                            <label>Account Status:</label>
                            <span class="status-badge status-<?php echo $currentUser['status']; ?>">
                                <?php echo ucfirst($currentUser['status']); ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <script src="../assets/js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Profile tab navigation enhancement
            const profileTabs = document.querySelectorAll('.profile-tab');
            profileTabs.forEach(tab => {
                tab.addEventListener('click', function(e) {
                    // Add loading state for better UX
                    if (!this.classList.contains('active')) {
                        this.style.opacity = '0.7';
                        this.innerHTML += ' <i class="icon-spinner" style="animation: spin 1s linear infinite;"></i>';
                    }
                });
            });

            // Add CSS for spinner animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
            // Password confirmation validation
            const newPasswordInput = document.getElementById('new_password');
            const confirmPasswordInput = document.getElementById('confirm_password');
            
            function validatePasswordMatch() {
                if (confirmPasswordInput.value && newPasswordInput.value !== confirmPasswordInput.value) {
                    confirmPasswordInput.setCustomValidity('Passwords do not match');
                } else {
                    confirmPasswordInput.setCustomValidity('');
                }
            }
            
            newPasswordInput.addEventListener('input', validatePasswordMatch);
            confirmPasswordInput.addEventListener('input', validatePasswordMatch);
            
            // Form validation
            document.querySelector('.password-form').addEventListener('submit', function(e) {
                validatePasswordMatch();
                if (!confirmPasswordInput.checkValidity()) {
                    e.preventDefault();
                    alert('Please ensure passwords match');
                }
            });
        });
    </script>
</body>
</html>
