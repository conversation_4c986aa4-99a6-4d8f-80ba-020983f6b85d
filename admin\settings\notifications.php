<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Notification Settings Management
 *
 * @version 1.0
 * <AUTHOR> Development Team
 */

declare(strict_types=1);

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication
requireAuth();

// Only super admin can access this
if (getCurrentUser()['role'] !== 'super_admin') {
    header('Location: ../dashboard.php?message=Access denied&type=error');
    exit;
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] === 'update_notification') {
            updateNotificationSettings($_POST);
            $message = 'Notification settings updated successfully';
            $messageType = 'success';
        } else {
            throw new Exception('Invalid action');
        }
    } catch (Exception $e) {
        $message = 'Error: ' . $e->getMessage();
        $messageType = 'error';
    }
}

/**
 * Helper Functions for Notification Settings Management
 */

function getAllSettings(): array {
    $result = fetchAll("SELECT setting_key, setting_value, description FROM system_settings ORDER BY setting_key");
    $settings = [];
    foreach ($result as $row) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }
    return $settings;
}

function updateNotificationSettings(array $data): void {
    $validSettings = [
        'email_notifications' => 'Enable email notifications (1=yes, 0=no)',
        'smtp_host' => 'SMTP server hostname',
        'smtp_port' => 'SMTP server port',
        'smtp_username' => 'SMTP authentication username',
        'smtp_password' => 'SMTP authentication password',
        'smtp_encryption' => 'SMTP encryption method (tls/ssl)',
        'notification_from_email' => 'From email address for notifications',
        'notification_from_name' => 'From name for notifications'
    ];

    foreach ($validSettings as $key => $description) {
        if (isset($data[$key])) {
            $value = sanitizeInput($data[$key]);
            setSetting($key, $value, $description);
        }
    }
}

// Get current settings
$settings = getAllSettings();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notification Settings - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>

    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1>Notification Settings</h1>
                <nav class="breadcrumb">
                    <a href="../dashboard.php">Dashboard</a>
                    <span>/</span>
                    <span>Settings</span>
                </nav>
            </div>
        </div>

        <!-- Settings Tab Navigation -->
        <div class="profile-tabs-container">
            <nav class="profile-tabs">
                <a href="index.php" class="profile-tab">
                    <i class="icon-settings"></i>
                    General
                </a>
                <a href="security.php" class="profile-tab">
                    <i class="icon-shield"></i>
                    Security
                </a>
                <a href="events.php" class="profile-tab">
                    <i class="icon-trophy"></i>
                    Events
                </a>
                <a href="notifications.php" class="profile-tab active">
                    <i class="icon-mail"></i>
                    Notifications
                </a>
                <a href="system.php" class="profile-tab">
                    <i class="icon-info"></i>
                    System Info
                </a>
            </nav>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?>">
                <i class="icon-<?php echo $messageType === 'success' ? 'check' : 'warning'; ?>"></i>
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <div class="settings-container">
            <!-- Notification Settings Panel -->
            <div class="settings-content">
                <div class="settings-panel active" id="notification-panel">
                    <div class="panel-header">
                        <h2>Notification Settings</h2>
                        <p>Configure email notifications and SMTP settings</p>
                    </div>

                    <form method="POST" class="settings-form">
                        <input type="hidden" name="action" value="update_notification">

                        <div class="form-grid">
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="email_notifications" value="1"
                                           <?php echo ($settings['email_notifications'] ?? '0') === '1' ? 'checked' : ''; ?>>
                                    <span class="checkmark"></span>
                                    Enable Email Notifications
                                </label>
                                <small>Send email notifications for important events</small>
                            </div>

                            <div class="form-group">
                                <label for="notification_from_email">From Email Address</label>
                                <input type="email" id="notification_from_email" name="notification_from_email"
                                       value="<?php echo htmlspecialchars($settings['notification_from_email'] ?? ''); ?>"
                                       maxlength="100">
                                <small>Email address used as sender for notifications</small>
                            </div>

                            <div class="form-group">
                                <label for="notification_from_name">From Name</label>
                                <input type="text" id="notification_from_name" name="notification_from_name"
                                       value="<?php echo htmlspecialchars($settings['notification_from_name'] ?? ''); ?>"
                                       maxlength="100">
                                <small>Display name for notification emails</small>
                            </div>

                            <div class="form-group">
                                <label for="smtp_host">SMTP Host</label>
                                <input type="text" id="smtp_host" name="smtp_host"
                                       value="<?php echo htmlspecialchars($settings['smtp_host'] ?? ''); ?>"
                                       maxlength="100">
                                <small>SMTP server hostname (e.g., smtp.gmail.com)</small>
                            </div>

                            <div class="form-group">
                                <label for="smtp_port">SMTP Port</label>
                                <input type="number" id="smtp_port" name="smtp_port"
                                       value="<?php echo (int)($settings['smtp_port'] ?? 587); ?>"
                                       min="1" max="65535">
                                <small>SMTP server port (usually 587 for TLS, 465 for SSL)</small>
                            </div>

                            <div class="form-group">
                                <label for="smtp_encryption">SMTP Encryption</label>
                                <select id="smtp_encryption" name="smtp_encryption">
                                    <?php
                                    $currentEnc = $settings['smtp_encryption'] ?? 'tls';
                                    $encryptions = ['none' => 'None', 'tls' => 'TLS', 'ssl' => 'SSL'];
                                    foreach ($encryptions as $value => $label): ?>
                                        <option value="<?php echo $value; ?>" <?php echo $currentEnc === $value ? 'selected' : ''; ?>>
                                            <?php echo $label; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <small>Encryption method for SMTP connection</small>
                            </div>

                            <div class="form-group">
                                <label for="smtp_username">SMTP Username</label>
                                <input type="text" id="smtp_username" name="smtp_username"
                                       value="<?php echo htmlspecialchars($settings['smtp_username'] ?? ''); ?>"
                                       maxlength="100">
                                <small>Username for SMTP authentication</small>
                            </div>

                            <div class="form-group">
                                <label for="smtp_password">SMTP Password</label>
                                <input type="password" id="smtp_password" name="smtp_password"
                                       value="<?php echo htmlspecialchars($settings['smtp_password'] ?? ''); ?>"
                                       maxlength="100">
                                <small>Password for SMTP authentication</small>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="icon-mail"></i>
                                Save Notification Settings
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="testEmailSettings()">
                                <i class="icon-send"></i>
                                Test Email
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>

    <script src="../../assets/js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Settings tab navigation enhancement
            const profileTabs = document.querySelectorAll('.profile-tab');
            profileTabs.forEach(tab => {
                tab.addEventListener('click', function(e) {
                    // Add loading state for better UX
                    if (!this.classList.contains('active')) {
                        this.style.opacity = '0.7';
                        this.innerHTML += ' <i class="icon-spinner" style="animation: spin 1s linear infinite;"></i>';
                    }
                });
            });
            
            // Add CSS for spinner animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        });

        // Global functions for button actions
        function testEmailSettings() {
            const form = document.querySelector('.settings-form');
            const formData = new FormData(form);
            
            fetch('test_email.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Test email sent successfully!');
                } else {
                    alert('Failed to send test email: ' + data.message);
                }
            })
            .catch(error => {
                alert('Error testing email settings');
            });
        }
    </script>
</body>
</html>
