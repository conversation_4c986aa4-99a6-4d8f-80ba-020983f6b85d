<?php
/**
 * Database Schema Fix: Add missing phone field and fix getCurrentUser issues
 * This script will fix all database-related issues for the profile system
 */

declare(strict_types=1);

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';

// Direct database connection for schema fixes
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
    
    echo "<h2>🔧 Database Schema Fix Tool</h2>";
    echo "<p>Checking and fixing database schema issues...</p>";
    
    // Step 1: Check current table structure
    echo "<h3>Step 1: Checking admin_users table structure</h3>";
    $stmt = $pdo->query("DESCRIBE admin_users");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    $hasPhone = false;
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>";
        
        if ($column['Field'] === 'phone') {
            $hasPhone = true;
        }
    }
    echo "</table>";
    
    // Step 2: Add phone field if missing
    if (!$hasPhone) {
        echo "<h3>Step 2: Adding missing phone field</h3>";
        try {
            $pdo->exec("ALTER TABLE admin_users ADD COLUMN phone VARCHAR(20) NULL AFTER full_name");
            echo "<p style='color: green;'>✅ Successfully added phone field to admin_users table</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error adding phone field: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<h3>Step 2: Phone field check</h3>";
        echo "<p style='color: green;'>✅ Phone field already exists</p>";
    }
    
    // Step 3: Test the getCurrentUser query
    echo "<h3>Step 3: Testing getCurrentUser query</h3>";
    try {
        $testQuery = "SELECT admin_id, username, email, full_name, phone, role, status, password_hash, 
                             created_at, updated_at, last_login, failed_login_attempts 
                      FROM admin_users LIMIT 1";
        $stmt = $pdo->query($testQuery);
        $testUser = $stmt->fetch();
        
        if ($testUser) {
            echo "<p style='color: green;'>✅ getCurrentUser query works correctly</p>";
            echo "<p><strong>Sample user data:</strong></p>";
            echo "<ul>";
            foreach ($testUser as $key => $value) {
                if ($key !== 'password_hash') { // Don't display password hash
                    echo "<li><strong>{$key}:</strong> " . ($value ?? 'NULL') . "</li>";
                }
            }
            echo "</ul>";
        } else {
            echo "<p style='color: orange;'>⚠️ No users found in database</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error testing getCurrentUser query: " . $e->getMessage() . "</p>";
    }
    
    // Step 4: Verify final table structure
    echo "<h3>Step 4: Final table structure verification</h3>";
    $stmt = $pdo->query("DESCRIBE admin_users");
    $finalColumns = $stmt->fetchAll();
    
    $requiredFields = ['admin_id', 'username', 'email', 'full_name', 'phone', 'role', 'status', 
                      'password_hash', 'created_at', 'updated_at', 'last_login', 'failed_login_attempts'];
    
    $missingFields = [];
    $existingFields = array_column($finalColumns, 'Field');
    
    foreach ($requiredFields as $field) {
        if (!in_array($field, $existingFields)) {
            $missingFields[] = $field;
        }
    }
    
    if (empty($missingFields)) {
        echo "<p style='color: green;'>✅ All required fields are present in the database</p>";
        echo "<p style='color: green;'><strong>Database schema is now compatible with the profile system!</strong></p>";
    } else {
        echo "<p style='color: red;'>❌ Missing fields: " . implode(', ', $missingFields) . "</p>";
    }
    
    echo "<h3>✅ Database Fix Complete</h3>";
    echo "<p><a href='../profile.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Profile Page</a></p>";
    echo "<p><a href='../change-password.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Change Password</a></p>";
    
} catch (Exception $e) {
    echo "<h2 style='color: red;'>❌ Database Connection Error</h2>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Please check your database configuration in includes/config.php</p>";
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Schema Fix - SCIMS</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h2, h3 {
            color: #333;
            margin-bottom: 15px;
        }
        p {
            line-height: 1.6;
            color: #666;
        }
        table {
            width: 100%;
            margin: 15px 0;
        }
        th, td {
            padding: 8px 12px;
            text-align: left;
            border: 1px solid #ddd;
        }
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
        ul {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        a {
            display: inline-block;
            margin: 5px 10px 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Database fix output appears above -->
    </div>
</body>
</html>
