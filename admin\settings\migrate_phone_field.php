<?php
/**
 * Database Migration: Add phone field to admin_users table
 * Run this script once to add the phone field to existing databases
 */

declare(strict_types=1);

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Check if user is logged in and is super admin
session_start();
if (!isset($_SESSION['admin_id'])) {
    die('Access denied. Please log in as super admin.');
}

$currentUser = getCurrentUser();
if (!$currentUser || $currentUser['role'] !== 'super_admin') {
    die('Access denied. Super admin privileges required.');
}

try {
    // Check if phone column already exists
    $checkSql = "SELECT COUNT(*) as count 
                 FROM INFORMATION_SCHEMA.COLUMNS 
                 WHERE TABLE_SCHEMA = DATABASE() 
                 AND TABLE_NAME = 'admin_users' 
                 AND COLUMN_NAME = 'phone'";
    
    $result = fetchOne($checkSql);
    
    if ($result['count'] > 0) {
        echo "<h2>✅ Migration Status: Already Complete</h2>";
        echo "<p>The 'phone' field already exists in the admin_users table.</p>";
        echo "<p><a href='../profile.php'>Go to Profile Page</a></p>";
        exit;
    }
    
    // Add phone column
    $migrationSql = "ALTER TABLE admin_users ADD COLUMN phone VARCHAR(20) NULL AFTER full_name";
    executeQuery($migrationSql);
    
    // Verify the change
    $verifyResult = fetchOne($checkSql);
    
    if ($verifyResult['count'] > 0) {
        echo "<h2>✅ Migration Successful!</h2>";
        echo "<p>The 'phone' field has been successfully added to the admin_users table.</p>";
        echo "<p>You can now use the profile management features.</p>";
        echo "<p><a href='../profile.php'>Go to Profile Page</a></p>";
        
        // Log the migration
        logActivity('database_migration', 'Added phone field to admin_users table');
        
    } else {
        throw new Exception('Migration verification failed');
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Migration Failed</h2>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Please check your database connection and permissions.</p>";
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Migration - SCIMS</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h2 {
            color: #333;
            margin-bottom: 20px;
        }
        p {
            line-height: 1.6;
            color: #666;
        }
        a {
            color: #007cba;
            text-decoration: none;
            font-weight: bold;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Migration output appears above -->
    </div>
</body>
</html>
