<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Tournament Scheduling Page - Step 3
 * Professional match scheduling with venue allocation and calendar integration
 * 
 * @version 2.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication and admin permission
requireAuth();
requirePermission('manage_events');

// Get parameters
$eventSportId = filter_input(INPUT_GET, 'event_sport_id', FILTER_VALIDATE_INT);
$eventId = filter_input(INPUT_GET, 'event_id', FILTER_VALIDATE_INT);

if (!$eventSportId || !$eventId) {
    header('Location: index.php?error=invalid_parameters');
    exit;
}

// Fetch event sport details
$eventSport = fetchOne("
    SELECT es.*, s.name as sport_name, s.category as sport_category, s.scoring_type,
           e.name as event_name, e.start_date, e.end_date
    FROM event_sports es
    INNER JOIN sports s ON es.sport_id = s.sport_id
    INNER JOIN events e ON es.event_id = e.event_id
    WHERE es.event_sport_id = ? AND es.event_id = ?
", [$eventSportId, $eventId]);

if (!$eventSport) {
    header('Location: index.php?error=event_sport_not_found');
    exit;
}

// Fetch tournament configuration from Step 2
$tournamentConfig = fetchOne("
    SELECT * FROM tournament_configs 
    WHERE event_sport_id = ?
", [$eventSportId]);

if (!$tournamentConfig || $tournamentConfig['status'] !== 'active') {
    header('Location: tournament_config.php?event_sport_id=' . $eventSportId . '&event_id=' . $eventId . '&error=bracket_not_configured');
    exit;
}

// Initialize variables
$message = '';
$messageType = '';
$csrfToken = bin2hex(random_bytes(32));
$_SESSION['csrf_token'] = $csrfToken;

// Get bracket data and extract matches
$bracketData = json_decode($tournamentConfig['bracket_data'], true);
$schedulableMatches = extractSchedulableMatches($bracketData, $eventSport);

// Fetch available venues
$venues = fetchAll("
    SELECT venue_id, name, location, capacity, facilities, status
    FROM venues
    WHERE status = 'available'
    ORDER BY name
");

// Create tournament_schedules table if it doesn't exist
if (!tableExists('tournament_schedules')) {
    executeQuery("
        CREATE TABLE tournament_schedules (
            schedule_id INT AUTO_INCREMENT PRIMARY KEY,
            config_id INT NOT NULL,
            schedule_name VARCHAR(255) NOT NULL,
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            time_slots JSON,
            venue_assignments JSON,
            scheduling_rules JSON,
            status ENUM('draft', 'published', 'active', 'completed') DEFAULT 'draft',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (config_id) REFERENCES tournament_configs(config_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
}

// Create scheduled_matches table if it doesn't exist
if (!tableExists('scheduled_matches')) {
    executeQuery("
        CREATE TABLE scheduled_matches (
            scheduled_match_id INT AUTO_INCREMENT PRIMARY KEY,
            schedule_id INT NOT NULL,
            match_id VARCHAR(50) NOT NULL,
            venue_id INT NOT NULL,
            scheduled_date DATE NOT NULL,
            scheduled_time TIME NOT NULL,
            estimated_duration INT DEFAULT 60,
            status ENUM('scheduled', 'confirmed', 'in_progress', 'completed', 'postponed') DEFAULT 'scheduled',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (schedule_id) REFERENCES tournament_schedules(schedule_id) ON DELETE CASCADE,
            FOREIGN KEY (venue_id) REFERENCES venues(venue_id) ON DELETE CASCADE,
            UNIQUE KEY unique_schedule_match (schedule_id, match_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
}

// Fetch existing tournament schedule
$tournamentSchedule = fetchOne("
    SELECT * FROM tournament_schedules
    WHERE config_id = ?
", [$tournamentConfig['config_id']]);

// Default time slots (can be customized)
$defaultTimeSlots = [
    '08:00:00' => '8:00 AM',
    '09:30:00' => '9:30 AM', 
    '11:00:00' => '11:00 AM',
    '13:00:00' => '1:00 PM',
    '14:30:00' => '2:30 PM',
    '16:00:00' => '4:00 PM',
    '17:30:00' => '5:30 PM'
];

// Get current time slots from schedule or use defaults
$timeSlots = $tournamentSchedule ? 
    json_decode($tournamentSchedule['time_slots'], true) : $defaultTimeSlots;

/**
 * Extract schedulable matches from bracket data
 */
function extractSchedulableMatches($bracketData, $eventSport) {
    $matches = [];
    
    if (!$bracketData || !isset($bracketData['format'])) {
        return $matches;
    }
    
    $format = $bracketData['format'];
    $matchCounter = 1;
    
    switch ($format) {
        case 'single_elimination':
            if (isset($bracketData['rounds'])) {
                foreach ($bracketData['rounds'] as $roundIndex => $round) {
                    if (isset($round['matches'])) {
                        foreach ($round['matches'] as $match) {
                            $matches[] = [
                                'match_id' => $match['match_id'],
                                'match_number' => 'M' . str_pad($matchCounter++, 3, '0', STR_PAD_LEFT),
                                'round_name' => $round['round_name'],
                                'round_index' => $roundIndex + 1,
                                'participant_1' => $match['participant_1'],
                                'participant_2' => $match['participant_2'],
                                'status' => $match['status'],
                                'sport_name' => $eventSport['sport_name'],
                                'estimated_duration' => getEstimatedDuration($eventSport['sport_category']),
                                'venue_requirements' => getVenueRequirements($eventSport['sport_name'])
                            ];
                        }
                    }
                }
            }
            break;
            
        case 'round_robin':
            if (isset($bracketData['matches'])) {
                foreach ($bracketData['matches'] as $match) {
                    $matches[] = [
                        'match_id' => $match['match_id'],
                        'match_number' => 'M' . str_pad($matchCounter++, 3, '0', STR_PAD_LEFT),
                        'round_name' => 'Round Robin',
                        'round_index' => 1,
                        'participant_1' => $match['participant_1'],
                        'participant_2' => $match['participant_2'],
                        'status' => $match['status'],
                        'sport_name' => $eventSport['sport_name'],
                        'estimated_duration' => getEstimatedDuration($eventSport['sport_category']),
                        'venue_requirements' => getVenueRequirements($eventSport['sport_name'])
                    ];
                }
            }
            break;
            
        default:
            // Handle other formats
            break;
    }
    
    return $matches;
}

/**
 * Get estimated match duration based on sport category
 */
function getEstimatedDuration($category) {
    $durations = [
        'team' => 90,           // 1.5 hours for team sports
        'individual' => 30,     // 30 minutes for individual
        'performing_arts' => 45, // 45 minutes for performances
        'academic' => 60,       // 1 hour for academic competitions
        'pageant' => 120        // 2 hours for pageants
    ];
    
    return $durations[$category] ?? 60;
}

/**
 * Get venue requirements for specific sports
 */
function getVenueRequirements($sportName) {
    $requirements = [
        'Basketball' => ['court', 'scoreboard', 'sound_system'],
        'Volleyball' => ['court', 'net', 'scoreboard'],
        'Swimming' => ['pool', 'timing_system'],
        'Track and Field' => ['track', 'field_events_area'],
        'Chess' => ['tables', 'chairs', 'quiet_environment'],
        'Badminton' => ['court', 'net', 'shuttlecocks']
    ];
    
    return $requirements[$sportName] ?? ['basic_venue'];
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $message = 'Invalid security token. Please try again.';
        $messageType = 'error';
    } else {
        $action = $_POST['action'] ?? '';

        try {
            switch ($action) {
                case 'save_schedule_config':
                    $startDate = $_POST['start_date'] ?? '';
                    $endDate = $_POST['end_date'] ?? '';
                    $customTimeSlots = $_POST['time_slots'] ?? [];
                    $scheduleName = $_POST['schedule_name'] ?? $eventSport['sport_name'] . ' Schedule';

                    // Validate dates
                    if (empty($startDate) || empty($endDate)) {
                        throw new Exception('Start date and end date are required.');
                    }

                    if (strtotime($startDate) > strtotime($endDate)) {
                        throw new Exception('Start date cannot be after end date.');
                    }



                    // Prepare schedule data
                    $scheduleData = [
                        'config_id' => $tournamentConfig['config_id'],
                        'schedule_name' => $scheduleName,
                        'start_date' => $startDate,
                        'end_date' => $endDate,
                        'time_slots' => json_encode($customTimeSlots ?: $defaultTimeSlots),
                        'status' => 'draft'
                    ];

                    if ($tournamentSchedule) {
                        updateRecord('tournament_schedules', $scheduleData, 'schedule_id = ?', [$tournamentSchedule['schedule_id']]);
                        $message = 'Schedule configuration updated successfully!';
                    } else {
                        insertRecord('tournament_schedules', $scheduleData);
                        $message = 'Schedule configuration saved successfully!';
                    }

                    $messageType = 'success';

                    // Refresh schedule data
                    $tournamentSchedule = fetchOne("
                        SELECT * FROM tournament_schedules
                        WHERE config_id = ?
                    ", [$tournamentConfig['config_id']]);

                    break;

                case 'schedule_matches':
                    if (!$tournamentSchedule) {
                        throw new Exception('Please configure schedule settings first.');
                    }

                    $matchSchedules = $_POST['match_schedules'] ?? [];



                    // Clear existing scheduled matches for this schedule
                    executeQuery("DELETE FROM scheduled_matches WHERE schedule_id = ?", [$tournamentSchedule['schedule_id']]);

                    // Insert new scheduled matches
                    $scheduledCount = 0;
                    foreach ($matchSchedules as $matchId => $scheduleInfo) {
                        if (!empty($scheduleInfo['date']) && !empty($scheduleInfo['time']) && !empty($scheduleInfo['venue_id'])) {
                            $scheduledMatchData = [
                                'schedule_id' => $tournamentSchedule['schedule_id'],
                                'match_id' => $matchId,
                                'venue_id' => (int)$scheduleInfo['venue_id'],
                                'scheduled_date' => $scheduleInfo['date'],
                                'scheduled_time' => $scheduleInfo['time'],
                                'estimated_duration' => (int)($scheduleInfo['duration'] ?? 60),
                                'notes' => $scheduleInfo['notes'] ?? ''
                            ];

                            insertRecord('scheduled_matches', $scheduledMatchData);
                            $scheduledCount++;
                        }
                    }

                    $message = "Successfully scheduled {$scheduledCount} matches!";
                    $messageType = 'success';

                    break;

                case 'auto_schedule':
                    if (!$tournamentSchedule) {
                        throw new Exception('Please configure schedule settings first.');
                    }

                    $autoScheduleResult = generateAutoSchedule($schedulableMatches, $venues, $tournamentSchedule);

                    if ($autoScheduleResult['success']) {
                        $message = "Auto-scheduling completed! {$autoScheduleResult['scheduled_count']} matches scheduled.";
                        $messageType = 'success';
                    } else {
                        $message = "Auto-scheduling failed: " . $autoScheduleResult['error'];
                        $messageType = 'error';
                    }

                    break;

                case 'publish_schedule':
                    if (!$tournamentSchedule) {
                        throw new Exception('No schedule configuration found.');
                    }

                    // Check if all matches are scheduled
                    $scheduledMatchesCount = fetchOne("
                        SELECT COUNT(*) as count
                        FROM scheduled_matches
                        WHERE schedule_id = ?
                    ", [$tournamentSchedule['schedule_id']])['count'];

                    if ($scheduledMatchesCount < count($schedulableMatches)) {
                        throw new Exception('Please schedule all matches before publishing.');
                    }

                    // Update schedule status to published
                    updateRecord('tournament_schedules', [
                        'status' => 'published'
                    ], 'schedule_id = ?', [$tournamentSchedule['schedule_id']]);

                    $message = 'Tournament schedule published successfully!';
                    $messageType = 'success';

                    // Refresh schedule data
                    $tournamentSchedule = fetchOne("
                        SELECT * FROM tournament_schedules
                        WHERE config_id = ?
                    ", [$tournamentConfig['config_id']]);

                    break;

                default:
                    throw new Exception('Invalid action specified.');
            }

        } catch (Exception $e) {
            $message = $e->getMessage();
            $messageType = 'error';
        }
    }
}

// Fetch existing scheduled matches
$scheduledMatches = [];
if ($tournamentSchedule) {
    $existingSchedules = fetchAll("
        SELECT sm.*, v.name as venue_name, v.location as venue_location
        FROM scheduled_matches sm
        LEFT JOIN venues v ON sm.venue_id = v.venue_id
        WHERE sm.schedule_id = ?
        ORDER BY sm.scheduled_date, sm.scheduled_time
    ", [$tournamentSchedule['schedule_id']]);

    foreach ($existingSchedules as $schedule) {
        $scheduledMatches[$schedule['match_id']] = $schedule;
    }
}

/**
 * Generate automatic schedule for matches
 */
function generateAutoSchedule($matches, $venues, $schedule) {
    try {
        if (empty($matches) || empty($venues)) {
            return ['success' => false, 'error' => 'No matches or venues available'];
        }

        $startDate = new DateTime($schedule['start_date']);
        $endDate = new DateTime($schedule['end_date']);
        $timeSlots = json_decode($schedule['time_slots'], true);

        if (empty($timeSlots)) {
            return ['success' => false, 'error' => 'No time slots configured'];
        }

        // Clear existing scheduled matches
        executeQuery("DELETE FROM scheduled_matches WHERE schedule_id = ?", [$schedule['schedule_id']]);

        $currentDate = clone $startDate;
        $scheduledCount = 0;
        $matchIndex = 0;
        $venueIndex = 0;

        // Simple round-robin scheduling algorithm
        while ($currentDate <= $endDate && $matchIndex < count($matches)) {
            // Skip weekends for now (can be made configurable)
            if ($currentDate->format('N') >= 6) {
                $currentDate->add(new DateInterval('P1D'));
                continue;
            }

            foreach ($timeSlots as $time => $label) {
                if ($matchIndex >= count($matches)) break;

                $match = $matches[$matchIndex];
                $venue = $venues[$venueIndex % count($venues)];

                // Check for conflicts (simplified)
                $conflictCheck = fetchOne("
                    SELECT COUNT(*) as count
                    FROM scheduled_matches
                    WHERE venue_id = ? AND scheduled_date = ? AND scheduled_time = ?
                ", [$venue['venue_id'], $currentDate->format('Y-m-d'), $time]);

                if ($conflictCheck['count'] == 0) {
                    // Schedule the match
                    $scheduledMatchData = [
                        'schedule_id' => $schedule['schedule_id'],
                        'match_id' => $match['match_id'],
                        'venue_id' => $venue['venue_id'],
                        'scheduled_date' => $currentDate->format('Y-m-d'),
                        'scheduled_time' => $time,
                        'estimated_duration' => $match['estimated_duration']
                    ];

                    insertRecord('scheduled_matches', $scheduledMatchData);
                    $scheduledCount++;
                    $matchIndex++;
                }

                $venueIndex++;
            }

            $currentDate->add(new DateInterval('P1D'));
        }

        return [
            'success' => true,
            'scheduled_count' => $scheduledCount,
            'remaining_matches' => count($matches) - $scheduledCount
        ];

    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Check for scheduling conflicts
 */
function checkSchedulingConflicts($venueId, $date, $time, $duration, $excludeMatchId = null) {
    $conflicts = [];

    // Check venue conflicts
    $venueConflicts = fetchAll("
        SELECT sm.*, v.name as venue_name
        FROM scheduled_matches sm
        JOIN venues v ON sm.venue_id = v.venue_id
        WHERE sm.venue_id = ?
        AND sm.scheduled_date = ?
        AND (
            (sm.scheduled_time <= ? AND ADDTIME(sm.scheduled_time, SEC_TO_TIME(sm.estimated_duration * 60)) > ?) OR
            (? <= sm.scheduled_time AND ADDTIME(?, SEC_TO_TIME(? * 60)) > sm.scheduled_time)
        )
        " . ($excludeMatchId ? "AND sm.match_id != ?" : ""),
        array_filter([
            $venueId, $date, $time, $time, $time, $time, $duration,
            $excludeMatchId
        ])
    );

    foreach ($venueConflicts as $conflict) {
        $conflicts[] = [
            'type' => 'venue',
            'message' => "Venue conflict with match {$conflict['match_id']} at {$conflict['scheduled_time']}"
        ];
    }

    return $conflicts;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tournament Scheduling - <?php echo htmlspecialchars($eventSport['sport_name']); ?> | <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <style>
        /* Tournament Scheduling Specific Styles */
        .scheduling-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }

        .tournament-header {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: white;
            padding: 30px;
            border-radius: var(--border-radius-lg);
            margin-bottom: 30px;
            box-shadow: var(--shadow-lg);
        }

        .tournament-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .tournament-header .sport-icon {
            font-size: 3rem;
        }

        .tournament-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .meta-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: var(--border-radius);
            backdrop-filter: blur(10px);
        }

        .meta-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-bottom: 5px;
        }

        .meta-value {
            font-size: 1.1rem;
            font-weight: 600;
        }

        .wizard-navigation {
            display: flex;
            justify-content: center;
            margin-bottom: 40px;
        }

        .wizard-steps {
            display: flex;
            align-items: center;
            background: white;
            padding: 20px 40px;
            border-radius: 50px;
            box-shadow: var(--shadow);
            gap: 30px;
        }

        .wizard-step {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 20px;
            border-radius: 25px;
            transition: var(--transition);
            cursor: pointer;
            text-decoration: none;
            color: var(--gray-600);
        }

        .wizard-step.active {
            background: #059669;
            color: white;
            transform: scale(1.05);
        }

        .wizard-step.completed {
            background: var(--success-color);
            color: white;
        }

        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: currentColor;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .wizard-step:not(.active):not(.completed) .step-number {
            background: var(--gray-300);
            color: var(--gray-600);
        }

        .step-title {
            font-weight: 600;
            white-space: nowrap;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 30px;
            min-height: 600px;
        }

        .left-panel {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow);
            overflow: hidden;
        }

        .right-panel {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow);
            overflow: hidden;
        }

        .panel-header {
            background: var(--gray-50);
            padding: 20px 25px;
            border-bottom: 1px solid var(--gray-200);
        }

        .panel-header h3 {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 5px;
        }

        .panel-header p {
            color: var(--gray-600);
            font-size: 0.9rem;
        }

        .panel-content {
            padding: 25px;
        }

        /* Schedule Configuration Styles */
        .config-section {
            margin-bottom: 30px;
        }

        .config-section h4 {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 15px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-group label {
            font-weight: 600;
            color: var(--gray-700);
            font-size: 0.9rem;
        }

        .form-control {
            padding: 10px 12px;
            border: 1px solid var(--gray-300);
            border-radius: var(--border-radius);
            font-size: 0.95rem;
            transition: var(--transition);
        }

        .form-control:focus {
            outline: none;
            border-color: #059669;
            box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
        }

        /* Calendar Styles */
        .calendar-container {
            min-height: 500px;
            position: relative;
        }

        .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: var(--gray-50);
            border-radius: var(--border-radius);
        }

        .calendar-navigation {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            background: var(--gray-200);
            border-radius: var(--border-radius);
            overflow: hidden;
        }

        .calendar-day-header {
            background: var(--gray-100);
            padding: 10px;
            text-align: center;
            font-weight: 600;
            color: var(--gray-700);
        }

        .calendar-day {
            background: white;
            min-height: 100px;
            padding: 8px;
            position: relative;
        }

        .day-number {
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 5px;
        }

        .day-matches {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .match-block {
            background: #059669;
            color: white;
            padding: 4px 6px;
            border-radius: 4px;
            font-size: 0.75rem;
            cursor: pointer;
            transition: var(--transition);
        }

        .match-block:hover {
            background: #047857;
            transform: scale(1.02);
        }

        /* Match List Styles */
        .matches-list {
            max-height: 500px;
            overflow-y: auto;
        }

        .match-item {
            border: 1px solid var(--gray-200);
            border-radius: var(--border-radius);
            padding: 15px;
            margin-bottom: 15px;
            background: white;
            transition: var(--transition);
        }

        .match-item:hover {
            border-color: #059669;
            box-shadow: var(--shadow-sm);
        }

        .match-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .match-title {
            font-weight: 600;
            color: var(--gray-900);
        }

        .match-round {
            background: var(--gray-100);
            color: var(--gray-700);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
        }

        .match-participants {
            margin-bottom: 15px;
            color: var(--gray-700);
        }

        .match-schedule-form {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 10px;
            align-items: end;
        }

        .schedule-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-scheduled {
            background: var(--success-color);
            color: white;
        }

        .status-unscheduled {
            background: var(--warning-color);
            color: white;
        }

        /* Action Buttons */
        .schedule-actions {
            display: flex;
            gap: 15px;
            padding: 20px;
            border-top: 1px solid var(--gray-200);
            background: var(--gray-50);
        }

        .btn-schedule {
            background: #059669;
            color: white;
        }

        .btn-schedule:hover {
            background: #047857;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .tournament-header h1 {
                font-size: 2rem;
            }

            .calendar-grid {
                grid-template-columns: 1fr;
            }

            .match-schedule-form {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }

        @media (max-width: 768px) {
            .scheduling-container {
                padding: 15px;
            }

            .wizard-steps {
                flex-direction: column;
                gap: 15px;
                padding: 20px;
            }

            .tournament-meta {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>

    <main class="admin-main">
        <div class="scheduling-container">
            <!-- Tournament Header -->
            <div class="tournament-header">
                <h1>
                    <span class="sport-icon">📅</span>
                    Tournament Scheduling
                </h1>
                <p style="font-size: 1.1rem; opacity: 0.9; margin-bottom: 0;">
                    Schedule matches for <strong><?php echo htmlspecialchars($eventSport['sport_name']); ?></strong>
                    in <?php echo htmlspecialchars($eventSport['event_name']); ?>
                </p>

                <div class="tournament-meta">
                    <div class="meta-item">
                        <div class="meta-label">Tournament Format</div>
                        <div class="meta-value"><?php echo ucfirst(str_replace('_', ' ', $tournamentConfig['tournament_format'])); ?></div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">Total Matches</div>
                        <div class="meta-value"><?php echo count($schedulableMatches); ?> Matches</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">Available Venues</div>
                        <div class="meta-value"><?php echo count($venues); ?> Venues</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">Schedule Status</div>
                        <div class="meta-value">
                            <?php
                            if ($tournamentSchedule) {
                                echo ucfirst($tournamentSchedule['status']);
                            } else {
                                echo 'Not Configured';
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Wizard Navigation -->
            <div class="wizard-navigation">
                <div class="wizard-steps">
                    <a href="create.php?event_id=<?php echo $eventId; ?>" class="wizard-step completed">
                        <div class="step-number">1</div>
                        <div class="step-title">Event Setup</div>
                    </a>
                    <a href="tournament_config.php?event_sport_id=<?php echo $eventSportId; ?>&event_id=<?php echo $eventId; ?>" class="wizard-step completed">
                        <div class="step-number">2</div>
                        <div class="step-title">Bracket Setup</div>
                    </a>
                    <div class="wizard-step active">
                        <div class="step-number">3</div>
                        <div class="step-title">Scheduling</div>
                    </div>
                    <div class="wizard-step">
                        <div class="step-number">4</div>
                        <div class="step-title">Configuration</div>
                    </div>
                </div>
            </div>

            <!-- Alert Messages -->
            <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?>">
                    <i class="icon-<?php echo $messageType; ?>"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <!-- Main Content Area -->
            <div class="main-content">
                <!-- Left Panel: Schedule Configuration -->
                <div class="left-panel">
                    <div class="panel-header">
                        <h3>⚙️ Schedule Configuration</h3>
                        <p>Configure dates, times, and scheduling rules</p>
                    </div>
                    <div class="panel-content">
                        <!-- Schedule Settings Form -->
                        <div class="config-section">
                            <h4>Schedule Settings</h4>
                            <form method="POST" id="scheduleConfigForm">
                                <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                <input type="hidden" name="action" value="save_schedule_config">

                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="schedule_name">Schedule Name</label>
                                        <input type="text" name="schedule_name" id="schedule_name" class="form-control"
                                               value="<?php echo $tournamentSchedule ? htmlspecialchars($tournamentSchedule['schedule_name']) : htmlspecialchars($eventSport['sport_name'] . ' Schedule'); ?>"
                                               required>
                                    </div>
                                    <div class="form-group">
                                        <label for="start_date">Start Date</label>
                                        <input type="date" name="start_date" id="start_date" class="form-control"
                                               value="<?php echo $tournamentSchedule ? $tournamentSchedule['start_date'] : $eventSport['start_date']; ?>"
                                               min="<?php echo $eventSport['start_date']; ?>"
                                               max="<?php echo $eventSport['end_date']; ?>"
                                               required>
                                    </div>
                                    <div class="form-group">
                                        <label for="end_date">End Date</label>
                                        <input type="date" name="end_date" id="end_date" class="form-control"
                                               value="<?php echo $tournamentSchedule ? $tournamentSchedule['end_date'] : $eventSport['end_date']; ?>"
                                               min="<?php echo $eventSport['start_date']; ?>"
                                               max="<?php echo $eventSport['end_date']; ?>"
                                               required>
                                    </div>
                                </div>

                                <div style="margin-top: 20px;">
                                    <button type="submit" class="btn btn-schedule">
                                        <i class="icon-check"></i>
                                        Save Configuration
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Time Slots Configuration -->
                        <div class="config-section">
                            <h4>Available Time Slots</h4>
                            <div class="time-slots-grid">
                                <?php foreach ($timeSlots as $time => $label): ?>
                                    <div class="time-slot-item">
                                        <span class="time-label"><?php echo htmlspecialchars($label); ?></span>
                                        <span class="time-value"><?php echo htmlspecialchars($time); ?></span>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="config-section">
                            <h4>Quick Actions</h4>
                            <div style="display: flex; flex-direction: column; gap: 10px;">
                                <?php if ($tournamentSchedule): ?>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                        <input type="hidden" name="action" value="auto_schedule">
                                        <button type="submit" class="btn btn-secondary" style="width: 100%;">
                                            <i class="icon-magic"></i>
                                            Auto-Schedule Matches
                                        </button>
                                    </form>

                                    <?php if ($tournamentSchedule['status'] === 'draft'): ?>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                            <input type="hidden" name="action" value="publish_schedule">
                                            <button type="submit" class="btn btn-schedule" style="width: 100%;">
                                                <i class="icon-publish"></i>
                                                Publish Schedule
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Venue Information -->
                        <div class="config-section">
                            <h4>Available Venues</h4>
                            <div class="venues-list">
                                <?php foreach ($venues as $venue): ?>
                                    <div class="venue-item">
                                        <div class="venue-name"><?php echo htmlspecialchars($venue['name']); ?></div>
                                        <div class="venue-details">
                                            <?php echo htmlspecialchars($venue['location']); ?>
                                            <?php if ($venue['capacity']): ?>
                                                • Capacity: <?php echo number_format($venue['capacity']); ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Panel: Match Scheduling -->
                <div class="right-panel">
                    <div class="panel-header">
                        <h3>📋 Match Scheduling</h3>
                        <p>Schedule individual matches with venues and times</p>
                    </div>
                    <div class="panel-content">
                        <?php if (empty($schedulableMatches)): ?>
                            <div class="empty-state">
                                <div class="empty-icon">📅</div>
                                <h4>No Matches Available</h4>
                                <p>Complete the bracket setup first to generate schedulable matches.</p>
                                <a href="tournament_config.php?event_sport_id=<?php echo $eventSportId; ?>&event_id=<?php echo $eventId; ?>"
                                   class="btn btn-primary">
                                    <i class="icon-arrow-left"></i>
                                    Back to Bracket Setup
                                </a>
                            </div>
                        <?php else: ?>
                            <form method="POST" id="matchSchedulingForm">
                                <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                <input type="hidden" name="action" value="schedule_matches">

                                <div class="matches-list">
                                    <?php foreach ($schedulableMatches as $match): ?>
                                        <?php
                                        $isScheduled = isset($scheduledMatches[$match['match_id']]);
                                        $scheduleInfo = $isScheduled ? $scheduledMatches[$match['match_id']] : null;
                                        ?>
                                        <div class="match-item">
                                            <div class="match-header">
                                                <div class="match-title">
                                                    <?php echo htmlspecialchars($match['match_number']); ?>
                                                    <span class="match-round"><?php echo htmlspecialchars($match['round_name']); ?></span>
                                                </div>
                                                <div class="schedule-status <?php echo $isScheduled ? 'status-scheduled' : 'status-unscheduled'; ?>">
                                                    <?php echo $isScheduled ? 'Scheduled' : 'Unscheduled'; ?>
                                                </div>
                                            </div>

                                            <div class="match-participants">
                                                <strong><?php echo htmlspecialchars($match['participant_1']); ?></strong>
                                                vs
                                                <strong><?php echo htmlspecialchars($match['participant_2']); ?></strong>
                                            </div>

                                            <div class="match-schedule-form">
                                                <div class="form-group">
                                                    <label>Date</label>
                                                    <input type="date"
                                                           name="match_schedules[<?php echo $match['match_id']; ?>][date]"
                                                           class="form-control"
                                                           value="<?php echo $scheduleInfo ? $scheduleInfo['scheduled_date'] : ''; ?>"
                                                           min="<?php echo $tournamentSchedule ? $tournamentSchedule['start_date'] : $eventSport['start_date']; ?>"
                                                           max="<?php echo $tournamentSchedule ? $tournamentSchedule['end_date'] : $eventSport['end_date']; ?>">
                                                </div>

                                                <div class="form-group">
                                                    <label>Time</label>
                                                    <select name="match_schedules[<?php echo $match['match_id']; ?>][time]"
                                                            class="form-control">
                                                        <option value="">Select Time</option>
                                                        <?php foreach ($timeSlots as $time => $label): ?>
                                                            <option value="<?php echo $time; ?>"
                                                                <?php echo ($scheduleInfo && $scheduleInfo['scheduled_time'] === $time) ? 'selected' : ''; ?>>
                                                                <?php echo htmlspecialchars($label); ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </div>

                                                <div class="form-group">
                                                    <label>Venue</label>
                                                    <select name="match_schedules[<?php echo $match['match_id']; ?>][venue_id]"
                                                            class="form-control">
                                                        <option value="">Select Venue</option>
                                                        <?php foreach ($venues as $venue): ?>
                                                            <option value="<?php echo $venue['venue_id']; ?>"
                                                                <?php echo ($scheduleInfo && $scheduleInfo['venue_id'] == $venue['venue_id']) ? 'selected' : ''; ?>>
                                                                <?php echo htmlspecialchars($venue['name']); ?>
                                                                <?php if ($venue['location']): ?>
                                                                    - <?php echo htmlspecialchars($venue['location']); ?>
                                                                <?php endif; ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </div>
                                            </div>

                                            <div class="match-details">
                                                <small>
                                                    Duration: <?php echo $match['estimated_duration']; ?> minutes
                                                    <?php if ($scheduleInfo): ?>
                                                        | Venue: <?php echo htmlspecialchars($scheduleInfo['venue_name']); ?>
                                                        <?php if ($scheduleInfo['venue_location']): ?>
                                                            (<?php echo htmlspecialchars($scheduleInfo['venue_location']); ?>)
                                                        <?php endif; ?>
                                                    <?php endif; ?>
                                                </small>
                                            </div>

                                            <!-- Hidden fields for additional data -->
                                            <input type="hidden"
                                                   name="match_schedules[<?php echo $match['match_id']; ?>][duration]"
                                                   value="<?php echo $match['estimated_duration']; ?>">
                                        </div>
                                    <?php endforeach; ?>
                                </div>

                                <div class="schedule-actions">
                                    <button type="submit" class="btn btn-schedule">
                                        <i class="icon-save"></i>
                                        Save Match Schedules
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="clearAllSchedules()">
                                        <i class="icon-clear"></i>
                                        Clear All
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="validateSchedules()">
                                        <i class="icon-check"></i>
                                        Validate Schedules
                                    </button>
                                </div>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Tournament Scheduling JavaScript
        let schedulableMatches = <?php echo json_encode($schedulableMatches); ?>;
        let venues = <?php echo json_encode($venues); ?>;
        let timeSlots = <?php echo json_encode($timeSlots); ?>;
        let scheduledMatches = <?php echo json_encode($scheduledMatches); ?>;

        // Initialize scheduling interface
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Tournament Scheduling Page Loaded');
            console.log('Schedulable matches:', schedulableMatches.length);
            console.log('Available venues:', venues.length);

            // Initialize form validation
            initializeFormValidation();

            // Initialize conflict detection
            initializeConflictDetection();

            // Auto-save functionality
            initializeAutoSave();
        });

        // Form validation
        function initializeFormValidation() {
            const form = document.getElementById('matchSchedulingForm');
            if (form) {
                form.addEventListener('submit', function(e) {
                    if (!validateAllSchedules()) {
                        e.preventDefault();
                        showNotification('Please fix scheduling conflicts before saving.', 'error');
                    }
                });
            }
        }

        // Conflict detection
        function initializeConflictDetection() {
            const dateInputs = document.querySelectorAll('input[type="date"]');
            const timeSelects = document.querySelectorAll('select[name*="[time]"]');
            const venueSelects = document.querySelectorAll('select[name*="[venue_id]"]');

            [...dateInputs, ...timeSelects, ...venueSelects].forEach(input => {
                input.addEventListener('change', function() {
                    checkForConflicts();
                });
            });
        }

        // Auto-save functionality
        function initializeAutoSave() {
            let autoSaveTimeout;
            const formInputs = document.querySelectorAll('#matchSchedulingForm input, #matchSchedulingForm select');

            formInputs.forEach(input => {
                input.addEventListener('change', function() {
                    clearTimeout(autoSaveTimeout);
                    autoSaveTimeout = setTimeout(() => {
                        console.log('Auto-save triggered');
                        // Could implement auto-save here
                    }, 3000);
                });
            });
        }

        // Validate all schedules
        function validateAllSchedules() {
            const conflicts = checkForConflicts();
            return conflicts.length === 0;
        }

        // Check for scheduling conflicts
        function checkForConflicts() {
            const conflicts = [];
            const schedules = {};

            // Collect all current schedules
            document.querySelectorAll('.match-item').forEach(matchItem => {
                const matchId = getMatchIdFromItem(matchItem);
                const date = matchItem.querySelector('input[type="date"]').value;
                const time = matchItem.querySelector('select[name*="[time]"]').value;
                const venueId = matchItem.querySelector('select[name*="[venue_id]"]').value;

                if (date && time && venueId) {
                    const key = `${date}_${time}_${venueId}`;
                    if (!schedules[key]) {
                        schedules[key] = [];
                    }
                    schedules[key].push({
                        matchId: matchId,
                        element: matchItem
                    });
                }
            });

            // Check for conflicts
            Object.keys(schedules).forEach(key => {
                if (schedules[key].length > 1) {
                    schedules[key].forEach(schedule => {
                        conflicts.push({
                            matchId: schedule.matchId,
                            element: schedule.element,
                            type: 'venue_time_conflict',
                            message: 'Venue and time conflict with another match'
                        });
                    });
                }
            });

            // Update UI to show conflicts
            updateConflictDisplay(conflicts);

            return conflicts;
        }

        // Update conflict display
        function updateConflictDisplay(conflicts) {
            // Clear previous conflict indicators
            document.querySelectorAll('.match-item').forEach(item => {
                item.classList.remove('has-conflict');
                const existingWarning = item.querySelector('.conflict-warning');
                if (existingWarning) {
                    existingWarning.remove();
                }
            });

            // Add conflict indicators
            conflicts.forEach(conflict => {
                conflict.element.classList.add('has-conflict');

                const warning = document.createElement('div');
                warning.className = 'conflict-warning';
                warning.innerHTML = `<i class="icon-warning"></i> ${conflict.message}`;
                conflict.element.appendChild(warning);
            });
        }

        // Get match ID from match item element
        function getMatchIdFromItem(matchItem) {
            const dateInput = matchItem.querySelector('input[type="date"]');
            if (dateInput && dateInput.name) {
                const match = dateInput.name.match(/match_schedules\[([^\]]+)\]/);
                return match ? match[1] : null;
            }
            return null;
        }

        // Clear all schedules
        function clearAllSchedules() {
            if (confirm('Are you sure you want to clear all match schedules?')) {
                document.querySelectorAll('#matchSchedulingForm input[type="date"]').forEach(input => {
                    input.value = '';
                });
                document.querySelectorAll('#matchSchedulingForm select').forEach(select => {
                    select.selectedIndex = 0;
                });

                // Update status indicators
                document.querySelectorAll('.schedule-status').forEach(status => {
                    status.className = 'schedule-status status-unscheduled';
                    status.textContent = 'Unscheduled';
                });

                showNotification('All schedules cleared.', 'info');
            }
        }

        // Validate schedules
        function validateSchedules() {
            const conflicts = checkForConflicts();
            const unscheduledMatches = [];

            // Check for unscheduled matches
            document.querySelectorAll('.match-item').forEach(matchItem => {
                const date = matchItem.querySelector('input[type="date"]').value;
                const time = matchItem.querySelector('select[name*="[time]"]').value;
                const venue = matchItem.querySelector('select[name*="[venue_id]"]').value;

                if (!date || !time || !venue) {
                    const matchTitle = matchItem.querySelector('.match-title').textContent.trim();
                    unscheduledMatches.push(matchTitle);
                }
            });

            // Show validation results
            let message = '';
            let type = 'success';

            if (conflicts.length > 0) {
                message = `Found ${conflicts.length} scheduling conflict(s).`;
                type = 'error';
            } else if (unscheduledMatches.length > 0) {
                message = `${unscheduledMatches.length} match(es) still need to be scheduled.`;
                type = 'warning';
            } else {
                message = 'All matches are properly scheduled with no conflicts!';
                type = 'success';
            }

            showNotification(message, type);
        }

        // Utility function to show notifications
        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `alert alert-${type}`;
            notification.style.position = 'fixed';
            notification.style.top = '20px';
            notification.style.right = '20px';
            notification.style.zIndex = '10000';
            notification.style.minWidth = '300px';
            notification.innerHTML = `
                <i class="icon-${type}"></i>
                ${message}
            `;

            document.body.appendChild(notification);

            // Auto remove after 4 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 4000);
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+S to save
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                const form = document.getElementById('matchSchedulingForm');
                if (form && validateAllSchedules()) {
                    form.submit();
                }
            }

            // Ctrl+Shift+V to validate
            if (e.ctrlKey && e.shiftKey && e.key === 'V') {
                e.preventDefault();
                validateSchedules();
            }
        });

        // Smart scheduling suggestions
        function suggestOptimalTime(matchElement) {
            // This could implement AI-powered scheduling suggestions
            // For now, just a placeholder
            console.log('Suggesting optimal time for match:', matchElement);
        }

        // Export schedule functionality
        function exportSchedule() {
            // This would implement schedule export
            showNotification('Schedule export feature coming soon!', 'info');
        }
    </script>

    <style>
        /* Additional styles for scheduling interface */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--gray-500);
        }

        .empty-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .time-slots-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }

        .time-slot-item {
            background: var(--gray-50);
            padding: 10px;
            border-radius: var(--border-radius);
            text-align: center;
            border: 1px solid var(--gray-200);
        }

        .time-label {
            display: block;
            font-weight: 600;
            color: var(--gray-900);
        }

        .time-value {
            display: block;
            font-size: 0.85rem;
            color: var(--gray-600);
        }

        .venues-list {
            max-height: 200px;
            overflow-y: auto;
        }

        .venue-item {
            padding: 10px;
            border-bottom: 1px solid var(--gray-200);
        }

        .venue-item:last-child {
            border-bottom: none;
        }

        .venue-name {
            font-weight: 600;
            color: var(--gray-900);
        }

        .venue-details {
            font-size: 0.85rem;
            color: var(--gray-600);
        }

        .match-item.has-conflict {
            border-color: var(--error-color);
            background: rgba(239, 68, 68, 0.05);
        }

        .conflict-warning {
            background: var(--error-color);
            color: white;
            padding: 8px 12px;
            border-radius: var(--border-radius);
            margin-top: 10px;
            font-size: 0.85rem;
        }

        .match-details {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid var(--gray-200);
        }

        /* Icon styles */
        .icon-magic::before { content: "🪄"; }
        .icon-publish::before { content: "📢"; }
        .icon-save::before { content: "💾"; }
        .icon-clear::before { content: "🗑️"; }
        .icon-warning::before { content: "⚠️"; }
    </style>
</body>
</html>
