<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Tournament Scheduling Page - Step 3
 * Professional match scheduling with venue allocation and calendar integration
 * 
 * @version 2.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication and admin permission
requireAuth();
requirePermission('manage_events');

// Get parameters
$eventSportId = filter_input(INPUT_GET, 'event_sport_id', FILTER_VALIDATE_INT);
$eventId = filter_input(INPUT_GET, 'event_id', FILTER_VALIDATE_INT);

if (!$eventSportId || !$eventId) {
    header('Location: index.php?error=invalid_parameters');
    exit;
}

// Fetch event sport details
$eventSport = fetchOne("
    SELECT es.*, s.name as sport_name, s.category as sport_category, s.scoring_type,
           e.name as event_name, e.start_date, e.end_date
    FROM event_sports es
    INNER JOIN sports s ON es.sport_id = s.sport_id
    INNER JOIN events e ON es.event_id = e.event_id
    WHERE es.event_sport_id = ? AND es.event_id = ?
", [$eventSportId, $eventId]);

if (!$eventSport) {
    header('Location: index.php?error=event_sport_not_found');
    exit;
}

// Fetch tournament configuration from Step 2
$tournamentConfig = fetchOne("
    SELECT * FROM tournament_configs 
    WHERE event_sport_id = ?
", [$eventSportId]);

if (!$tournamentConfig || $tournamentConfig['status'] !== 'active') {
    header('Location: tournament_config.php?event_sport_id=' . $eventSportId . '&event_id=' . $eventId . '&error=bracket_not_configured');
    exit;
}

// Initialize variables
$message = '';
$messageType = '';
$csrfToken = bin2hex(random_bytes(32));
$_SESSION['csrf_token'] = $csrfToken;

// Get bracket data and extract matches
$bracketData = json_decode($tournamentConfig['bracket_data'], true);
$schedulableMatches = extractSchedulableMatches($bracketData, $eventSport);

// Fetch available venues
$venues = fetchAll("
    SELECT venue_id, name, location, capacity, facilities, status
    FROM venues
    WHERE status = 'available'
    ORDER BY name
");

// Create tournament_schedules table if it doesn't exist
if (!tableExists('tournament_schedules')) {
    executeQuery("
        CREATE TABLE tournament_schedules (
            schedule_id INT AUTO_INCREMENT PRIMARY KEY,
            config_id INT NOT NULL,
            schedule_name VARCHAR(255) NOT NULL,
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            time_slots JSON,
            venue_assignments JSON,
            scheduling_rules JSON,
            status ENUM('draft', 'published', 'active', 'completed') DEFAULT 'draft',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (config_id) REFERENCES tournament_configs(config_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
}

// Create scheduled_matches table if it doesn't exist
if (!tableExists('scheduled_matches')) {
    executeQuery("
        CREATE TABLE scheduled_matches (
            scheduled_match_id INT AUTO_INCREMENT PRIMARY KEY,
            schedule_id INT NOT NULL,
            match_id VARCHAR(50) NOT NULL,
            venue_id INT NOT NULL,
            scheduled_date DATE NOT NULL,
            scheduled_time TIME NOT NULL,
            estimated_duration INT DEFAULT 60,
            status ENUM('scheduled', 'confirmed', 'in_progress', 'completed', 'postponed') DEFAULT 'scheduled',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (schedule_id) REFERENCES tournament_schedules(schedule_id) ON DELETE CASCADE,
            FOREIGN KEY (venue_id) REFERENCES venues(venue_id) ON DELETE CASCADE,
            UNIQUE KEY unique_schedule_match (schedule_id, match_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
}

// Fetch existing tournament schedule
$tournamentSchedule = fetchOne("
    SELECT * FROM tournament_schedules
    WHERE config_id = ?
", [$tournamentConfig['config_id']]);

// Default time slots (can be customized)
$defaultTimeSlots = [
    '08:00:00' => '8:00 AM',
    '09:30:00' => '9:30 AM', 
    '11:00:00' => '11:00 AM',
    '13:00:00' => '1:00 PM',
    '14:30:00' => '2:30 PM',
    '16:00:00' => '4:00 PM',
    '17:30:00' => '5:30 PM'
];

// Get current time slots from schedule or use defaults
$timeSlots = $tournamentSchedule ? 
    json_decode($tournamentSchedule['time_slots'], true) : $defaultTimeSlots;

/**
 * Extract schedulable matches from bracket data
 */
function extractSchedulableMatches($bracketData, $eventSport) {
    $matches = [];
    
    if (!$bracketData || !isset($bracketData['format'])) {
        return $matches;
    }
    
    $format = $bracketData['format'];
    $matchCounter = 1;
    
    switch ($format) {
        case 'single_elimination':
            if (isset($bracketData['rounds'])) {
                foreach ($bracketData['rounds'] as $roundIndex => $round) {
                    if (isset($round['matches'])) {
                        foreach ($round['matches'] as $match) {
                            $matches[] = [
                                'match_id' => $match['match_id'],
                                'match_number' => 'M' . str_pad($matchCounter++, 3, '0', STR_PAD_LEFT),
                                'round_name' => $round['round_name'],
                                'round_index' => $roundIndex + 1,
                                'participant_1' => $match['participant_1'],
                                'participant_2' => $match['participant_2'],
                                'status' => $match['status'],
                                'sport_name' => $eventSport['sport_name'],
                                'estimated_duration' => getEstimatedDuration($eventSport['sport_category']),
                                'venue_requirements' => getVenueRequirements($eventSport['sport_name'])
                            ];
                        }
                    }
                }
            }
            break;
            
        case 'round_robin':
            if (isset($bracketData['matches'])) {
                foreach ($bracketData['matches'] as $match) {
                    $matches[] = [
                        'match_id' => $match['match_id'],
                        'match_number' => 'M' . str_pad($matchCounter++, 3, '0', STR_PAD_LEFT),
                        'round_name' => 'Round Robin',
                        'round_index' => 1,
                        'participant_1' => $match['participant_1'],
                        'participant_2' => $match['participant_2'],
                        'status' => $match['status'],
                        'sport_name' => $eventSport['sport_name'],
                        'estimated_duration' => getEstimatedDuration($eventSport['sport_category']),
                        'venue_requirements' => getVenueRequirements($eventSport['sport_name'])
                    ];
                }
            }
            break;
            
        default:
            // Handle other formats
            break;
    }
    
    return $matches;
}

/**
 * Get estimated match duration based on sport category
 */
function getEstimatedDuration($category) {
    $durations = [
        'team' => 90,           // 1.5 hours for team sports
        'individual' => 30,     // 30 minutes for individual
        'performing_arts' => 45, // 45 minutes for performances
        'academic' => 60,       // 1 hour for academic competitions
        'pageant' => 120        // 2 hours for pageants
    ];
    
    return $durations[$category] ?? 60;
}

/**
 * Get venue requirements for specific sports
 */
function getVenueRequirements($sportName) {
    $requirements = [
        'Basketball' => ['court', 'scoreboard', 'sound_system'],
        'Volleyball' => ['court', 'net', 'scoreboard'],
        'Swimming' => ['pool', 'timing_system'],
        'Track and Field' => ['track', 'field_events_area'],
        'Chess' => ['tables', 'chairs', 'quiet_environment'],
        'Badminton' => ['court', 'net', 'shuttlecocks']
    ];
    
    return $requirements[$sportName] ?? ['basic_venue'];
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $message = 'Invalid security token. Please try again.';
        $messageType = 'error';
    } else {
        $action = $_POST['action'] ?? '';

        try {
            switch ($action) {
                case 'save_schedule_config':
                    $startDate = $_POST['start_date'] ?? '';
                    $endDate = $_POST['end_date'] ?? '';
                    $customTimeSlots = $_POST['time_slots'] ?? [];
                    $scheduleName = $_POST['schedule_name'] ?? $eventSport['sport_name'] . ' Schedule';

                    // Validate dates
                    if (empty($startDate) || empty($endDate)) {
                        throw new Exception('Start date and end date are required.');
                    }

                    if (strtotime($startDate) > strtotime($endDate)) {
                        throw new Exception('Start date cannot be after end date.');
                    }



                    // Prepare schedule data
                    $scheduleData = [
                        'config_id' => $tournamentConfig['config_id'],
                        'schedule_name' => $scheduleName,
                        'start_date' => $startDate,
                        'end_date' => $endDate,
                        'time_slots' => json_encode($customTimeSlots ?: $defaultTimeSlots),
                        'status' => 'draft'
                    ];

                    if ($tournamentSchedule) {
                        updateRecord('tournament_schedules', $scheduleData, 'schedule_id = ?', [$tournamentSchedule['schedule_id']]);
                        $message = 'Schedule configuration updated successfully!';
                    } else {
                        insertRecord('tournament_schedules', $scheduleData);
                        $message = 'Schedule configuration saved successfully!';
                    }

                    $messageType = 'success';

                    // Refresh schedule data
                    $tournamentSchedule = fetchOne("
                        SELECT * FROM tournament_schedules
                        WHERE config_id = ?
                    ", [$tournamentConfig['config_id']]);

                    break;

                case 'schedule_matches':
                    if (!$tournamentSchedule) {
                        throw new Exception('Please configure schedule settings first.');
                    }

                    $matchSchedules = $_POST['match_schedules'] ?? [];



                    // Clear existing scheduled matches for this schedule
                    executeQuery("DELETE FROM scheduled_matches WHERE schedule_id = ?", [$tournamentSchedule['schedule_id']]);

                    // Insert new scheduled matches
                    $scheduledCount = 0;
                    foreach ($matchSchedules as $matchId => $scheduleInfo) {
                        if (!empty($scheduleInfo['date']) && !empty($scheduleInfo['time']) && !empty($scheduleInfo['venue_id'])) {
                            $scheduledMatchData = [
                                'schedule_id' => $tournamentSchedule['schedule_id'],
                                'match_id' => $matchId,
                                'venue_id' => (int)$scheduleInfo['venue_id'],
                                'scheduled_date' => $scheduleInfo['date'],
                                'scheduled_time' => $scheduleInfo['time'],
                                'estimated_duration' => (int)($scheduleInfo['duration'] ?? 60),
                                'notes' => $scheduleInfo['notes'] ?? ''
                            ];

                            insertRecord('scheduled_matches', $scheduledMatchData);
                            $scheduledCount++;
                        }
                    }

                    $message = "Successfully scheduled {$scheduledCount} matches!";
                    $messageType = 'success';

                    break;

                case 'auto_schedule':
                    if (!$tournamentSchedule) {
                        throw new Exception('Please configure schedule settings first.');
                    }

                    $autoScheduleResult = generateAutoSchedule($schedulableMatches, $venues, $tournamentSchedule);

                    if ($autoScheduleResult['success']) {
                        $message = "Auto-scheduling completed! {$autoScheduleResult['scheduled_count']} matches scheduled.";
                        $messageType = 'success';
                    } else {
                        $message = "Auto-scheduling failed: " . $autoScheduleResult['error'];
                        $messageType = 'error';
                    }

                    break;

                case 'publish_schedule':
                    if (!$tournamentSchedule) {
                        throw new Exception('No schedule configuration found.');
                    }

                    // Check if all matches are scheduled
                    $scheduledMatchesCount = fetchOne("
                        SELECT COUNT(*) as count
                        FROM scheduled_matches
                        WHERE schedule_id = ?
                    ", [$tournamentSchedule['schedule_id']])['count'];

                    if ($scheduledMatchesCount < count($schedulableMatches)) {
                        throw new Exception('Please schedule all matches before publishing.');
                    }

                    // Update schedule status to published
                    updateRecord('tournament_schedules', [
                        'status' => 'published'
                    ], 'schedule_id = ?', [$tournamentSchedule['schedule_id']]);

                    $message = 'Tournament schedule published successfully!';
                    $messageType = 'success';

                    // Refresh schedule data
                    $tournamentSchedule = fetchOne("
                        SELECT * FROM tournament_schedules
                        WHERE config_id = ?
                    ", [$tournamentConfig['config_id']]);

                    break;

                default:
                    throw new Exception('Invalid action specified.');
            }

        } catch (Exception $e) {
            $message = $e->getMessage();
            $messageType = 'error';
        }
    }
}

// Fetch existing scheduled matches
$scheduledMatches = [];
if ($tournamentSchedule) {
    $existingSchedules = fetchAll("
        SELECT sm.*, v.name as venue_name, v.location as venue_location
        FROM scheduled_matches sm
        LEFT JOIN venues v ON sm.venue_id = v.venue_id
        WHERE sm.schedule_id = ?
        ORDER BY sm.scheduled_date, sm.scheduled_time
    ", [$tournamentSchedule['schedule_id']]);

    foreach ($existingSchedules as $schedule) {
        $scheduledMatches[$schedule['match_id']] = $schedule;
    }
}

/**
 * Generate automatic schedule for matches
 */
function generateAutoSchedule($matches, $venues, $schedule) {
    try {
        if (empty($matches) || empty($venues)) {
            return ['success' => false, 'error' => 'No matches or venues available'];
        }

        $startDate = new DateTime($schedule['start_date']);
        $endDate = new DateTime($schedule['end_date']);
        $timeSlots = json_decode($schedule['time_slots'], true);

        if (empty($timeSlots)) {
            return ['success' => false, 'error' => 'No time slots configured'];
        }

        // Clear existing scheduled matches
        executeQuery("DELETE FROM scheduled_matches WHERE schedule_id = ?", [$schedule['schedule_id']]);

        $currentDate = clone $startDate;
        $scheduledCount = 0;
        $matchIndex = 0;
        $venueIndex = 0;

        // Simple round-robin scheduling algorithm
        while ($currentDate <= $endDate && $matchIndex < count($matches)) {
            // Skip weekends for now (can be made configurable)
            if ($currentDate->format('N') >= 6) {
                $currentDate->add(new DateInterval('P1D'));
                continue;
            }

            foreach ($timeSlots as $time => $label) {
                if ($matchIndex >= count($matches)) break;

                $match = $matches[$matchIndex];
                $venue = $venues[$venueIndex % count($venues)];

                // Check for conflicts (simplified)
                $conflictCheck = fetchOne("
                    SELECT COUNT(*) as count
                    FROM scheduled_matches
                    WHERE venue_id = ? AND scheduled_date = ? AND scheduled_time = ?
                ", [$venue['venue_id'], $currentDate->format('Y-m-d'), $time]);

                if ($conflictCheck['count'] == 0) {
                    // Schedule the match
                    $scheduledMatchData = [
                        'schedule_id' => $schedule['schedule_id'],
                        'match_id' => $match['match_id'],
                        'venue_id' => $venue['venue_id'],
                        'scheduled_date' => $currentDate->format('Y-m-d'),
                        'scheduled_time' => $time,
                        'estimated_duration' => $match['estimated_duration']
                    ];

                    insertRecord('scheduled_matches', $scheduledMatchData);
                    $scheduledCount++;
                    $matchIndex++;
                }

                $venueIndex++;
            }

            $currentDate->add(new DateInterval('P1D'));
        }

        return [
            'success' => true,
            'scheduled_count' => $scheduledCount,
            'remaining_matches' => count($matches) - $scheduledCount
        ];

    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Check for scheduling conflicts
 */
function checkSchedulingConflicts($venueId, $date, $time, $duration, $excludeMatchId = null) {
    $conflicts = [];

    // Check venue conflicts
    $venueConflicts = fetchAll("
        SELECT sm.*, v.name as venue_name
        FROM scheduled_matches sm
        JOIN venues v ON sm.venue_id = v.venue_id
        WHERE sm.venue_id = ?
        AND sm.scheduled_date = ?
        AND (
            (sm.scheduled_time <= ? AND ADDTIME(sm.scheduled_time, SEC_TO_TIME(sm.estimated_duration * 60)) > ?) OR
            (? <= sm.scheduled_time AND ADDTIME(?, SEC_TO_TIME(? * 60)) > sm.scheduled_time)
        )
        " . ($excludeMatchId ? "AND sm.match_id != ?" : ""),
        array_filter([
            $venueId, $date, $time, $time, $time, $time, $duration,
            $excludeMatchId
        ])
    );

    foreach ($venueConflicts as $conflict) {
        $conflicts[] = [
            'type' => 'venue',
            'message' => "Venue conflict with match {$conflict['match_id']} at {$conflict['scheduled_time']}"
        ];
    }

    return $conflicts;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tournament Scheduling - <?php echo htmlspecialchars($eventSport['sport_name']); ?> | <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <style>
        /* Enhanced Tournament Scheduling Styles */
        .scheduling-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 24px;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            min-height: 100vh;
        }

        .tournament-header {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: white;
            padding: 32px;
            border-radius: var(--border-radius-lg);
            margin-bottom: 32px;
            box-shadow: 0 8px 32px rgba(5, 150, 105, 0.3);
            position: relative;
            overflow: hidden;
        }

        .tournament-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #10b981, #34d399, #6ee7b7);
        }

        .tournament-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .tournament-header .sport-icon {
            font-size: 3rem;
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
        }

        .tournament-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 20px;
            margin-top: 24px;
        }

        .meta-item {
            background: rgba(255, 255, 255, 0.15);
            padding: 18px;
            border-radius: var(--border-radius-lg);
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .meta-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .meta-label {
            font-size: 0.9rem;
            opacity: 0.85;
            margin-bottom: 6px;
            font-weight: 500;
        }

        .meta-value {
            font-size: 1.2rem;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .wizard-navigation {
            display: flex;
            justify-content: center;
            margin-bottom: 40px;
        }

        .wizard-steps {
            display: flex;
            align-items: center;
            background: white;
            padding: 24px 48px;
            border-radius: 60px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            gap: 32px;
            border: 1px solid var(--gray-200);
        }

        .wizard-step {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 24px;
            border-radius: 30px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            text-decoration: none;
            color: var(--gray-600);
            position: relative;
        }

        .wizard-step::after {
            content: '';
            position: absolute;
            right: -16px;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-left: 8px solid var(--gray-300);
            border-top: 6px solid transparent;
            border-bottom: 6px solid transparent;
        }

        .wizard-step:last-child::after {
            display: none;
        }

        .wizard-step.active {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: white;
            transform: scale(1.05);
            box-shadow: 0 4px 16px rgba(5, 150, 105, 0.3);
        }

        .wizard-step.active::after {
            border-left-color: #059669;
        }

        .wizard-step.completed {
            background: linear-gradient(135deg, var(--success-color) 0%, var(--success-600) 100%);
            color: white;
            box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);
        }

        .wizard-step.completed::after {
            border-left-color: var(--success-color);
        }

        .wizard-step:hover:not(.active) {
            background: var(--gray-100);
            transform: translateY(-2px);
        }

        .step-number {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: currentColor;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 0.9rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .wizard-step:not(.active):not(.completed) .step-number {
            background: var(--gray-300);
            color: var(--gray-600);
            box-shadow: none;
        }

        .step-title {
            font-weight: 600;
            white-space: nowrap;
            font-size: 0.95rem;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 32px;
            min-height: 600px;
        }

        .left-panel {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            border: 1px solid var(--gray-200);
            position: sticky;
            top: 20px;
            height: fit-content;
            max-height: calc(100vh - 200px);
            overflow-y: auto;
        }

        .right-panel {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            border: 1px solid var(--gray-200);
        }

        .panel-header {
            background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
            padding: 24px 28px;
            border-bottom: 1px solid var(--gray-200);
            position: relative;
        }

        .panel-header::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #059669, #10b981, #34d399);
        }

        .panel-header h3 {
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 6px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .panel-header p {
            color: var(--gray-600);
            font-size: 0.95rem;
            margin: 0;
            font-weight: 500;
        }

        .panel-content {
            padding: 28px;
        }

        /* Enhanced Schedule Configuration Styles */
        .config-section {
            margin-bottom: 32px;
            padding: 24px;
            background: var(--gray-50);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--gray-200);
        }

        .config-section h4 {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 18px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .config-section h4::before {
            content: '⚙️';
            font-size: 1.1rem;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 24px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .form-group label {
            font-weight: 600;
            color: var(--gray-700);
            font-size: 0.95rem;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .form-control {
            padding: 12px 16px;
            border: 2px solid var(--gray-300);
            border-radius: var(--border-radius-lg);
            font-size: 0.95rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: white;
        }

        .form-control:focus {
            outline: none;
            border-color: #059669;
            box-shadow: 0 0 0 4px rgba(5, 150, 105, 0.1);
            transform: translateY(-1px);
        }

        .form-control:hover:not(:focus) {
            border-color: var(--gray-400);
        }

        .form-control[type="date"],
        .form-control[type="time"] {
            cursor: pointer;
        }

        .form-control option {
            padding: 8px;
        }

        /* Enhanced Calendar Styles */
        .calendar-container {
            min-height: 500px;
            position: relative;
            background: white;
            border-radius: var(--border-radius-lg);
            padding: 20px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        }

        .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding: 20px;
            background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--gray-200);
        }

        .calendar-navigation {
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .calendar-nav-btn {
            padding: 8px 16px;
            background: white;
            border: 2px solid var(--gray-300);
            border-radius: var(--border-radius);
            color: var(--gray-700);
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .calendar-nav-btn:hover {
            border-color: #059669;
            color: #059669;
            transform: translateY(-1px);
        }

        .calendar-month-year {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--gray-900);
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 2px;
            background: var(--gray-200);
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .calendar-day-header {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            padding: 12px;
            text-align: center;
            font-weight: 700;
            color: white;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .calendar-day {
            background: white;
            min-height: 120px;
            padding: 10px;
            position: relative;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .calendar-day:hover {
            background: var(--gray-50);
            transform: scale(1.02);
        }

        .calendar-day.has-matches {
            background: linear-gradient(135deg, rgba(5, 150, 105, 0.05) 0%, rgba(5, 150, 105, 0.1) 100%);
        }

        .day-number {
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 8px;
            font-size: 1rem;
        }

        .day-matches {
            display: flex;
            flex-direction: column;
            gap: 3px;
        }

        .match-block {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: white;
            padding: 6px 8px;
            border-radius: var(--border-radius);
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(5, 150, 105, 0.3);
        }

        .match-block:hover {
            background: linear-gradient(135deg, #047857 0%, #065f46 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(5, 150, 105, 0.4);
        }

        /* Enhanced Match List Styles */
        .matches-list {
            max-height: 600px;
            overflow-y: auto;
            padding-right: 8px;
        }

        .matches-list::-webkit-scrollbar {
            width: 6px;
        }

        .matches-list::-webkit-scrollbar-track {
            background: var(--gray-100);
            border-radius: 3px;
        }

        .matches-list::-webkit-scrollbar-thumb {
            background: var(--gray-400);
            border-radius: 3px;
        }

        .matches-list::-webkit-scrollbar-thumb:hover {
            background: var(--gray-500);
        }

        .match-item {
            border: 2px solid var(--gray-200);
            border-radius: var(--border-radius-lg);
            padding: 20px;
            margin-bottom: 20px;
            background: white;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .match-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #059669, #10b981);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .match-item:hover {
            border-color: #059669;
            box-shadow: 0 8px 25px rgba(5, 150, 105, 0.15);
            transform: translateY(-2px);
        }

        .match-item:hover::before {
            transform: scaleX(1);
        }

        .match-item.scheduled {
            border-color: var(--success-color);
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.02) 0%, rgba(16, 185, 129, 0.05) 100%);
        }

        .match-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .match-title {
            font-weight: 700;
            color: var(--gray-900);
            font-size: 1.1rem;
        }

        .match-round {
            background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
            color: var(--gray-700);
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .match-participants {
            margin-bottom: 18px;
            color: var(--gray-700);
            font-weight: 500;
            padding: 12px;
            background: var(--gray-50);
            border-radius: var(--border-radius);
            border-left: 4px solid #059669;
        }

        .match-schedule-form {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 16px;
            align-items: end;
        }

        .schedule-status {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            text-align: center;
        }

        .status-scheduled {
            background: linear-gradient(135deg, var(--success-color) 0%, var(--success-600) 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
        }

        .status-unscheduled {
            background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-600) 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
        }

        .status-conflict {
            background: linear-gradient(135deg, var(--error-color) 0%, var(--error-600) 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
        }

        /* Enhanced Action Buttons */
        .schedule-actions {
            display: flex;
            gap: 16px;
            padding: 24px 28px;
            border-top: 1px solid var(--gray-200);
            background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
            justify-content: space-between;
            align-items: center;
        }

        .btn-schedule {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: white;
            padding: 12px 24px;
            border-radius: var(--border-radius-lg);
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
        }

        .btn-schedule:hover {
            background: linear-gradient(135deg, #047857 0%, #065f46 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(5, 150, 105, 0.4);
        }

        .btn-secondary {
            background: white;
            color: var(--gray-700);
            border: 2px solid var(--gray-300);
            padding: 10px 20px;
            border-radius: var(--border-radius-lg);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            border-color: var(--gray-400);
            background: var(--gray-50);
            transform: translateY(-1px);
        }

        .action-group {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        /* Enhanced Responsive Design */
        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr 1.5fr;
                gap: 24px;
            }

            .left-panel {
                position: static;
                max-height: none;
            }
        }

        @media (max-width: 1200px) {
            .scheduling-container {
                padding: 16px;
            }

            .wizard-steps {
                padding: 20px 36px;
                gap: 28px;
            }

            .wizard-step {
                min-width: 130px;
                padding: 12px 20px;
            }

            .tournament-meta {
                grid-template-columns: repeat(2, 1fr);
                gap: 16px;
            }

            .calendar-grid {
                grid-template-columns: repeat(7, 1fr);
                gap: 2px;
            }

            .calendar-day {
                min-height: 100px;
                padding: 8px;
            }

            .match-schedule-form {
                grid-template-columns: repeat(2, 1fr);
                gap: 20px;
            }
        }

        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 24px;
            }

            .tournament-header h1 {
                font-size: 2.2rem;
            }

            .calendar-grid {
                grid-template-columns: repeat(7, 1fr);
            }

            .match-schedule-form {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .wizard-steps {
                padding: 20px 32px;
                gap: 24px;
            }

            .left-panel {
                order: 1;
            }

            .right-panel {
                order: 2;
            }

            .schedule-actions {
                padding: 20px;
                gap: 16px;
            }

            .action-group {
                flex-wrap: wrap;
                gap: 12px;
            }
        }

        @media (max-width: 768px) {
            .scheduling-container {
                padding: 12px;
            }

            .wizard-steps {
                flex-direction: column;
                gap: 16px;
                padding: 20px;
                border-radius: 20px;
            }

            .wizard-step {
                width: 100%;
                min-width: auto;
                justify-content: flex-start;
                padding: 16px 20px;
            }

            .wizard-step::after {
                display: none;
            }

            .step-description {
                display: block;
            }

            .tournament-meta {
                grid-template-columns: 1fr;
                gap: 16px;
                text-align: center;
            }

            .tournament-header {
                padding: 24px 20px;
                text-align: center;
            }

            .tournament-header h1 {
                font-size: 1.8rem;
                flex-direction: column;
                text-align: center;
                gap: 12px;
            }

            .panel-content {
                padding: 20px;
            }

            .form-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .calendar-grid {
                grid-template-columns: repeat(7, 1fr);
                gap: 1px;
                font-size: 0.85rem;
            }

            .calendar-day {
                min-height: 80px;
                padding: 6px 4px;
            }

            .day-number {
                font-size: 0.9rem;
                margin-bottom: 4px;
            }

            .match-block {
                font-size: 0.7rem;
                padding: 4px 6px;
                margin-bottom: 2px;
            }

            .match-item {
                padding: 16px;
                margin-bottom: 12px;
            }

            .match-teams {
                flex-direction: column;
                gap: 8px;
                text-align: center;
            }

            .vs-separator {
                transform: rotate(90deg);
                margin: 4px 0;
            }

            .schedule-actions {
                flex-direction: column;
                gap: 12px;
                padding: 20px;
            }

            .action-group {
                width: 100%;
                justify-content: center;
                flex-wrap: wrap;
                gap: 8px;
            }

            .btn {
                min-height: 48px;
                font-size: 1rem;
                padding: 12px 20px;
            }

            .config-section {
                margin-bottom: 32px;
            }

            .panel-header h3 {
                font-size: 1.3rem;
            }

            .panel-header p {
                font-size: 0.9rem;
            }
        }

        @media (max-width: 480px) {
            .scheduling-container {
                padding: 12px;
            }

            .tournament-header {
                padding: 20px;
            }

            .tournament-header h1 {
                font-size: 1.5rem;
            }

            .panel-header {
                padding: 20px;
            }

            .panel-content {
                padding: 16px;
            }

            .calendar-day {
                min-height: 60px;
                padding: 4px;
            }

            .day-number {
                font-size: 0.9rem;
            }

            .match-block {
                font-size: 0.7rem;
                padding: 4px 6px;
            }
        }

        /* Enhanced Touch Device Support */
        @media (hover: none) and (pointer: coarse) {
            /* Disable hover effects on touch devices */
            .calendar-day:hover,
            .match-item:hover,
            .wizard-step:hover,
            .btn:hover {
                transform: none;
                box-shadow: var(--shadow);
            }

            /* Enhanced touch targets */
            .calendar-day {
                min-height: 100px;
                padding: 12px 8px;
                cursor: pointer;
                -webkit-tap-highlight-color: rgba(37, 99, 235, 0.1);
            }

            .match-item {
                cursor: pointer;
                min-height: 80px;
                padding: 20px;
                -webkit-tap-highlight-color: rgba(37, 99, 235, 0.1);
            }

            .btn {
                min-height: 52px;
                font-size: 1.1rem;
                padding: 16px 24px;
                -webkit-tap-highlight-color: transparent;
            }

            .wizard-step {
                min-height: 60px;
                padding: 16px 24px;
                -webkit-tap-highlight-color: rgba(37, 99, 235, 0.1);
            }

            /* Better form controls for touch */
            .form-control,
            select,
            input[type="text"],
            input[type="number"],
            input[type="date"],
            input[type="time"],
            textarea {
                min-height: 48px;
                font-size: 16px; /* Prevents zoom on iOS */
                padding: 12px 16px;
                -webkit-appearance: none;
                appearance: none;
                border-radius: 8px;
            }

            /* Enhanced focus states for touch navigation */
            .calendar-day:focus,
            .match-item:focus,
            .wizard-step:focus {
                outline: 3px solid var(--primary-color);
                outline-offset: 2px;
            }

            /* Improved spacing for touch interactions */
            .calendar-grid {
                gap: 4px;
            }

            .match-list .match-item {
                margin-bottom: 16px;
            }

            .schedule-actions .btn {
                margin: 8px 0;
            }

            /* Touch-friendly scrolling */
            .match-list,
            .calendar-container {
                -webkit-overflow-scrolling: touch;
                scroll-behavior: smooth;
            }

            /* Enhanced match blocks for touch */
            .match-block {
                min-height: 32px;
                padding: 8px 10px;
                font-size: 0.8rem;
                cursor: pointer;
                -webkit-tap-highlight-color: rgba(37, 99, 235, 0.1);
            }

            /* Better venue selection for touch */
            .venue-item {
                min-height: 60px;
                padding: 16px;
                cursor: pointer;
                -webkit-tap-highlight-color: rgba(37, 99, 235, 0.1);
            }
        }

        /* Enhanced Form Validation Styles */
        .field-error {
            border-color: #ef4444 !important;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
            animation: shake 0.3s ease-in-out;
        }

        .field-success {
            border-color: #10b981 !important;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
        }

        .field-error-message {
            color: #ef4444;
            font-size: 0.85rem;
            margin-top: 4px;
            display: flex;
            align-items: center;
            gap: 4px;
            animation: fadeInUp 0.3s ease-out;
        }

        .conflict-highlight {
            background-color: rgba(239, 68, 68, 0.1) !important;
            border: 2px solid #ef4444 !important;
            animation: pulse 1s infinite;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(100%); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideOutRight {
            from { opacity: 1; transform: translateX(0); }
            to { opacity: 0; transform: translateX(100%); }
        }

        .btn.loading {
            position: relative;
            color: transparent !important;
            pointer-events: none;
        }

        .btn.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .validation-progress {
            height: 4px;
            background: #e5e7eb;
            border-radius: 2px;
            overflow: hidden;
            margin-top: 8px;
        }

        .validation-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #10b981, #059669);
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        /* High DPI displays */
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
            .sport-icon,
            .empty-icon {
                image-rendering: -webkit-optimize-contrast;
                image-rendering: crisp-edges;
            }
        }
    </style>
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>

    <main class="admin-main">
        <div class="scheduling-container">
            <!-- Tournament Header -->
            <div class="tournament-header">
                <h1>
                    <span class="sport-icon">📅</span>
                    Tournament Scheduling
                </h1>
                <p style="font-size: 1.1rem; opacity: 0.9; margin-bottom: 0;">
                    Schedule matches for <strong><?php echo htmlspecialchars($eventSport['sport_name']); ?></strong>
                    in <?php echo htmlspecialchars($eventSport['event_name']); ?>
                </p>

                <div class="tournament-meta">
                    <div class="meta-item">
                        <div class="meta-label">Tournament Format</div>
                        <div class="meta-value"><?php echo ucfirst(str_replace('_', ' ', $tournamentConfig['tournament_format'])); ?></div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">Total Matches</div>
                        <div class="meta-value"><?php echo count($schedulableMatches); ?> Matches</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">Available Venues</div>
                        <div class="meta-value"><?php echo count($venues); ?> Venues</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">Schedule Status</div>
                        <div class="meta-value">
                            <?php
                            if ($tournamentSchedule) {
                                echo ucfirst($tournamentSchedule['status']);
                            } else {
                                echo 'Not Configured';
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Wizard Navigation -->
            <div class="wizard-navigation">
                <div class="wizard-steps">
                    <a href="create.php?event_id=<?php echo $eventId; ?>" class="wizard-step completed">
                        <div class="step-number">1</div>
                        <div class="step-title">Event Setup</div>
                    </a>
                    <a href="tournament_config.php?event_sport_id=<?php echo $eventSportId; ?>&event_id=<?php echo $eventId; ?>" class="wizard-step completed">
                        <div class="step-number">2</div>
                        <div class="step-title">Bracket Setup</div>
                    </a>
                    <div class="wizard-step active">
                        <div class="step-number">3</div>
                        <div class="step-title">Scheduling</div>
                    </div>
                    <div class="wizard-step">
                        <div class="step-number">4</div>
                        <div class="step-title">Configuration</div>
                    </div>
                </div>
            </div>

            <!-- Alert Messages -->
            <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?>">
                    <i class="icon-<?php echo $messageType; ?>"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <!-- Main Content Area -->
            <div class="main-content">
                <!-- Left Panel: Schedule Configuration -->
                <div class="left-panel">
                    <div class="panel-header">
                        <h3>⚙️ Schedule Configuration</h3>
                        <p>Configure dates, times, and scheduling rules</p>
                    </div>
                    <div class="panel-content">
                        <!-- Schedule Settings Form -->
                        <div class="config-section">
                            <h4>Schedule Settings</h4>
                            <form method="POST" id="scheduleConfigForm">
                                <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                <input type="hidden" name="action" value="save_schedule_config">

                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="schedule_name">Schedule Name</label>
                                        <input type="text" name="schedule_name" id="schedule_name" class="form-control"
                                               value="<?php echo $tournamentSchedule ? htmlspecialchars($tournamentSchedule['schedule_name']) : htmlspecialchars($eventSport['sport_name'] . ' Schedule'); ?>"
                                               required>
                                    </div>
                                    <div class="form-group">
                                        <label for="start_date">Start Date</label>
                                        <input type="date" name="start_date" id="start_date" class="form-control"
                                               value="<?php echo $tournamentSchedule ? $tournamentSchedule['start_date'] : $eventSport['start_date']; ?>"
                                               min="<?php echo $eventSport['start_date']; ?>"
                                               max="<?php echo $eventSport['end_date']; ?>"
                                               required>
                                    </div>
                                    <div class="form-group">
                                        <label for="end_date">End Date</label>
                                        <input type="date" name="end_date" id="end_date" class="form-control"
                                               value="<?php echo $tournamentSchedule ? $tournamentSchedule['end_date'] : $eventSport['end_date']; ?>"
                                               min="<?php echo $eventSport['start_date']; ?>"
                                               max="<?php echo $eventSport['end_date']; ?>"
                                               required>
                                    </div>
                                </div>

                                <div style="margin-top: 20px;">
                                    <button type="submit" class="btn btn-schedule">
                                        <i class="icon-check"></i>
                                        Save Configuration
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Time Slots Configuration -->
                        <div class="config-section">
                            <h4>Available Time Slots</h4>
                            <div class="time-slots-grid">
                                <?php foreach ($timeSlots as $time => $label): ?>
                                    <div class="time-slot-item">
                                        <span class="time-label"><?php echo htmlspecialchars($label); ?></span>
                                        <span class="time-value"><?php echo htmlspecialchars($time); ?></span>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="config-section">
                            <h4>Quick Actions</h4>
                            <div style="display: flex; flex-direction: column; gap: 10px;">
                                <?php if ($tournamentSchedule): ?>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                        <input type="hidden" name="action" value="auto_schedule">
                                        <button type="submit" class="btn btn-secondary" style="width: 100%;">
                                            <i class="icon-magic"></i>
                                            Auto-Schedule Matches
                                        </button>
                                    </form>

                                    <?php if ($tournamentSchedule['status'] === 'draft'): ?>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                            <input type="hidden" name="action" value="publish_schedule">
                                            <button type="submit" class="btn btn-schedule" style="width: 100%;">
                                                <i class="icon-publish"></i>
                                                Publish Schedule
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Venue Information -->
                        <div class="config-section">
                            <h4>Available Venues</h4>
                            <div class="venues-list">
                                <?php foreach ($venues as $venue): ?>
                                    <div class="venue-item">
                                        <div class="venue-name"><?php echo htmlspecialchars($venue['name']); ?></div>
                                        <div class="venue-details">
                                            <?php echo htmlspecialchars($venue['location']); ?>
                                            <?php if ($venue['capacity']): ?>
                                                • Capacity: <?php echo number_format($venue['capacity']); ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Panel: Match Scheduling -->
                <div class="right-panel">
                    <div class="panel-header">
                        <h3>📋 Match Scheduling</h3>
                        <p>Schedule individual matches with venues and times</p>
                    </div>
                    <div class="panel-content">
                        <?php if (empty($schedulableMatches)): ?>
                            <div class="empty-state">
                                <div class="empty-icon">📅</div>
                                <h4>No Matches Available</h4>
                                <p>Complete the bracket setup first to generate schedulable matches.</p>
                                <a href="tournament_config.php?event_sport_id=<?php echo $eventSportId; ?>&event_id=<?php echo $eventId; ?>"
                                   class="btn btn-primary">
                                    <i class="icon-arrow-left"></i>
                                    Back to Bracket Setup
                                </a>
                            </div>
                        <?php else: ?>
                            <form method="POST" id="matchSchedulingForm">
                                <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                <input type="hidden" name="action" value="schedule_matches">

                                <div class="matches-list">
                                    <?php foreach ($schedulableMatches as $match): ?>
                                        <?php
                                        $isScheduled = isset($scheduledMatches[$match['match_id']]);
                                        $scheduleInfo = $isScheduled ? $scheduledMatches[$match['match_id']] : null;
                                        ?>
                                        <div class="match-item">
                                            <div class="match-header">
                                                <div class="match-title">
                                                    <?php echo htmlspecialchars($match['match_number']); ?>
                                                    <span class="match-round"><?php echo htmlspecialchars($match['round_name']); ?></span>
                                                </div>
                                                <div class="schedule-status <?php echo $isScheduled ? 'status-scheduled' : 'status-unscheduled'; ?>">
                                                    <?php echo $isScheduled ? 'Scheduled' : 'Unscheduled'; ?>
                                                </div>
                                            </div>

                                            <div class="match-participants">
                                                <strong><?php echo htmlspecialchars($match['participant_1']); ?></strong>
                                                vs
                                                <strong><?php echo htmlspecialchars($match['participant_2']); ?></strong>
                                            </div>

                                            <div class="match-schedule-form">
                                                <div class="form-group">
                                                    <label>Date</label>
                                                    <input type="date"
                                                           name="match_schedules[<?php echo $match['match_id']; ?>][date]"
                                                           class="form-control"
                                                           value="<?php echo $scheduleInfo ? $scheduleInfo['scheduled_date'] : ''; ?>"
                                                           min="<?php echo $tournamentSchedule ? $tournamentSchedule['start_date'] : $eventSport['start_date']; ?>"
                                                           max="<?php echo $tournamentSchedule ? $tournamentSchedule['end_date'] : $eventSport['end_date']; ?>">
                                                </div>

                                                <div class="form-group">
                                                    <label>Time</label>
                                                    <select name="match_schedules[<?php echo $match['match_id']; ?>][time]"
                                                            class="form-control">
                                                        <option value="">Select Time</option>
                                                        <?php foreach ($timeSlots as $time => $label): ?>
                                                            <option value="<?php echo $time; ?>"
                                                                <?php echo ($scheduleInfo && $scheduleInfo['scheduled_time'] === $time) ? 'selected' : ''; ?>>
                                                                <?php echo htmlspecialchars($label); ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </div>

                                                <div class="form-group">
                                                    <label>Venue</label>
                                                    <select name="match_schedules[<?php echo $match['match_id']; ?>][venue_id]"
                                                            class="form-control">
                                                        <option value="">Select Venue</option>
                                                        <?php foreach ($venues as $venue): ?>
                                                            <option value="<?php echo $venue['venue_id']; ?>"
                                                                <?php echo ($scheduleInfo && $scheduleInfo['venue_id'] == $venue['venue_id']) ? 'selected' : ''; ?>>
                                                                <?php echo htmlspecialchars($venue['name']); ?>
                                                                <?php if ($venue['location']): ?>
                                                                    - <?php echo htmlspecialchars($venue['location']); ?>
                                                                <?php endif; ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </div>
                                            </div>

                                            <div class="match-details">
                                                <small>
                                                    Duration: <?php echo $match['estimated_duration']; ?> minutes
                                                    <?php if ($scheduleInfo): ?>
                                                        | Venue: <?php echo htmlspecialchars($scheduleInfo['venue_name']); ?>
                                                        <?php if ($scheduleInfo['venue_location']): ?>
                                                            (<?php echo htmlspecialchars($scheduleInfo['venue_location']); ?>)
                                                        <?php endif; ?>
                                                    <?php endif; ?>
                                                </small>
                                            </div>

                                            <!-- Hidden fields for additional data -->
                                            <input type="hidden"
                                                   name="match_schedules[<?php echo $match['match_id']; ?>][duration]"
                                                   value="<?php echo $match['estimated_duration']; ?>">
                                        </div>
                                    <?php endforeach; ?>
                                </div>

                                <div class="schedule-actions">
                                    <button type="submit" class="btn btn-schedule">
                                        <i class="icon-save"></i>
                                        Save Match Schedules
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="clearAllSchedules()">
                                        <i class="icon-clear"></i>
                                        Clear All
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="validateSchedules()">
                                        <i class="icon-check"></i>
                                        Validate Schedules
                                    </button>
                                </div>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Tournament Scheduling JavaScript
        let schedulableMatches = <?php echo json_encode($schedulableMatches); ?>;
        let venues = <?php echo json_encode($venues); ?>;
        let timeSlots = <?php echo json_encode($timeSlots); ?>;
        let scheduledMatches = <?php echo json_encode($scheduledMatches); ?>;

        // Initialize scheduling interface
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Tournament Scheduling Page Loaded');
            console.log('Schedulable matches:', schedulableMatches.length);
            console.log('Available venues:', venues.length);

            // Initialize form validation
            initializeFormValidation();

            // Initialize conflict detection
            initializeConflictDetection();

            // Auto-save functionality
            initializeAutoSave();
        });

        // Enhanced form validation with comprehensive feedback
        function initializeFormValidation() {
            const form = document.getElementById('matchSchedulingForm');
            if (form) {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();

                    // Show loading state
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.classList.add('loading');
                        submitBtn.disabled = true;
                    }

                    // Perform comprehensive validation
                    performScheduleValidation(form, submitBtn);
                });
            }

            // Real-time validation for form fields
            const formInputs = document.querySelectorAll('#matchSchedulingForm input, #matchSchedulingForm select');
            formInputs.forEach(input => {
                input.addEventListener('blur', function() {
                    validateScheduleField(this);
                });

                input.addEventListener('change', function() {
                    validateScheduleField(this);
                    checkForConflicts();
                });
            });
        }

        function performScheduleValidation(form, submitBtn) {
            let validationStep = 0;
            const totalSteps = 4;

            showValidationProgress(++validationStep, totalSteps, 'Checking schedule completeness...');

            setTimeout(() => {
                const incompleteMatches = validateScheduleCompleteness();
                if (incompleteMatches.length > 0) {
                    resetSubmitButton(submitBtn);
                    showIncompleteMatchesError(incompleteMatches);
                    return;
                }

                showValidationProgress(++validationStep, totalSteps, 'Checking for conflicts...');

                setTimeout(() => {
                    const conflicts = checkForConflicts();
                    if (conflicts.length > 0) {
                        resetSubmitButton(submitBtn);
                        showConflictsError(conflicts);
                        return;
                    }

                    showValidationProgress(++validationStep, totalSteps, 'Validating venue capacity...');

                    setTimeout(() => {
                        const capacityIssues = validateVenueCapacity();
                        if (capacityIssues.length > 0) {
                            resetSubmitButton(submitBtn);
                            showCapacityError(capacityIssues);
                            return;
                        }

                        showValidationProgress(++validationStep, totalSteps, 'Finalizing schedule...');

                        setTimeout(() => {
                            showNotification('Schedule validation successful! Saving...', 'success', 3000);
                            form.submit();
                        }, 500);
                    }, 500);
                }, 500);
            }, 500);
        }

        // Conflict detection
        function initializeConflictDetection() {
            const dateInputs = document.querySelectorAll('input[type="date"]');
            const timeSelects = document.querySelectorAll('select[name*="[time]"]');
            const venueSelects = document.querySelectorAll('select[name*="[venue_id]"]');

            [...dateInputs, ...timeSelects, ...venueSelects].forEach(input => {
                input.addEventListener('change', function() {
                    checkForConflicts();
                });
            });
        }

        // Auto-save functionality
        function initializeAutoSave() {
            let autoSaveTimeout;
            const formInputs = document.querySelectorAll('#matchSchedulingForm input, #matchSchedulingForm select');

            formInputs.forEach(input => {
                input.addEventListener('change', function() {
                    clearTimeout(autoSaveTimeout);
                    autoSaveTimeout = setTimeout(() => {
                        console.log('Auto-save triggered');
                        // Could implement auto-save here
                    }, 3000);
                });
            });
        }

        // Validate all schedules
        function validateAllSchedules() {
            const conflicts = checkForConflicts();
            return conflicts.length === 0;
        }

        // Check for scheduling conflicts
        function checkForConflicts() {
            const conflicts = [];
            const schedules = {};

            // Collect all current schedules
            document.querySelectorAll('.match-item').forEach(matchItem => {
                const matchId = getMatchIdFromItem(matchItem);
                const date = matchItem.querySelector('input[type="date"]').value;
                const time = matchItem.querySelector('select[name*="[time]"]').value;
                const venueId = matchItem.querySelector('select[name*="[venue_id]"]').value;

                if (date && time && venueId) {
                    const key = `${date}_${time}_${venueId}`;
                    if (!schedules[key]) {
                        schedules[key] = [];
                    }
                    schedules[key].push({
                        matchId: matchId,
                        element: matchItem
                    });
                }
            });

            // Check for conflicts
            Object.keys(schedules).forEach(key => {
                if (schedules[key].length > 1) {
                    schedules[key].forEach(schedule => {
                        conflicts.push({
                            matchId: schedule.matchId,
                            element: schedule.element,
                            type: 'venue_time_conflict',
                            message: 'Venue and time conflict with another match'
                        });
                    });
                }
            });

            // Update UI to show conflicts
            updateConflictDisplay(conflicts);

            return conflicts;
        }

        // Update conflict display
        function updateConflictDisplay(conflicts) {
            // Clear previous conflict indicators
            document.querySelectorAll('.match-item').forEach(item => {
                item.classList.remove('has-conflict');
                const existingWarning = item.querySelector('.conflict-warning');
                if (existingWarning) {
                    existingWarning.remove();
                }
            });

            // Add conflict indicators
            conflicts.forEach(conflict => {
                conflict.element.classList.add('has-conflict');

                const warning = document.createElement('div');
                warning.className = 'conflict-warning';
                warning.innerHTML = `<i class="icon-warning"></i> ${conflict.message}`;
                conflict.element.appendChild(warning);
            });
        }

        // Get match ID from match item element
        function getMatchIdFromItem(matchItem) {
            const dateInput = matchItem.querySelector('input[type="date"]');
            if (dateInput && dateInput.name) {
                const match = dateInput.name.match(/match_schedules\[([^\]]+)\]/);
                return match ? match[1] : null;
            }
            return null;
        }

        // Clear all schedules
        function clearAllSchedules() {
            if (confirm('Are you sure you want to clear all match schedules?')) {
                document.querySelectorAll('#matchSchedulingForm input[type="date"]').forEach(input => {
                    input.value = '';
                });
                document.querySelectorAll('#matchSchedulingForm select').forEach(select => {
                    select.selectedIndex = 0;
                });

                // Update status indicators
                document.querySelectorAll('.schedule-status').forEach(status => {
                    status.className = 'schedule-status status-unscheduled';
                    status.textContent = 'Unscheduled';
                });

                showNotification('All schedules cleared.', 'info');
            }
        }

        // Validate schedules
        function validateSchedules() {
            const conflicts = checkForConflicts();
            const unscheduledMatches = [];

            // Check for unscheduled matches
            document.querySelectorAll('.match-item').forEach(matchItem => {
                const date = matchItem.querySelector('input[type="date"]').value;
                const time = matchItem.querySelector('select[name*="[time]"]').value;
                const venue = matchItem.querySelector('select[name*="[venue_id]"]').value;

                if (!date || !time || !venue) {
                    const matchTitle = matchItem.querySelector('.match-title').textContent.trim();
                    unscheduledMatches.push(matchTitle);
                }
            });

            // Show validation results
            let message = '';
            let type = 'success';

            if (conflicts.length > 0) {
                message = `Found ${conflicts.length} scheduling conflict(s).`;
                type = 'error';
            } else if (unscheduledMatches.length > 0) {
                message = `${unscheduledMatches.length} match(es) still need to be scheduled.`;
                type = 'warning';
            } else {
                message = 'All matches are properly scheduled with no conflicts!';
                type = 'success';
            }

            showNotification(message, type);
        }

        // Utility function to show notifications
        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `alert alert-${type}`;
            notification.style.position = 'fixed';
            notification.style.top = '20px';
            notification.style.right = '20px';
            notification.style.zIndex = '10000';
            notification.style.minWidth = '300px';
            notification.innerHTML = `
                <i class="icon-${type}"></i>
                ${message}
            `;

            document.body.appendChild(notification);

            // Auto remove after 4 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 4000);
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+S to save
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                const form = document.getElementById('matchSchedulingForm');
                if (form && validateAllSchedules()) {
                    form.submit();
                }
            }

            // Ctrl+Shift+V to validate
            if (e.ctrlKey && e.shiftKey && e.key === 'V') {
                e.preventDefault();
                validateSchedules();
            }
        });

        // Enhanced Notification System
        function showNotification(message, type = 'info', duration = 4000, persistent = false) {
            const existingNotifications = document.querySelectorAll(`.notification-${type}`);
            existingNotifications.forEach(notification => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            });

            const notification = document.createElement('div');
            notification.className = `alert alert-${type} notification-${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                min-width: 320px;
                max-width: 500px;
                padding: 16px 20px;
                border-radius: 8px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                animation: slideInRight 0.3s ease-out;
                font-size: 0.95rem;
                line-height: 1.4;
            `;

            const typeStyles = {
                success: 'background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white;',
                error: 'background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white;',
                warning: 'background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white;',
                info: 'background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%); color: white;'
            };

            notification.style.cssText += typeStyles[type] || typeStyles.info;

            const icons = {
                success: '✅',
                error: '❌',
                warning: '⚠️',
                info: 'ℹ️'
            };

            notification.innerHTML = `
                <div style="display: flex; align-items: flex-start; gap: 12px;">
                    <span style="font-size: 1.2rem; flex-shrink: 0;">${icons[type] || icons.info}</span>
                    <div style="flex: 1;">
                        <div style="font-weight: 600; margin-bottom: 4px;">${type.charAt(0).toUpperCase() + type.slice(1)}</div>
                        <div>${message}</div>
                    </div>
                    ${!persistent ? '<button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: inherit; font-size: 1.2rem; cursor: pointer; padding: 0; margin-left: 8px; opacity: 0.7;">×</button>' : ''}
                </div>
            `;

            document.body.appendChild(notification);

            if (!persistent) {
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.style.animation = 'slideOutRight 0.3s ease-in';
                        setTimeout(() => {
                            if (notification.parentNode) {
                                notification.parentNode.removeChild(notification);
                            }
                        }, 300);
                    }
                }, duration);
            }

            return notification;
        }

        function showValidationProgress(current, total, message = 'Processing...') {
            let progressContainer = document.querySelector('.validation-progress-container');

            if (!progressContainer) {
                progressContainer = document.createElement('div');
                progressContainer.className = 'validation-progress-container';
                progressContainer.style.cssText = `
                    position: fixed;
                    top: 80px;
                    right: 20px;
                    width: 300px;
                    background: white;
                    padding: 16px;
                    border-radius: 8px;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
                    z-index: 9999;
                `;
                progressContainer.innerHTML = `
                    <div style="font-weight: 600; margin-bottom: 8px;">Schedule Validation</div>
                    <div class="validation-progress">
                        <div class="validation-progress-bar" style="width: 0%;"></div>
                    </div>
                    <div class="validation-status" style="font-size: 0.85rem; margin-top: 8px; color: #6b7280;">
                        Starting validation...
                    </div>
                `;
                document.body.appendChild(progressContainer);
            }

            const progressBar = progressContainer.querySelector('.validation-progress-bar');
            const statusText = progressContainer.querySelector('.validation-status');

            const percentage = Math.round((current / total) * 100);
            progressBar.style.width = `${percentage}%`;
            statusText.textContent = message;

            if (current === total) {
                setTimeout(() => {
                    if (progressContainer.parentNode) {
                        progressContainer.parentNode.removeChild(progressContainer);
                    }
                }, 2000);
            }
        }

        function resetSubmitButton(button) {
            if (button) {
                button.classList.remove('loading');
                button.disabled = false;
            }
        }

        // Enhanced validation helper functions
        function validateScheduleField(field) {
            const value = field.value.trim();
            const fieldType = field.type || field.tagName.toLowerCase();
            let isValid = true;
            let errorMessage = '';

            // Remove existing error styling
            field.classList.remove('field-error', 'field-success');
            const existingError = field.parentNode.querySelector('.field-error-message');
            if (existingError) {
                existingError.remove();
            }

            // Validate based on field type
            if (fieldType === 'date') {
                if (value) {
                    const selectedDate = new Date(value);
                    const today = new Date();
                    today.setHours(0, 0, 0, 0);

                    if (selectedDate < today) {
                        isValid = false;
                        errorMessage = 'Date cannot be in the past';
                    }
                }
            } else if (fieldType === 'select-one' && field.name.includes('venue_id')) {
                if (!value) {
                    isValid = false;
                    errorMessage = 'Please select a venue';
                }
            } else if (fieldType === 'select-one' && field.name.includes('time')) {
                if (!value) {
                    isValid = false;
                    errorMessage = 'Please select a time slot';
                }
            }

            // Apply visual feedback
            if (value && isValid) {
                field.classList.add('field-success');
            } else if (!isValid) {
                field.classList.add('field-error');

                const errorElement = document.createElement('div');
                errorElement.className = 'field-error-message';
                errorElement.innerHTML = `<span>⚠️</span> ${errorMessage}`;
                field.parentNode.appendChild(errorElement);
            }

            return { isValid, errorMessage };
        }

        function validateScheduleCompleteness() {
            const incompleteMatches = [];

            document.querySelectorAll('.match-item').forEach(matchItem => {
                const matchTitle = matchItem.querySelector('.match-title').textContent.trim();
                const date = matchItem.querySelector('input[type="date"]').value;
                const time = matchItem.querySelector('select[name*="[time]"]').value;
                const venue = matchItem.querySelector('select[name*="[venue_id]"]').value;

                if (!date || !time || !venue) {
                    incompleteMatches.push({
                        title: matchTitle,
                        missing: [
                            !date ? 'date' : null,
                            !time ? 'time' : null,
                            !venue ? 'venue' : null
                        ].filter(Boolean)
                    });
                }
            });

            return incompleteMatches;
        }

        function validateVenueCapacity() {
            const capacityIssues = [];
            // This would check if venue capacity matches expected attendance
            // For now, just a placeholder
            return capacityIssues;
        }

        function showIncompleteMatchesError(incompleteMatches) {
            const matchList = incompleteMatches.map(match =>
                `• ${match.title} (missing: ${match.missing.join(', ')})`
            ).join('<br>');

            showNotification(
                `The following matches are incomplete:<br><br>${matchList}`,
                'error',
                8000
            );
        }

        function showConflictsError(conflicts) {
            const conflictList = conflicts.map(conflict =>
                `• ${conflict.venue} at ${conflict.time} on ${conflict.date}`
            ).join('<br>');

            showNotification(
                `Scheduling conflicts detected:<br><br>${conflictList}`,
                'error',
                8000
            );
        }

        function showCapacityError(capacityIssues) {
            const issueList = capacityIssues.map(issue =>
                `• ${issue.venue}: ${issue.message}`
            ).join('<br>');

            showNotification(
                `Venue capacity issues:<br><br>${issueList}`,
                'warning',
                6000
            );
        }

        // Smart scheduling suggestions
        function suggestOptimalTime(matchElement) {
            // This could implement AI-powered scheduling suggestions
            // For now, just a placeholder
            console.log('Suggesting optimal time for match:', matchElement);
        }

        // Export schedule functionality
        function exportSchedule() {
            // This would implement schedule export
            showNotification('Schedule export feature coming soon!', 'info');
        }
    </script>

    <style>
        /* Additional styles for scheduling interface */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--gray-500);
        }

        .empty-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .time-slots-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }

        .time-slot-item {
            background: var(--gray-50);
            padding: 10px;
            border-radius: var(--border-radius);
            text-align: center;
            border: 1px solid var(--gray-200);
        }

        .time-label {
            display: block;
            font-weight: 600;
            color: var(--gray-900);
        }

        .time-value {
            display: block;
            font-size: 0.85rem;
            color: var(--gray-600);
        }

        .venues-list {
            max-height: 200px;
            overflow-y: auto;
        }

        .venue-item {
            padding: 10px;
            border-bottom: 1px solid var(--gray-200);
        }

        .venue-item:last-child {
            border-bottom: none;
        }

        .venue-name {
            font-weight: 600;
            color: var(--gray-900);
        }

        .venue-details {
            font-size: 0.85rem;
            color: var(--gray-600);
        }

        .match-item.has-conflict {
            border-color: var(--error-color);
            background: rgba(239, 68, 68, 0.05);
        }

        .conflict-warning {
            background: var(--error-color);
            color: white;
            padding: 8px 12px;
            border-radius: var(--border-radius);
            margin-top: 10px;
            font-size: 0.85rem;
        }

        .match-details {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid var(--gray-200);
        }

        /* Icon styles */
        .icon-magic::before { content: "🪄"; }
        .icon-publish::before { content: "📢"; }
        .icon-save::before { content: "💾"; }
        .icon-clear::before { content: "🗑️"; }
        .icon-warning::before { content: "⚠️"; }
    </style>
</body>
</html>
