<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Event Settings Management
 *
 * @version 1.0
 * <AUTHOR> Development Team
 */

declare(strict_types=1);

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication
requireAuth();

// Only super admin can access this
if (getCurrentUser()['role'] !== 'super_admin') {
    header('Location: ../dashboard.php?message=Access denied&type=error');
    exit;
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] === 'update_event') {
            updateEventSettings($_POST);
            $message = 'Event settings updated successfully';
            $messageType = 'success';
        } else {
            throw new Exception('Invalid action');
        }
    } catch (Exception $e) {
        $message = 'Error: ' . $e->getMessage();
        $messageType = 'error';
    }
}

/**
 * Helper Functions for Event Settings Management
 */

function getAllSettings(): array {
    $result = fetchAll("SELECT setting_key, setting_value, description FROM system_settings ORDER BY setting_key");
    $settings = [];
    foreach ($result as $row) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }
    return $settings;
}

function updateEventSettings(array $data): void {
    $validSettings = [
        'default_point_system' => 'Default point distribution for events',
        'allow_score_updates' => 'Allow score updates after finalization (1=yes, 0=no)',
        'score_update_interval' => 'Live score update interval in seconds',
        'auto_advance_rounds' => 'Automatically advance tournament rounds (1=yes, 0=no)',
        'require_score_approval' => 'Require approval for score entries (1=yes, 0=no)'
    ];

    foreach ($validSettings as $key => $description) {
        if (isset($data[$key])) {
            $value = sanitizeInput($data[$key]);
            setSetting($key, $value, $description);
        }
    }
}

// Get current settings
$settings = getAllSettings();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Settings - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>

    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1>Event Settings</h1>
                <nav class="breadcrumb">
                    <a href="../dashboard.php">Dashboard</a>
                    <span>/</span>
                    <span>Settings</span>
                </nav>
            </div>
        </div>

        <!-- Settings Tab Navigation -->
        <div class="profile-tabs-container">
            <nav class="profile-tabs">
                <a href="index.php" class="profile-tab">
                    <i class="icon-settings"></i>
                    General
                </a>
                <a href="security.php" class="profile-tab">
                    <i class="icon-shield"></i>
                    Security
                </a>
                <a href="events.php" class="profile-tab active">
                    <i class="icon-trophy"></i>
                    Events
                </a>
                <a href="notifications.php" class="profile-tab">
                    <i class="icon-mail"></i>
                    Notifications
                </a>
                <a href="system.php" class="profile-tab">
                    <i class="icon-info"></i>
                    System Info
                </a>
            </nav>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?>">
                <i class="icon-<?php echo $messageType === 'success' ? 'check' : 'warning'; ?>"></i>
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <div class="settings-container">
            <!-- Event Settings Panel -->
            <div class="settings-content">
                <div class="settings-panel active" id="event-panel">
                    <div class="panel-header">
                        <h2>Event Settings</h2>
                        <p>Configure default event management and scoring settings</p>
                    </div>

                    <form method="POST" class="settings-form">
                        <input type="hidden" name="action" value="update_event">

                        <div class="form-grid">
                            <div class="form-group">
                                <label for="default_point_system">Default Point System</label>
                                <textarea id="default_point_system" name="default_point_system" rows="4"
                                          placeholder='{"1st": 15, "2nd": 12, "3rd": 10, "4th": 8, "5th": 6, "participation": 3}'><?php echo htmlspecialchars($settings['default_point_system'] ?? ''); ?></textarea>
                                <small>JSON format for default point distribution in events</small>
                            </div>

                            <div class="form-group">
                                <label for="score_update_interval">Score Update Interval (seconds)</label>
                                <input type="number" id="score_update_interval" name="score_update_interval"
                                       value="<?php echo (int)($settings['score_update_interval'] ?? 30); ?>"
                                       min="5" max="300" required>
                                <small>How often live scores refresh automatically</small>
                            </div>

                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="allow_score_updates" value="1"
                                           <?php echo ($settings['allow_score_updates'] ?? '1') === '1' ? 'checked' : ''; ?>>
                                    <span class="checkmark"></span>
                                    Allow Score Updates After Finalization
                                </label>
                                <small>Permit score modifications after matches are marked complete</small>
                            </div>

                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="auto_advance_rounds" value="1"
                                           <?php echo ($settings['auto_advance_rounds'] ?? '0') === '1' ? 'checked' : ''; ?>>
                                    <span class="checkmark"></span>
                                    Auto-Advance Tournament Rounds
                                </label>
                                <small>Automatically advance winners to next tournament rounds</small>
                            </div>

                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="require_score_approval" value="1"
                                           <?php echo ($settings['require_score_approval'] ?? '0') === '1' ? 'checked' : ''; ?>>
                                    <span class="checkmark"></span>
                                    Require Score Approval
                                </label>
                                <small>Require administrator approval before scores are finalized</small>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="icon-trophy"></i>
                                Save Event Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>

    <script src="../../assets/js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Settings tab navigation enhancement
            const profileTabs = document.querySelectorAll('.profile-tab');
            profileTabs.forEach(tab => {
                tab.addEventListener('click', function(e) {
                    // Add loading state for better UX
                    if (!this.classList.contains('active')) {
                        this.style.opacity = '0.7';
                        this.innerHTML += ' <i class="icon-spinner" style="animation: spin 1s linear infinite;"></i>';
                    }
                });
            });
            
            // Add CSS for spinner animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>
