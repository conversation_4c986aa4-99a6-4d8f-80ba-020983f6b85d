<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Forgot Password Page
 * Professional-grade password reset functionality
 * 
 * @version 2.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// Redirect if already logged in
if (isLoggedIn()) {
    redirect('dashboard.php');
}

$message = '';
$messageType = '';
$step = $_GET['step'] ?? 'request';
$token = $_GET['token'] ?? '';

// Create password_reset_tokens table if it doesn't exist
try {
    $checkTable = "SHOW TABLES LIKE 'password_reset_tokens'";
    $tableExists = fetchOne($checkTable);
    
    if (!$tableExists) {
        $createTable = "
            CREATE TABLE password_reset_tokens (
                id INT AUTO_INCREMENT PRIMARY KEY,
                admin_id INT NOT NULL,
                token VARCHAR(64) NOT NULL UNIQUE,
                expires_at TIMESTAMP NOT NULL,
                used TINYINT(1) DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (admin_id) REFERENCES admin_users(admin_id) ON DELETE CASCADE,
                INDEX idx_token (token),
                INDEX idx_expires (expires_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        executeQuery($createTable);
    }
} catch (Exception $e) {
    error_log("Failed to create password_reset_tokens table: " . $e->getMessage());
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Verify CSRF token
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('Invalid security token');
        }
        
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'request_reset':
                $email = sanitizeInput($_POST['email'] ?? '');
                
                if (empty($email)) {
                    throw new Exception('Please enter your email address');
                }
                
                if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    throw new Exception('Please enter a valid email address');
                }
                
                // Generate reset token
                try {
                    $token = generatePasswordResetToken($email);
                    
                    // In a production environment, send email here
                    // For demo purposes, we'll show the reset link
                    $resetLink = APP_URL . "/admin/forgot-password.php?step=reset&token=" . $token;
                    
                    $message = "Password reset instructions have been sent to your email address. For demo purposes, use this link: <a href='{$resetLink}' style='color: #2563eb; text-decoration: underline;'>Reset Password</a>";
                    $messageType = 'success';
                    
                } catch (Exception $e) {
                    // Don't reveal if email exists or not for security
                    $message = "If an account with that email exists, password reset instructions have been sent.";
                    $messageType = 'info';
                }
                
                break;
                
            case 'reset_password':
                $token = sanitizeInput($_POST['token'] ?? '');
                $newPassword = $_POST['new_password'] ?? '';
                $confirmPassword = $_POST['confirm_password'] ?? '';
                
                if (empty($token) || empty($newPassword) || empty($confirmPassword)) {
                    throw new Exception('All fields are required');
                }
                
                if ($newPassword !== $confirmPassword) {
                    throw new Exception('Passwords do not match');
                }
                
                if (strlen($newPassword) < 8) {
                    throw new Exception('Password must be at least 8 characters long');
                }
                
                // Reset password
                resetPasswordWithToken($token, $newPassword);
                
                $message = 'Password has been reset successfully. You can now log in with your new password.';
                $messageType = 'success';
                $step = 'complete';
                
                break;
        }
        
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Verify token for reset step
if ($step === 'reset' && !empty($token)) {
    try {
        $tokenData = verifyPasswordResetToken($token);
        if (!$tokenData) {
            $message = 'Invalid or expired reset token. Please request a new password reset.';
            $messageType = 'error';
            $step = 'request';
        }
    } catch (Exception $e) {
        $message = 'Invalid or expired reset token. Please request a new password reset.';
        $messageType = 'error';
        $step = 'request';
    }
}

// Generate CSRF token
$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Password - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
    <style>
        /* Enhanced styles for forgot password page */
        .forgot-password-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            padding: 2rem;
        }
        
        .forgot-password-card {
            background: var(--white);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            width: 100%;
            max-width: 500px;
            animation: slideInUp 0.3s ease-out;
        }
        
        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .forgot-password-header {
            text-align: center;
            padding: 2rem 2rem 1rem;
            background: var(--gray-50);
            border-bottom: 1px solid var(--gray-200);
        }
        
        .forgot-password-header .logo {
            width: 60px;
            height: 60px;
            margin-bottom: 1rem;
        }
        
        .forgot-password-header h1 {
            font-size: var(--font-size-2xl);
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 0.5rem;
        }
        
        .forgot-password-header p {
            color: var(--gray-600);
            font-size: var(--font-size-sm);
        }
        
        .forgot-password-content {
            padding: 2rem;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        
        .step {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            font-size: var(--font-size-sm);
            font-weight: 600;
        }
        
        .step.active {
            background: var(--primary-color);
            color: var(--white);
        }
        
        .step.inactive {
            background: var(--gray-100);
            color: var(--gray-500);
        }
        
        .back-to-login {
            text-align: center;
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid var(--gray-200);
        }
        
        .back-to-login a {
            color: var(--primary-color);
            text-decoration: none;
            font-size: var(--font-size-sm);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .back-to-login a:hover {
            text-decoration: underline;
        }
        
        .password-requirements {
            background: var(--gray-50);
            border: 1px solid var(--gray-200);
            border-radius: var(--border-radius);
            padding: 1rem;
            margin-top: 1rem;
            font-size: var(--font-size-sm);
        }
        
        .password-requirements h4 {
            margin: 0 0 0.5rem 0;
            color: var(--gray-700);
        }
        
        .password-requirements ul {
            margin: 0;
            padding-left: 1.5rem;
            color: var(--gray-600);
        }
        
        .success-message {
            text-align: center;
            padding: 2rem;
        }
        
        .success-icon {
            font-size: 3rem;
            color: var(--success-color);
            margin-bottom: 1rem;
        }
        
        /* Responsive design */
        @media (max-width: 480px) {
            .forgot-password-container {
                padding: 1rem;
            }
            
            .forgot-password-content {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="forgot-password-container">
        <div class="forgot-password-card">
            <div class="forgot-password-header">
                <img src="../assets/images/logo.png" alt="SCIMS Logo" class="logo" onerror="this.style.display='none'">
                <h1>
                    <?php if ($step === 'request'): ?>
                        Forgot Password
                    <?php elseif ($step === 'reset'): ?>
                        Reset Password
                    <?php else: ?>
                        Password Reset Complete
                    <?php endif; ?>
                </h1>
                <p>
                    <?php if ($step === 'request'): ?>
                        Enter your email address to receive password reset instructions
                    <?php elseif ($step === 'reset'): ?>
                        Enter your new password below
                    <?php else: ?>
                        Your password has been successfully reset
                    <?php endif; ?>
                </p>
            </div>
            
            <div class="forgot-password-content">
                <?php if ($step !== 'complete'): ?>
                    <div class="step-indicator">
                        <div class="step <?php echo $step === 'request' ? 'active' : 'inactive'; ?>">
                            <span>1</span> Request Reset
                        </div>
                        <div class="step <?php echo $step === 'reset' ? 'active' : 'inactive'; ?>">
                            <span>2</span> Reset Password
                        </div>
                    </div>
                <?php endif; ?>
                
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?>">
                        <i class="icon-<?php echo $messageType; ?>"></i>
                        <?php echo $message; ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($step === 'request'): ?>
                    <form method="POST" action="" class="forgot-password-form" id="requestForm">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                        <input type="hidden" name="action" value="request_reset">
                        
                        <div class="form-group">
                            <label for="email">Email Address</label>
                            <div class="input-group">
                                <i class="icon-email"></i>
                                <input 
                                    type="email" 
                                    id="email" 
                                    name="email" 
                                    required 
                                    autocomplete="email"
                                    placeholder="Enter your email address"
                                    data-field-name="Email"
                                >
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-login">
                            <i class="icon-send"></i>
                            Send Reset Instructions
                        </button>
                    </form>
                    
                <?php elseif ($step === 'reset'): ?>
                    <form method="POST" action="" class="forgot-password-form" id="resetForm">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                        <input type="hidden" name="action" value="reset_password">
                        <input type="hidden" name="token" value="<?php echo htmlspecialchars($token); ?>">
                        
                        <div class="form-group">
                            <label for="new_password">New Password</label>
                            <div class="input-group">
                                <i class="icon-lock"></i>
                                <input 
                                    type="password" 
                                    id="new_password" 
                                    name="new_password" 
                                    required 
                                    autocomplete="new-password"
                                    placeholder="Enter new password"
                                    data-field-name="New Password"
                                >
                                <button type="button" class="password-toggle" onclick="togglePassword('new_password')">
                                    <i class="icon-eye" id="newPasswordToggleIcon"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="confirm_password">Confirm Password</label>
                            <div class="input-group">
                                <i class="icon-lock"></i>
                                <input 
                                    type="password" 
                                    id="confirm_password" 
                                    name="confirm_password" 
                                    required 
                                    autocomplete="new-password"
                                    placeholder="Confirm new password"
                                    data-field-name="Confirm Password"
                                >
                                <button type="button" class="password-toggle" onclick="togglePassword('confirm_password')">
                                    <i class="icon-eye" id="confirmPasswordToggleIcon"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="password-requirements">
                            <h4>Password Requirements:</h4>
                            <ul>
                                <li>At least 8 characters long</li>
                                <li>Contains uppercase and lowercase letters</li>
                                <li>Contains at least one number</li>
                                <li>Contains at least one special character</li>
                            </ul>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-login">
                            <i class="icon-check"></i>
                            Reset Password
                        </button>
                    </form>
                    
                <?php else: ?>
                    <div class="success-message">
                        <div class="success-icon">✅</div>
                        <h3>Password Reset Successful!</h3>
                        <p>Your password has been successfully reset. You can now log in with your new password.</p>
                        <a href="index.php" class="btn btn-primary">
                            <i class="icon-login"></i>
                            Go to Login
                        </a>
                    </div>
                <?php endif; ?>
                
                <div class="back-to-login">
                    <a href="index.php">
                        <i class="icon-arrow-left"></i>
                        Back to Login
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Enhanced form validation and UX
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-focus first input
            const firstInput = document.querySelector('input[type="email"], input[type="password"]');
            if (firstInput) {
                firstInput.focus();
            }

            // Enhanced form validation
            const forms = document.querySelectorAll('.forgot-password-form');
            forms.forEach(form => {
                form.addEventListener('submit', handleFormSubmit);

                // Real-time validation
                const inputs = form.querySelectorAll('input[required]');
                inputs.forEach(input => {
                    input.addEventListener('blur', validateField);
                    input.addEventListener('input', clearFieldError);
                });
            });

            // Password confirmation validation
            const confirmPassword = document.getElementById('confirm_password');
            if (confirmPassword) {
                confirmPassword.addEventListener('input', validatePasswordMatch);
            }

            // Password strength indicator
            const newPassword = document.getElementById('new_password');
            if (newPassword) {
                newPassword.addEventListener('input', checkPasswordStrength);
            }
        });

        function handleFormSubmit(e) {
            const form = e.target;
            const submitBtn = form.querySelector('button[type="submit"]');

            // Validate all required fields
            const inputs = form.querySelectorAll('input[required]');
            let isValid = true;

            inputs.forEach(input => {
                if (!validateField({ target: input })) {
                    isValid = false;
                }
            });

            if (!isValid) {
                e.preventDefault();
                return false;
            }

            // Show loading state
            if (submitBtn) {
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="icon-spinner"></i> Processing...';
                submitBtn.disabled = true;

                // Re-enable after 5 seconds as fallback
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 5000);
            }

            return true;
        }

        function validateField(e) {
            const input = e.target;
            const value = input.value.trim();
            const fieldName = input.dataset.fieldName || input.name;

            // Clear previous errors
            clearFieldError(e);

            // Required field validation
            if (input.hasAttribute('required') && !value) {
                showFieldError(input, `${fieldName} is required`);
                return false;
            }

            // Email validation
            if (input.type === 'email' && value) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                    showFieldError(input, 'Please enter a valid email address');
                    return false;
                }
            }

            // Password validation
            if (input.type === 'password' && input.name === 'new_password' && value) {
                if (value.length < 8) {
                    showFieldError(input, 'Password must be at least 8 characters long');
                    return false;
                }
            }

            return true;
        }

        function validatePasswordMatch() {
            const newPassword = document.getElementById('new_password');
            const confirmPassword = document.getElementById('confirm_password');

            if (newPassword && confirmPassword && confirmPassword.value) {
                if (newPassword.value !== confirmPassword.value) {
                    showFieldError(confirmPassword, 'Passwords do not match');
                    return false;
                } else {
                    clearFieldError({ target: confirmPassword });
                    return true;
                }
            }

            return true;
        }

        function checkPasswordStrength(e) {
            const password = e.target.value;
            const requirements = document.querySelector('.password-requirements ul');

            if (!requirements) return;

            const checks = [
                { regex: /.{8,}/, text: 'At least 8 characters long' },
                { regex: /[a-z]/, text: 'Contains lowercase letters' },
                { regex: /[A-Z]/, text: 'Contains uppercase letters' },
                { regex: /\d/, text: 'Contains at least one number' },
                { regex: /[!@#$%^&*(),.?":{}|<>]/, text: 'Contains at least one special character' }
            ];

            requirements.innerHTML = '';
            checks.forEach(check => {
                const li = document.createElement('li');
                li.textContent = check.text;
                li.style.color = check.regex.test(password) ? 'var(--success-color)' : 'var(--gray-600)';
                if (check.regex.test(password)) {
                    li.innerHTML = '✓ ' + check.text;
                }
                requirements.appendChild(li);
            });
        }

        function showFieldError(input, message) {
            const inputGroup = input.closest('.input-group') || input.closest('.form-group');

            // Remove existing error
            const existingError = inputGroup.querySelector('.field-error');
            if (existingError) {
                existingError.remove();
            }

            // Add error class
            input.classList.add('error');

            // Create error message
            const errorDiv = document.createElement('div');
            errorDiv.className = 'field-error';
            errorDiv.textContent = message;
            inputGroup.appendChild(errorDiv);
        }

        function clearFieldError(e) {
            const input = e.target;
            const inputGroup = input.closest('.input-group') || input.closest('.form-group');

            // Remove error class
            input.classList.remove('error');

            // Remove error message
            const errorDiv = inputGroup.querySelector('.field-error');
            if (errorDiv) {
                errorDiv.remove();
            }
        }

        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const icon = document.getElementById(inputId + 'ToggleIcon');

            if (input.type === 'password') {
                input.type = 'text';
                icon.className = 'icon-eye-off';
            } else {
                input.type = 'password';
                icon.className = 'icon-eye';
            }
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Escape key to go back to login
            if (e.key === 'Escape') {
                window.location.href = 'index.php';
            }
        });

        // Auto-dismiss alerts after 10 seconds
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                if (!alert.classList.contains('alert-error')) {
                    alert.style.opacity = '0';
                    setTimeout(() => alert.remove(), 300);
                }
            });
        }, 10000);
    </script>
</body>
</html>
