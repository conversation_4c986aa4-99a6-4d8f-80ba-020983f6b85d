<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Score Recording Interface
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication
requireAuth();

$matchId = (int)($_GET['match_id'] ?? 0);
$message = '';
$messageType = 'info';

// Get available matches if no specific match selected
if (!$matchId) {
    $availableMatches = fetchAll("
        SELECT m.match_id, m.match_date, m.match_time, m.status,
               s.name as sport_name, s.scoring_type,
               e.name as event_name,
               v.name as venue_name,
               (SELECT COUNT(*) FROM match_participants WHERE match_id = m.match_id) as participant_count
        FROM matches m
        JOIN sports s ON m.sport_id = s.sport_id
        JOIN events e ON m.event_id = e.event_id
        LEFT JOIN venues v ON m.venue_id = v.venue_id
        WHERE m.status IN ('scheduled', 'ongoing')
        ORDER BY m.match_date ASC, m.match_time ASC
        LIMIT 20
    ");
} else {
    // Get match details
    $match = fetchOne("
        SELECT m.*, s.name as sport_name, s.scoring_type, s.category,
               e.name as event_name,
               v.name as venue_name
        FROM matches m
        JOIN sports s ON m.sport_id = s.sport_id
        JOIN events e ON m.event_id = e.event_id
        LEFT JOIN venues v ON m.venue_id = v.venue_id
        WHERE m.match_id = ?
    ", [$matchId]);
    
    if (!$match) {
        header('Location: index.php?message=Match not found&type=error');
        exit;
    }
    
    // Get participants
    $participants = fetchAll("
        SELECT mp.*, d.name as dept_name, d.abbreviation as dept_abbr
        FROM match_participants mp
        JOIN departments d ON mp.dept_id = d.dept_id
        WHERE mp.match_id = ?
        ORDER BY mp.participant_name
    ", [$matchId]);
    
    // Get existing scores
    $existingScores = fetchAll("
        SELECT * FROM scores WHERE match_id = ? AND is_final = 1
    ", [$matchId]);
    
    $scoresByParticipant = [];
    foreach ($existingScores as $score) {
        $scoresByParticipant[$score['participant_id']] = $score;
    }
}

// Handle score submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $matchId) {
    try {
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('Invalid security token');
        }
        
        $scores = $_POST['scores'] ?? [];
        
        if (empty($scores)) {
            throw new Exception('No scores provided');
        }
        
        // Begin transaction
        $pdo->beginTransaction();
        
        // Delete existing final scores for this match
        executeQuery("DELETE FROM scores WHERE match_id = ? AND is_final = 1", [$matchId]);
        
        foreach ($scores as $participantId => $scoreData) {
            $participantId = (int)$participantId;
            
            if (!$participantId) continue;
            
            $scoreRecord = [
                'match_id' => $matchId,
                'participant_id' => $participantId,
                'recorded_by' => getCurrentUser()['admin_id'],
                'is_final' => 1
            ];
            
            // Add score based on scoring type
            switch ($match['scoring_type']) {
                case 'points':
                    $scoreRecord['points'] = (float)($scoreData['points'] ?? 0);
                    $scoreRecord['position'] = (int)($scoreData['position'] ?? 0);
                    break;
                    
                case 'time':
                    $scoreRecord['time_score'] = sanitizeInput($scoreData['time'] ?? '');
                    $scoreRecord['position'] = (int)($scoreData['position'] ?? 0);
                    break;
                    
                case 'distance':
                    $scoreRecord['distance_score'] = (float)($scoreData['distance'] ?? 0);
                    $scoreRecord['position'] = (int)($scoreData['position'] ?? 0);
                    break;
                    
                case 'subjective':
                    $scoreRecord['points'] = (float)($scoreData['points'] ?? 0);
                    $scoreRecord['position'] = (int)($scoreData['position'] ?? 0);
                    break;
            }
            
            insertRecord('scores', $scoreRecord);
        }
        
        // Update match status to completed
        updateRecord('matches', ['status' => 'completed'], 'match_id = :match_id', ['match_id' => $matchId]);
        
        $pdo->commit();
        
        logActivity('scores_recorded', "Scores recorded for match ID {$matchId}");
        
        $message = 'Scores recorded successfully';
        $messageType = 'success';
        
        // Refresh data
        $existingScores = fetchAll("SELECT * FROM scores WHERE match_id = ? AND is_final = 1", [$matchId]);
        $scoresByParticipant = [];
        foreach ($existingScores as $score) {
            $scoresByParticipant[$score['participant_id']] = $score;
        }
        
    } catch (Exception $e) {
        $pdo->rollBack();
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Record Scores - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>
    
    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1>Record Scores</h1>
                <nav class="breadcrumb">
                    <a href="../dashboard.php">Dashboard</a>
                    <span>/</span>
                    <a href="index.php">Scores</a>
                    <span>/</span>
                    <span>Record</span>
                </nav>
            </div>
            <div class="page-actions">
                <a href="index.php" class="btn btn-outline">
                    <i class="icon-arrow-left"></i>
                    Back to Scores
                </a>
            </div>
        </div>
        
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <?php if (!$matchId): ?>
            <!-- Match Selection -->
            <div class="match-selection">
                <h2>Select Match to Record Scores</h2>
                
                <?php if (empty($availableMatches)): ?>
                    <div class="no-data">
                        <h3>No matches available for scoring</h3>
                        <p>All matches have been completed or no matches are scheduled.</p>
                        <a href="../matches/" class="btn btn-primary">Schedule Matches</a>
                    </div>
                <?php else: ?>
                    <div class="matches-grid">
                        <?php foreach ($availableMatches as $availableMatch): ?>
                            <div class="match-card">
                                <div class="match-header">
                                    <h3><?php echo htmlspecialchars($availableMatch['sport_name']); ?></h3>
                                    <span class="status-badge status-<?php echo $availableMatch['status']; ?>">
                                        <?php echo ucfirst($availableMatch['status']); ?>
                                    </span>
                                </div>
                                <div class="match-details">
                                    <p><strong><?php echo htmlspecialchars($availableMatch['event_name']); ?></strong></p>
                                    <p><?php echo formatDate($availableMatch['match_date']); ?> at <?php echo formatTime($availableMatch['match_time']); ?></p>
                                    <?php if ($availableMatch['venue_name']): ?>
                                        <p><i class="icon-location"></i> <?php echo htmlspecialchars($availableMatch['venue_name']); ?></p>
                                    <?php endif; ?>
                                    <p><i class="icon-users"></i> <?php echo $availableMatch['participant_count']; ?> participants</p>
                                </div>
                                <div class="match-actions">
                                    <a href="?match_id=<?php echo $availableMatch['match_id']; ?>" class="btn btn-primary">
                                        Record Scores
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
            
        <?php else: ?>
            <!-- Score Recording Form -->
            <div class="score-recording">
                <!-- Match Info -->
                <div class="match-info-card">
                    <h2><?php echo htmlspecialchars($match['sport_name']); ?> - <?php echo htmlspecialchars($match['event_name']); ?></h2>
                    <div class="match-meta">
                        <span><?php echo formatDate($match['match_date']); ?> at <?php echo formatTime($match['match_time']); ?></span>
                        <?php if ($match['venue_name']): ?>
                            <span> | <?php echo htmlspecialchars($match['venue_name']); ?></span>
                        <?php endif; ?>
                        <span> | Scoring: <?php echo ucfirst($match['scoring_type']); ?></span>
                    </div>
                </div>
                
                <?php if (empty($participants)): ?>
                    <div class="no-data">
                        <h3>No participants registered</h3>
                        <p>This match has no registered participants.</p>
                        <a href="../matches/edit.php?id=<?php echo $matchId; ?>" class="btn btn-primary">Add Participants</a>
                    </div>
                <?php else: ?>
                    <form method="POST" class="score-form">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                        
                        <div class="participants-scoring">
                            <h3>Record Scores for Participants</h3>
                            
                            <div class="scoring-instructions">
                                <p><strong>Scoring Type:</strong> <?php echo ucfirst($match['scoring_type']); ?></p>
                                <?php if ($match['scoring_type'] === 'time'): ?>
                                    <p><em>Enter time in format: MM:SS.ms (e.g., 01:23.45)</em></p>
                                <?php elseif ($match['scoring_type'] === 'distance'): ?>
                                    <p><em>Enter distance in meters (e.g., 15.75)</em></p>
                                <?php elseif ($match['scoring_type'] === 'points'): ?>
                                    <p><em>Enter points scored (e.g., 85.5)</em></p>
                                <?php endif; ?>
                            </div>
                            
                            <div class="participants-table">
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>Participant</th>
                                            <th>Department</th>
                                            <?php if ($match['scoring_type'] === 'points' || $match['scoring_type'] === 'subjective'): ?>
                                                <th>Points</th>
                                            <?php elseif ($match['scoring_type'] === 'time'): ?>
                                                <th>Time</th>
                                            <?php elseif ($match['scoring_type'] === 'distance'): ?>
                                                <th>Distance (m)</th>
                                            <?php endif; ?>
                                            <th>Position</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($participants as $participant): ?>
                                            <?php $existingScore = $scoresByParticipant[$participant['participant_id']] ?? null; ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($participant['participant_name']); ?></strong>
                                                </td>
                                                <td>
                                                    <span class="dept-badge" style="background-color: var(--dept-color, #ccc);">
                                                        <?php echo htmlspecialchars($participant['dept_abbr']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($match['scoring_type'] === 'points' || $match['scoring_type'] === 'subjective'): ?>
                                                        <input type="number" 
                                                               name="scores[<?php echo $participant['participant_id']; ?>][points]" 
                                                               step="0.01" min="0" 
                                                               value="<?php echo $existingScore['points'] ?? ''; ?>"
                                                               class="form-control score-input">
                                                    <?php elseif ($match['scoring_type'] === 'time'): ?>
                                                        <input type="text" 
                                                               name="scores[<?php echo $participant['participant_id']; ?>][time]" 
                                                               placeholder="MM:SS.ms"
                                                               value="<?php echo $existingScore['time_score'] ?? ''; ?>"
                                                               class="form-control score-input">
                                                    <?php elseif ($match['scoring_type'] === 'distance'): ?>
                                                        <input type="number" 
                                                               name="scores[<?php echo $participant['participant_id']; ?>][distance]" 
                                                               step="0.01" min="0"
                                                               value="<?php echo $existingScore['distance_score'] ?? ''; ?>"
                                                               class="form-control score-input">
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <input type="number" 
                                                           name="scores[<?php echo $participant['participant_id']; ?>][position]" 
                                                           min="1" max="<?php echo count($participants); ?>"
                                                           value="<?php echo $existingScore['position'] ?? ''; ?>"
                                                           class="form-control position-input">
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="icon-check"></i>
                                Record Scores
                            </button>
                            <a href="index.php" class="btn btn-outline">Cancel</a>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </main>
    
    <script src="../../assets/js/admin.js"></script>
    <script>
        // Auto-calculate positions based on scores
        document.addEventListener('DOMContentLoaded', function() {
            const scoreInputs = document.querySelectorAll('.score-input');
            const positionInputs = document.querySelectorAll('.position-input');
            
            function autoCalculatePositions() {
                const scores = [];
                scoreInputs.forEach((input, index) => {
                    const value = parseFloat(input.value) || 0;
                    scores.push({ value, index, input });
                });
                
                // Sort by score (descending for points, ascending for time)
                const scoringType = '<?php echo $match['scoring_type'] ?? ''; ?>';
                if (scoringType === 'time') {
                    scores.sort((a, b) => a.value - b.value); // Ascending for time
                } else {
                    scores.sort((a, b) => b.value - a.value); // Descending for points/distance
                }
                
                scores.forEach((score, position) => {
                    if (score.value > 0) {
                        positionInputs[score.index].value = position + 1;
                    }
                });
            }
            
            scoreInputs.forEach(input => {
                input.addEventListener('blur', autoCalculatePositions);
            });
        });
    </script>
</body>
</html>
