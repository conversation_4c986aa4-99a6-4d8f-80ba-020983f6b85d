<?php
/**
 * Simplified Tournament Configuration Test Page
 * Testing core functionality without external dependencies
 */

// Simulate basic configuration
define('APP_NAME', 'SCIMS');

// Mock data for testing
$eventSportId = 1;
$eventId = 1;

$eventSport = [
    'sport_name' => 'Basketball',
    'sport_category' => 'team',
    'scoring_type' => 'points',
    'event_name' => 'Annual Sports Festival 2024'
];

$participants = [
    [
        'participant_id' => 1,
        'participant_name' => 'Team Alpha',
        'dept_id' => 1,
        'dept_name' => 'Computer Science',
        'dept_abbr' => 'CS'
    ],
    [
        'participant_id' => 2,
        'participant_name' => 'Team Beta',
        'dept_id' => 2,
        'dept_name' => 'Information Technology',
        'dept_abbr' => 'IT'
    ],
    [
        'participant_id' => 3,
        'participant_name' => 'Team Gamma',
        'dept_id' => 3,
        'dept_name' => 'Engineering',
        'dept_abbr' => 'ENG'
    ],
    [
        'participant_id' => 4,
        'participant_name' => 'Team Delta',
        'dept_id' => 4,
        'dept_name' => 'Business Administration',
        'dept_abbr' => 'BA'
    ]
];

// Tournament formats for team sports
$availableFormats = [
    'single_elimination' => [
        'name' => 'Single Elimination',
        'icon' => '🏆',
        'description' => 'Traditional knockout format. Lose once, you\'re out.',
        'details' => ['Fast completion', 'High stakes', 'Best for: 8-64 teams'],
        'suitable' => true
    ],
    'double_elimination' => [
        'name' => 'Double Elimination',
        'icon' => '🔄',
        'description' => 'Two chances - winners and losers brackets.',
        'details' => ['More forgiving', 'Longer tournament', 'Best for: 8-32 teams'],
        'suitable' => true
    ],
    'round_robin' => [
        'name' => 'Round Robin',
        'icon' => '🔁',
        'description' => 'Every team plays every other team.',
        'details' => ['Most fair', 'Many matches', 'Best for: 4-12 teams'],
        'suitable' => true
    ]
];

$tournamentConfig = null;
$message = '';
$messageType = '';
$csrfToken = 'test_token';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tournament Configuration Test - <?php echo htmlspecialchars($eventSport['sport_name']); ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .page-header {
            background: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .page-header h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .breadcrumb {
            color: #666;
            font-size: 0.9rem;
        }
        
        .breadcrumb span {
            margin: 0 8px;
        }
        
        .tournament-wizard {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .wizard-steps {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        
        .step {
            flex: 1;
            padding: 20px;
            text-align: center;
            border-right: 1px solid #dee2e6;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .step:last-child {
            border-right: none;
        }
        
        .step.active {
            background: #007cba;
            color: white;
        }
        
        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #dee2e6;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            font-weight: 600;
        }
        
        .step.active .step-number {
            background: white;
            color: #007cba;
        }
        
        .step-title {
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .wizard-content {
            padding: 40px;
        }
        
        .step-header h2 {
            color: #2c3e50;
            font-size: 2rem;
            margin-bottom: 15px;
        }
        
        .step-header p {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 30px;
        }
        
        .sport-category-info {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .category-badge, .scoring-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }
        
        .category-badge {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .scoring-badge {
            background: #f3e5f5;
            color: #7b1fa2;
        }
        
        .format-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .format-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .format-card:hover {
            border-color: #007cba;
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .format-card.selected {
            border-color: #007cba;
            background: #f8f9fa;
        }
        
        .format-icon {
            font-size: 3rem;
            text-align: center;
            margin-bottom: 15px;
        }
        
        .format-card h3 {
            color: #2c3e50;
            font-size: 1.3rem;
            margin-bottom: 10px;
            text-align: center;
        }
        
        .format-description {
            color: #666;
            text-align: center;
            margin-bottom: 15px;
        }
        
        .format-details {
            list-style: none;
            padding: 0;
        }
        
        .format-details li {
            color: #666;
            font-size: 0.9rem;
            margin: 5px 0;
            padding-left: 20px;
            position: relative;
        }
        
        .format-details li:before {
            content: "✓";
            color: #28a745;
            font-weight: 600;
            position: absolute;
            left: 0;
        }
        
        .format-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: #28a745;
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: #007cba;
            color: white;
        }
        
        .btn-primary:hover {
            background: #005a87;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .wizard-navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 30px 40px;
            background: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }
        
        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="page-header">
            <h1>🏆 Tournament Configuration</h1>
            <nav class="breadcrumb">
                Dashboard <span>/</span> Events <span>/</span> <?php echo htmlspecialchars($eventSport['event_name']); ?> <span>/</span> <?php echo htmlspecialchars($eventSport['sport_name']); ?> <span>/</span> Tournament Config
            </nav>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <div class="tournament-wizard">
            <div class="wizard-steps">
                <div class="step active" data-step="1">
                    <div class="step-number">1</div>
                    <div class="step-title">Format Selection</div>
                </div>
                <div class="step" data-step="2">
                    <div class="step-number">2</div>
                    <div class="step-title">Bracket Setup</div>
                </div>
                <div class="step" data-step="3">
                    <div class="step-number">3</div>
                    <div class="step-title">Scheduling</div>
                </div>
                <div class="step" data-step="4">
                    <div class="step-number">4</div>
                    <div class="step-title">Configuration</div>
                </div>
            </div>

            <div class="wizard-content" data-step="1">
                <div class="step-header">
                    <h2>Select Tournament Format</h2>
                    <p>Choose the tournament format that best suits your <strong><?php echo ucfirst(str_replace('_', ' ', $eventSport['sport_category'])); ?></strong> sport and number of participants.</p>
                    <div class="sport-category-info">
                        <span class="category-badge">
                            <?php echo ucfirst(str_replace('_', ' ', $eventSport['sport_category'])); ?> Sport
                        </span>
                        <span class="scoring-badge">
                            <?php echo ucfirst($eventSport['scoring_type']); ?> Scoring
                        </span>
                    </div>
                </div>

                <div class="format-selection">
                    <div class="format-grid">
                        <?php foreach ($availableFormats as $formatKey => $format): ?>
                            <div class="format-card" data-format="<?php echo $formatKey; ?>" onclick="selectFormat('<?php echo $formatKey; ?>')">
                                <div class="format-icon"><?php echo $format['icon']; ?></div>
                                <h3><?php echo htmlspecialchars($format['name']); ?></h3>
                                <p class="format-description"><?php echo htmlspecialchars($format['description']); ?></p>
                                <ul class="format-details">
                                    <?php foreach ($format['details'] as $detail): ?>
                                        <li><?php echo htmlspecialchars($detail); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                                <?php if ($format['suitable']): ?>
                                    <div class="format-badge">Recommended</div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <div class="wizard-navigation">
                <div>
                    <span style="color: #666;">Participants: <strong><?php echo count($participants); ?></strong></span>
                </div>
                <div>
                    <button type="button" class="btn btn-secondary" onclick="previousStep()" style="display: none;">← Previous</button>
                    <button type="button" class="btn btn-primary" onclick="nextStep()">Next: Bracket Setup →</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentStep = 1;
        let selectedFormat = null;

        function selectFormat(formatKey) {
            // Remove previous selection
            document.querySelectorAll('.format-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // Add selection to clicked card
            event.currentTarget.classList.add('selected');
            selectedFormat = formatKey;
            
            console.log('Selected format:', formatKey);
        }

        function nextStep() {
            if (currentStep === 1 && !selectedFormat) {
                alert('Please select a tournament format first.');
                return;
            }
            
            if (currentStep < 4) {
                currentStep++;
                updateStepDisplay();
            }
        }

        function previousStep() {
            if (currentStep > 1) {
                currentStep--;
                updateStepDisplay();
            }
        }

        function updateStepDisplay() {
            // Update step indicators
            document.querySelectorAll('.step').forEach((step, index) => {
                if (index + 1 === currentStep) {
                    step.classList.add('active');
                } else {
                    step.classList.remove('active');
                }
            });
            
            // Update navigation buttons
            const prevBtn = document.querySelector('.btn-secondary');
            const nextBtn = document.querySelector('.btn-primary');
            
            if (currentStep === 1) {
                prevBtn.style.display = 'none';
                nextBtn.textContent = 'Next: Bracket Setup →';
            } else if (currentStep === 4) {
                prevBtn.style.display = 'inline-block';
                nextBtn.textContent = 'Save Configuration';
            } else {
                prevBtn.style.display = 'inline-block';
                nextBtn.textContent = 'Next →';
            }
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Tournament Configuration Test Page Loaded');
            console.log('Available formats:', <?php echo json_encode(array_keys($availableFormats)); ?>);
            console.log('Participants:', <?php echo json_encode($participants); ?>);
        });
    </script>
</body>
</html>
