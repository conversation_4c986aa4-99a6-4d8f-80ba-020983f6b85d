<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Security Settings Management
 *
 * @version 1.0
 * <AUTHOR> Development Team
 */

declare(strict_types=1);

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication
requireAuth();

// Only super admin can access this
if (getCurrentUser()['role'] !== 'super_admin') {
    header('Location: ../dashboard.php?message=Access denied&type=error');
    exit;
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] === 'update_security') {
            updateSecuritySettings($_POST);
            $message = 'Security settings updated successfully';
            $messageType = 'success';
        } else {
            throw new Exception('Invalid action');
        }
    } catch (Exception $e) {
        $message = 'Error: ' . $e->getMessage();
        $messageType = 'error';
    }
}

/**
 * Helper Functions for Security Settings Management
 */

function getAllSettings(): array {
    $result = fetchAll("SELECT setting_key, setting_value, description FROM system_settings ORDER BY setting_key");
    $settings = [];
    foreach ($result as $row) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }
    return $settings;
}

function updateSecuritySettings(array $data): void {
    $validSettings = [
        'max_login_attempts' => 'Maximum failed login attempts before lockout',
        'lockout_duration' => 'Account lockout duration in minutes',
        'session_timeout' => 'Session timeout in seconds',
        'password_min_length' => 'Minimum password length requirement',
        'require_password_complexity' => 'Require complex passwords (1=yes, 0=no)',
        'enable_two_factor' => 'Enable two-factor authentication (1=yes, 0=no)'
    ];

    foreach ($validSettings as $key => $description) {
        if (isset($data[$key])) {
            $value = sanitizeInput($data[$key]);
            // Validate numeric settings
            if (in_array($key, ['max_login_attempts', 'lockout_duration', 'session_timeout', 'password_min_length'])) {
                $value = max(1, (int)$value);
            }
            setSetting($key, (string)$value, $description);
        }
    }
}

// Get current settings
$settings = getAllSettings();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Settings - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>

    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1>Security Settings</h1>
                <nav class="breadcrumb">
                    <a href="../dashboard.php">Dashboard</a>
                    <span>/</span>
                    <span>Settings</span>
                </nav>
            </div>
        </div>

        <!-- Settings Tab Navigation -->
        <div class="profile-tabs-container">
            <nav class="profile-tabs">
                <a href="index.php" class="profile-tab">
                    <i class="icon-settings"></i>
                    General
                </a>
                <a href="security.php" class="profile-tab active">
                    <i class="icon-shield"></i>
                    Security
                </a>
                <a href="events.php" class="profile-tab">
                    <i class="icon-trophy"></i>
                    Events
                </a>
                <a href="notifications.php" class="profile-tab">
                    <i class="icon-mail"></i>
                    Notifications
                </a>
                <a href="system.php" class="profile-tab">
                    <i class="icon-info"></i>
                    System Info
                </a>
            </nav>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?>">
                <i class="icon-<?php echo $messageType === 'success' ? 'check' : 'warning'; ?>"></i>
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <div class="settings-container">
            <!-- Security Settings Panel -->
            <div class="settings-content">
                <div class="settings-panel active" id="security-panel">
                    <div class="panel-header">
                        <h2>Security Settings</h2>
                        <p>Configure authentication and security policies</p>
                    </div>

                    <form method="POST" class="settings-form">
                        <input type="hidden" name="action" value="update_security">

                        <div class="form-grid">
                            <div class="form-group">
                                <label for="max_login_attempts">Max Login Attempts</label>
                                <input type="number" id="max_login_attempts" name="max_login_attempts"
                                       value="<?php echo (int)($settings['max_login_attempts'] ?? 5); ?>"
                                       min="1" max="20" required>
                                <small>Maximum failed login attempts before account lockout</small>
                            </div>

                            <div class="form-group">
                                <label for="lockout_duration">Lockout Duration (minutes)</label>
                                <input type="number" id="lockout_duration" name="lockout_duration"
                                       value="<?php echo (int)($settings['lockout_duration'] ?? 15); ?>"
                                       min="1" max="1440" required>
                                <small>How long accounts remain locked after failed attempts</small>
                            </div>

                            <div class="form-group">
                                <label for="session_timeout">Session Timeout (seconds)</label>
                                <input type="number" id="session_timeout" name="session_timeout"
                                       value="<?php echo (int)($settings['session_timeout'] ?? 1800); ?>"
                                       min="300" max="86400" required>
                                <small>Automatic logout time for inactive sessions</small>
                            </div>

                            <div class="form-group">
                                <label for="password_min_length">Minimum Password Length</label>
                                <input type="number" id="password_min_length" name="password_min_length"
                                       value="<?php echo (int)($settings['password_min_length'] ?? 8); ?>"
                                       min="6" max="50" required>
                                <small>Minimum required password length for new accounts</small>
                            </div>

                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="require_password_complexity" value="1"
                                           <?php echo ($settings['require_password_complexity'] ?? '0') === '1' ? 'checked' : ''; ?>>
                                    <span class="checkmark"></span>
                                    Require Password Complexity
                                </label>
                                <small>Require uppercase, lowercase, numbers, and special characters</small>
                            </div>

                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" name="enable_two_factor" value="1"
                                           <?php echo ($settings['enable_two_factor'] ?? '0') === '1' ? 'checked' : ''; ?>>
                                    <span class="checkmark"></span>
                                    Enable Two-Factor Authentication
                                </label>
                                <small>Require additional verification for admin accounts</small>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="icon-shield"></i>
                                Save Security Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>

    <script src="../../assets/js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Settings tab navigation enhancement
            const profileTabs = document.querySelectorAll('.profile-tab');
            profileTabs.forEach(tab => {
                tab.addEventListener('click', function(e) {
                    // Add loading state for better UX
                    if (!this.classList.contains('active')) {
                        this.style.opacity = '0.7';
                        this.innerHTML += ' <i class="icon-spinner" style="animation: spin 1s linear infinite;"></i>';
                    }
                });
            });
            
            // Add CSS for spinner animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>
