<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Match Creation Form
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication
requireAuth();

$errors = [];
$message = '';
$messageType = 'info';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('Invalid security token');
        }
        
        // Validate input
        $eventId = (int)($_POST['event_id'] ?? 0);
        $sportId = (int)($_POST['sport_id'] ?? 0);
        $venueId = (int)($_POST['venue_id'] ?? 0) ?: null;
        $matchDate = sanitizeInput($_POST['match_date'] ?? '');
        $matchTime = sanitizeInput($_POST['match_time'] ?? '');
        $roundType = sanitizeInput($_POST['round_type'] ?? 'preliminary');
        $matchNumber = sanitizeInput($_POST['match_number'] ?? '');
        $notes = sanitizeInput($_POST['notes'] ?? '');
        
        // Validation
        if (!$eventId) $errors[] = 'Event is required';
        if (!$sportId) $errors[] = 'Sport is required';
        if (!$matchDate) $errors[] = 'Match date is required';
        if (!$matchTime) $errors[] = 'Match time is required';
        
        // Check if venue is available at the specified time
        if ($venueId && $matchDate && $matchTime) {
            $conflictCheck = fetchOne("
                SELECT COUNT(*) as count 
                FROM matches 
                WHERE venue_id = ? AND match_date = ? AND match_time = ? AND status != 'cancelled'
            ", [$venueId, $matchDate, $matchTime]);
            
            if ($conflictCheck['count'] > 0) {
                $errors[] = 'Venue is already booked at this date and time';
            }
        }
        
        if (empty($errors)) {
            // Create match
            $matchData = [
                'event_id' => $eventId,
                'sport_id' => $sportId,
                'venue_id' => $venueId,
                'match_date' => $matchDate,
                'match_time' => $matchTime,
                'round_type' => $roundType,
                'match_number' => $matchNumber,
                'notes' => $notes,
                'status' => 'scheduled'
            ];
            
            $matchId = insertRecord('matches', $matchData);
            
            logActivity('match_created', "Match created with ID: {$matchId}");
            redirect('view.php?id=' . $matchId, 'Match scheduled successfully!', 'success');
        }
        
    } catch (Exception $e) {
        $errors[] = $e->getMessage();
    }
}

// Get form data
$events = fetchAll("SELECT event_id, name FROM events WHERE status IN ('upcoming', 'ongoing') ORDER BY name");
$sports = fetchAll("SELECT sport_id, name, category FROM sports WHERE status = 'active' ORDER BY name");
$venues = fetchAll("SELECT venue_id, name, location FROM venues WHERE status = 'available' ORDER BY name");

$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Schedule Match - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>
    
    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1>Schedule Match</h1>
                <nav class="breadcrumb">
                    <a href="../dashboard.php">Dashboard</a>
                    <span>/</span>
                    <a href="index.php">Matches</a>
                    <span>/</span>
                    <span>Schedule</span>
                </nav>
            </div>
        </div>
        
        <?php if (!empty($errors)): ?>
            <div class="alert alert-error">
                <ul>
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <div class="form-container">
            <form method="POST" class="admin-form">
                <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                
                <div class="form-section">
                    <h2>Match Details</h2>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="event_id">Event *</label>
                            <select name="event_id" id="event_id" class="form-control" required>
                                <option value="">Select Event</option>
                                <?php foreach ($events as $event): ?>
                                    <option value="<?php echo $event['event_id']; ?>" 
                                            <?php echo (($_POST['event_id'] ?? '') == $event['event_id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($event['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="sport_id">Sport *</label>
                            <select name="sport_id" id="sport_id" class="form-control" required>
                                <option value="">Select Sport</option>
                                <?php foreach ($sports as $sport): ?>
                                    <option value="<?php echo $sport['sport_id']; ?>" 
                                            data-category="<?php echo $sport['category']; ?>"
                                            <?php echo (($_POST['sport_id'] ?? '') == $sport['sport_id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($sport['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="match_date">Match Date *</label>
                            <input type="date" name="match_date" id="match_date" class="form-control" 
                                   value="<?php echo htmlspecialchars($_POST['match_date'] ?? ''); ?>" 
                                   min="<?php echo date('Y-m-d'); ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="match_time">Match Time *</label>
                            <input type="time" name="match_time" id="match_time" class="form-control" 
                                   value="<?php echo htmlspecialchars($_POST['match_time'] ?? ''); ?>" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="venue_id">Venue</label>
                            <select name="venue_id" id="venue_id" class="form-control">
                                <option value="">Select Venue (Optional)</option>
                                <?php foreach ($venues as $venue): ?>
                                    <option value="<?php echo $venue['venue_id']; ?>" 
                                            <?php echo (($_POST['venue_id'] ?? '') == $venue['venue_id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($venue['name']); ?>
                                        <?php if ($venue['location']): ?>
                                            - <?php echo htmlspecialchars($venue['location']); ?>
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="round_type">Round Type</label>
                            <select name="round_type" id="round_type" class="form-control">
                                <option value="preliminary" <?php echo (($_POST['round_type'] ?? 'preliminary') === 'preliminary') ? 'selected' : ''; ?>>Preliminary</option>
                                <option value="quarterfinal" <?php echo (($_POST['round_type'] ?? '') === 'quarterfinal') ? 'selected' : ''; ?>>Quarterfinal</option>
                                <option value="semifinal" <?php echo (($_POST['round_type'] ?? '') === 'semifinal') ? 'selected' : ''; ?>>Semifinal</option>
                                <option value="final" <?php echo (($_POST['round_type'] ?? '') === 'final') ? 'selected' : ''; ?>>Final</option>
                                <option value="round_robin" <?php echo (($_POST['round_type'] ?? '') === 'round_robin') ? 'selected' : ''; ?>>Round Robin</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="match_number">Match Number</label>
                            <input type="text" name="match_number" id="match_number" class="form-control" 
                                   value="<?php echo htmlspecialchars($_POST['match_number'] ?? ''); ?>" 
                                   placeholder="e.g., M001, Game 1">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="notes">Notes</label>
                        <textarea name="notes" id="notes" class="form-control" rows="3" 
                                  placeholder="Additional notes or instructions for this match"><?php echo htmlspecialchars($_POST['notes'] ?? ''); ?></textarea>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="icon-calendar"></i>
                        Schedule Match
                    </button>
                    <a href="index.php" class="btn btn-secondary">Cancel</a>
                </div>
            </form>
        </div>
        
        <!-- Venue Availability Check -->
        <div id="venueAvailability" class="venue-availability" style="display: none;">
            <h3>Venue Availability</h3>
            <div id="availabilityResults"></div>
        </div>
    </main>
    
    <script src="../../assets/js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const venueSelect = document.getElementById('venue_id');
            const dateInput = document.getElementById('match_date');
            const timeInput = document.getElementById('match_time');
            const availabilityDiv = document.getElementById('venueAvailability');
            const resultsDiv = document.getElementById('availabilityResults');
            
            // Check venue availability when venue, date, or time changes
            function checkVenueAvailability() {
                const venueId = venueSelect.value;
                const date = dateInput.value;
                const time = timeInput.value;
                
                if (venueId && date && time) {
                    fetch('ajax/check_venue_availability.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            venue_id: venueId,
                            date: date,
                            time: time
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.available) {
                            resultsDiv.innerHTML = '<p class="text-success">✓ Venue is available at this time</p>';
                        } else {
                            resultsDiv.innerHTML = '<p class="text-error">✗ Venue is already booked at this time</p>';
                        }
                        availabilityDiv.style.display = 'block';
                    })
                    .catch(error => {
                        console.error('Error checking availability:', error);
                    });
                } else {
                    availabilityDiv.style.display = 'none';
                }
            }
            
            venueSelect.addEventListener('change', checkVenueAvailability);
            dateInput.addEventListener('change', checkVenueAvailability);
            timeInput.addEventListener('change', checkVenueAvailability);
            
            // Auto-generate match number based on event and sport
            const eventSelect = document.getElementById('event_id');
            const sportSelect = document.getElementById('sport_id');
            const matchNumberInput = document.getElementById('match_number');
            
            function generateMatchNumber() {
                const eventId = eventSelect.value;
                const sportId = sportSelect.value;
                
                if (eventId && sportId && !matchNumberInput.value) {
                    fetch('ajax/generate_match_number.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            event_id: eventId,
                            sport_id: sportId
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.match_number) {
                            matchNumberInput.value = data.match_number;
                        }
                    })
                    .catch(error => {
                        console.error('Error generating match number:', error);
                    });
                }
            }
            
            eventSelect.addEventListener('change', generateMatchNumber);
            sportSelect.addEventListener('change', generateMatchNumber);
        });
    </script>
</body>
</html>
