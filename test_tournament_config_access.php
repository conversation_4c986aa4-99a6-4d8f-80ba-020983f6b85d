<?php
/**
 * Test Tournament Configuration Access
 * Diagnostic page to verify tournament configuration is working
 */

// Basic configuration
define('APP_NAME', 'SCIMS');

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Tournament Configuration Access Test - SCIMS</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 20px; background: #f8f9fa; }
        .container { max-width: 1000px; margin: 0 auto; }
        .test-header { background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); color: white; padding: 30px; border-radius: 12px; margin-bottom: 30px; }
        .test-section { background: white; padding: 25px; margin: 20px 0; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status-good { color: #059669; font-weight: 600; }
        .status-error { color: #dc2626; font-weight: 600; }
        .status-warning { color: #d97706; font-weight: 600; }
        .demo-link { display: inline-block; background: #2563eb; color: white; padding: 12px 24px; border-radius: 8px; text-decoration: none; margin: 10px 10px 10px 0; transition: all 0.2s; }
        .demo-link:hover { background: #1d4ed8; transform: translateY(-1px); }
        .code-block { background: #1e293b; color: #e2e8f0; padding: 15px; border-radius: 8px; overflow-x: auto; font-family: 'Courier New', monospace; font-size: 0.9rem; margin: 15px 0; }
        .diagnostic-item { padding: 15px; border-left: 4px solid #e5e7eb; margin: 10px 0; background: #f9fafb; }
        .diagnostic-item.success { border-color: #059669; background: #f0fdf4; }
        .diagnostic-item.error { border-color: #dc2626; background: #fef2f2; }
        .diagnostic-item.warning { border-color: #d97706; background: #fffbeb; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='test-header'>
            <h1>🔧 Tournament Configuration Access Test</h1>
            <p style='font-size: 1.1rem; opacity: 0.9; margin-bottom: 0;'>
                Diagnostic page to verify tournament configuration functionality
            </p>
        </div>";

// Test 1: File Access
echo "<div class='test-section'>
    <h2>📁 File Access Test</h2>
    <p>Testing if the tournament configuration file is accessible and properly structured:</p>";

$configFile = 'admin/events/tournament_config.php';
if (file_exists($configFile)) {
    $fileSize = filesize($configFile);
    $lastModified = date('Y-m-d H:i:s', filemtime($configFile));
    
    echo "<div class='diagnostic-item success'>
        <strong>✅ File Exists:</strong> {$configFile}<br>
        <strong>File Size:</strong> " . number_format($fileSize) . " bytes<br>
        <strong>Last Modified:</strong> {$lastModified}
    </div>";
    
    // Check for syntax errors
    $syntaxCheck = shell_exec("php -l {$configFile} 2>&1");
    if (strpos($syntaxCheck, 'No syntax errors') !== false) {
        echo "<div class='diagnostic-item success'>
            <strong>✅ Syntax Check:</strong> No syntax errors detected
        </div>";
    } else {
        echo "<div class='diagnostic-item error'>
            <strong>❌ Syntax Error:</strong> {$syntaxCheck}
        </div>";
    }
} else {
    echo "<div class='diagnostic-item error'>
        <strong>❌ File Missing:</strong> {$configFile} not found
    </div>";
}

echo "</div>";

// Test 2: Dependencies Check
echo "<div class='test-section'>
    <h2>🔗 Dependencies Check</h2>
    <p>Checking if required files and dependencies are available:</p>";

$dependencies = [
    '../../includes/config.php' => 'Core configuration file',
    '../../includes/functions.php' => 'Helper functions',
    '../../includes/auth.php' => 'Authentication system',
    '../includes/header.php' => 'Admin header',
    '../includes/sidebar.php' => 'Admin sidebar',
    '../../assets/css/admin.css' => 'Admin CSS styles'
];

foreach ($dependencies as $file => $description) {
    if (file_exists($file)) {
        echo "<div class='diagnostic-item success'>
            <strong>✅ {$description}:</strong> {$file}
        </div>";
    } else {
        echo "<div class='diagnostic-item error'>
            <strong>❌ {$description}:</strong> {$file} - Missing
        </div>";
    }
}

echo "</div>";

// Test 3: URL Access Test
echo "<div class='test-section'>
    <h2>🌐 URL Access Test</h2>
    <p>Testing direct access to the tournament configuration page:</p>";

$testUrls = [
    'admin/events/tournament_config.php?event_sport_id=1&event_id=1' => 'Tournament Config with Parameters',
    'admin/events/index.php' => 'Events Index Page',
    'admin/dashboard.php' => 'Admin Dashboard'
];

foreach ($testUrls as $url => $description) {
    echo "<div class='diagnostic-item'>
        <strong>{$description}:</strong><br>
        <a href='{$url}' target='_blank' class='demo-link'>Test Access: {$url}</a>
    </div>";
}

echo "</div>";

// Test 4: Tournament Configuration Features
echo "<div class='test-section'>
    <h2>🏆 Tournament Configuration Features</h2>
    <p>Overview of implemented tournament configuration features:</p>";

$features = [
    '27 Tournament Formats' => 'Complete format selection across 5 sport categories',
    'Professional Interface' => 'Two-panel layout with format selection and bracket visualization',
    'Drag-and-Drop Management' => 'Interactive participant seeding and management',
    'Real-time Bracket Generation' => 'Dynamic bracket creation based on selected format',
    'Category-Specific Formats' => 'Team, Individual, Performing Arts, Academic, Pageant formats',
    'Responsive Design' => 'Mobile-friendly interface with touch support',
    'Auto-save Functionality' => 'Automatic saving of configuration changes',
    'Export Capabilities' => 'Bracket export functionality',
    'Validation System' => 'Form validation and error handling'
];

foreach ($features as $feature => $description) {
    echo "<div class='diagnostic-item success'>
        <strong>✅ {$feature}:</strong> {$description}
    </div>";
}

echo "</div>";

// Test 5: Troubleshooting Guide
echo "<div class='test-section'>
    <h2>🔧 Troubleshooting Guide</h2>
    <p>If the tournament configuration page is not displaying properly, try these steps:</p>";

$troubleshootingSteps = [
    'Clear Browser Cache' => 'Clear your browser cache and cookies, then refresh the page',
    'Check Browser Console' => 'Open browser developer tools (F12) and check for JavaScript errors',
    'Verify Database Connection' => 'Ensure the database is running and accessible',
    'Check File Permissions' => 'Verify that web server has read access to all files',
    'Test with Different Browser' => 'Try accessing the page with a different web browser',
    'Check Error Logs' => 'Review web server error logs for any PHP errors',
    'Verify URL Parameters' => 'Ensure event_sport_id and event_id parameters are valid',
    'Test Authentication' => 'Make sure you are logged in with proper admin permissions'
];

foreach ($troubleshootingSteps as $step => $description) {
    echo "<div class='diagnostic-item warning'>
        <strong>⚠️ {$step}:</strong> {$description}
    </div>";
}

echo "</div>";

// Test 6: Direct Access Links
echo "<div class='test-section'>
    <h2>🚀 Direct Access Links</h2>
    <p>Use these links to access the tournament configuration system:</p>
    
    <div style='text-align: center; margin: 30px 0;'>
        <a href='admin/events/tournament_config.php?event_sport_id=1&event_id=1' class='demo-link'>
            🏆 Tournament Configuration (Test Data)
        </a>
        <a href='admin/events/create.php' class='demo-link'>
            📝 Create New Event
        </a>
        <a href='admin/events/index.php' class='demo-link'>
            📋 Events Management
        </a>
        <a href='admin/dashboard.php' class='demo-link'>
            🏠 Admin Dashboard
        </a>
    </div>
    
    <div class='code-block'>
# If you're still having issues, try accessing the page directly:
http://localhost/IMS/admin/events/tournament_config.php?event_sport_id=1&event_id=1

# Or start from the events management page:
http://localhost/IMS/admin/events/index.php
    </div>
    
    <div style='background: #f0f9ff; padding: 20px; border-radius: 8px; border-left: 4px solid #06b6d4; margin-top: 20px;'>
        <h4 style='color: #0c4a6e; margin-bottom: 10px;'>💡 Quick Fix:</h4>
        <p style='color: #0369a1; margin: 0;'>
            If the page appears blank or doesn't load, the most common cause is browser caching. 
            Try pressing <strong>Ctrl+F5</strong> (or Cmd+Shift+R on Mac) to force refresh the page.
        </p>
    </div>
</div>";

echo "    </div>
</body>
</html>";
?>
