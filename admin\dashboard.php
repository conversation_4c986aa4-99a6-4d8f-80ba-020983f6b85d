<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Admin Dashboard
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// Require authentication
requireAuth();

// Get dashboard statistics
try {
    // Total events
    $totalEvents = fetchOne("SELECT COUNT(*) as count FROM events")['count'];
    
    // Active events
    $activeEvents = fetchOne("SELECT COUNT(*) as count FROM events WHERE status = 'ongoing'")['count'];
    
    // Total departments
    $totalDepartments = fetchOne("SELECT COUNT(*) as count FROM departments WHERE status = 'active'")['count'];

    // Total venues
    $totalVenues = fetchOne("SELECT COUNT(*) as count FROM venues WHERE status = 'available'")['count'];
    
    // Total matches today
    $matchesToday = fetchOne("SELECT COUNT(*) as count FROM matches WHERE match_date = CURDATE()")['count'];
    
    // Ongoing matches
    $ongoingMatches = fetchOne("SELECT COUNT(*) as count FROM matches WHERE status = 'ongoing'")['count'];
    
    // Completed matches today
    $completedToday = fetchOne("SELECT COUNT(*) as count FROM matches WHERE match_date = CURDATE() AND status = 'completed'")['count'];
    
    // Recent activities (last 10)
    $recentMatches = fetchAll("
        SELECT m.match_id, m.match_date, m.match_time, m.status, m.round_type,
               s.name as sport_name, v.name as venue_name,
               'TBD' as participants
        FROM matches m
        JOIN sports s ON m.sport_id = s.sport_id
        LEFT JOIN venues v ON m.venue_id = v.venue_id
        WHERE m.match_date >= CURDATE() - INTERVAL 7 DAY
        ORDER BY m.match_date DESC, m.match_time DESC
        LIMIT 10
    ");
    
    // Upcoming matches (next 5)
    $upcomingMatches = fetchAll("
        SELECT m.match_id, m.match_date, m.match_time, m.status,
               s.name as sport_name, v.name as venue_name,
               'TBD' as participants
        FROM matches m
        JOIN sports s ON m.sport_id = s.sport_id
        LEFT JOIN venues v ON m.venue_id = v.venue_id
        WHERE m.match_date >= CURDATE() AND m.status = 'scheduled'
        ORDER BY m.match_date ASC, m.match_time ASC
        LIMIT 5
    ");
    
    // Top departments (simplified for now)
    $topDepartments = fetchAll("
        SELECT d.name, d.abbreviation, d.color_code,
               0 as total_points,
               0 as gold,
               0 as silver,
               0 as bronze,
               0 as rank_position
        FROM departments d
        WHERE d.status = 'active'
        ORDER BY d.name ASC
        LIMIT 5
    ");
    
} catch (Exception $e) {
    $error = "Error loading dashboard data: " . $e->getMessage();
}

$currentUser = getCurrentUser();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body class="admin-page">
    <?php include 'includes/header.php'; ?>
    <?php include 'includes/sidebar.php'; ?>
    
    <main class="main-content">
        <div class="page-header">
            <h1>Dashboard</h1>
            <p>Welcome back, <?php echo htmlspecialchars($currentUser['full_name']); ?>!</p>
        </div>
        
        <?php displayFlashMessage(); ?>
        
        <?php if (isset($error)): ?>
            <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="icon-calendar"></i>
                </div>
                <div class="stat-content">
                    <h3><?php echo $totalEvents; ?></h3>
                    <p>Total Events</p>
                    <span class="stat-change positive">
                        <?php echo $activeEvents; ?> active
                    </span>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="icon-users"></i>
                </div>
                <div class="stat-content">
                    <h3><?php echo $totalDepartments; ?></h3>
                    <p>Departments</p>
                    <span class="stat-change">
                        Participating
                    </span>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="icon-location"></i>
                </div>
                <div class="stat-content">
                    <h3><?php echo $totalVenues; ?></h3>
                    <p>Venues</p>
                    <span class="stat-change">
                        Available
                    </span>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="icon-trophy"></i>
                </div>
                <div class="stat-content">
                    <h3><?php echo $matchesToday; ?></h3>
                    <p>Matches Today</p>
                    <span class="stat-change <?php echo $ongoingMatches > 0 ? 'positive' : ''; ?>">
                        <?php echo $ongoingMatches; ?> ongoing
                    </span>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="icon-check"></i>
                </div>
                <div class="stat-content">
                    <h3><?php echo $completedToday; ?></h3>
                    <p>Completed Today</p>
                    <span class="stat-change positive">
                        Finished
                    </span>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="icon-settings"></i>
                </div>
                <div class="stat-content">
                    <h3><i class="icon-heartbeat"></i></h3>
                    <p>System Health</p>
                    <a href="system_health.php" class="stat-change positive">
                        Check Status
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Dashboard Content Grid -->
        <div class="dashboard-grid">
            <!-- Recent Matches -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h2>Recent Matches</h2>
                    <a href="matches/" class="btn btn-sm btn-outline">View All</a>
                </div>
                <div class="card-content">
                    <?php if (empty($recentMatches)): ?>
                        <p class="no-data">No recent matches found.</p>
                    <?php else: ?>
                        <div class="matches-list">
                            <?php foreach ($recentMatches as $match): ?>
                                <div class="match-item">
                                    <div class="match-info">
                                        <h4><?php echo htmlspecialchars($match['sport_name']); ?></h4>
                                        <p><?php echo htmlspecialchars($match['participants'] ?? 'TBD'); ?></p>
                                        <small>
                                            <?php echo formatDate($match['match_date']); ?> at 
                                            <?php echo formatTime($match['match_time']); ?>
                                            <?php if ($match['venue_name']): ?>
                                                - <?php echo htmlspecialchars($match['venue_name']); ?>
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                    <div class="match-status">
                                        <span class="status-badge status-<?php echo $match['status']; ?>">
                                            <?php echo ucfirst($match['status']); ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Upcoming Matches -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h2>Upcoming Matches</h2>
                    <a href="matches/schedule.php" class="btn btn-sm btn-outline">View Schedule</a>
                </div>
                <div class="card-content">
                    <?php if (empty($upcomingMatches)): ?>
                        <p class="no-data">No upcoming matches scheduled.</p>
                    <?php else: ?>
                        <div class="matches-list">
                            <?php foreach ($upcomingMatches as $match): ?>
                                <div class="match-item">
                                    <div class="match-info">
                                        <h4><?php echo htmlspecialchars($match['sport_name']); ?></h4>
                                        <p><?php echo htmlspecialchars($match['participants'] ?? 'TBD'); ?></p>
                                        <small>
                                            <?php echo formatDate($match['match_date']); ?> at 
                                            <?php echo formatTime($match['match_time']); ?>
                                            <?php if ($match['venue_name']): ?>
                                                - <?php echo htmlspecialchars($match['venue_name']); ?>
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                    <div class="match-actions">
                                        <a href="matches/view.php?id=<?php echo $match['match_id']; ?>" 
                                           class="btn btn-xs btn-primary">View</a>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Top Departments -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h2>Department Standings</h2>
                    <a href="reports/standings.php" class="btn btn-sm btn-outline">View Full Standings</a>
                </div>
                <div class="card-content">
                    <?php if (empty($topDepartments)): ?>
                        <p class="no-data">No standings data available.</p>
                    <?php else: ?>
                        <div class="standings-list">
                            <?php foreach ($topDepartments as $index => $dept): ?>
                                <div class="standing-item">
                                    <div class="rank">
                                        <?php echo $index + 1; ?>
                                    </div>
                                    <div class="dept-info">
                                        <div class="dept-color" style="background-color: <?php echo htmlspecialchars($dept['color_code']); ?>"></div>
                                        <div>
                                            <h4><?php echo htmlspecialchars($dept['abbreviation']); ?></h4>
                                            <small><?php echo htmlspecialchars($dept['name']); ?></small>
                                        </div>
                                    </div>
                                    <div class="dept-stats">
                                        <div class="points"><?php echo number_format($dept['total_points'], 1); ?> pts</div>
                                        <div class="medals">
                                            <span class="medal gold"><?php echo $dept['gold']; ?></span>
                                            <span class="medal silver"><?php echo $dept['silver']; ?></span>
                                            <span class="medal bronze"><?php echo $dept['bronze']; ?></span>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>
    
    <script src="../assets/js/admin.js"></script>
    <script>
        // Auto-refresh dashboard every 30 seconds
        setInterval(function() {
            // Only refresh if page is visible
            if (!document.hidden) {
                location.reload();
            }
        }, 30000);
        
        // Update last activity timestamp
        setInterval(function() {
            fetch('ajax/update_activity.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'update_activity'
                })
            });
        }, 60000); // Every minute
    </script>
</body>
</html>
