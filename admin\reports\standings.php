<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Department Standings Report
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication
requireAuth();

// Get events for filter
$events = fetchAll("SELECT event_id, name FROM events ORDER BY start_date DESC");
$selectedEventId = (int)($_GET['event_id'] ?? 0);

// If no event selected, use the most recent active event
if (!$selectedEventId && !empty($events)) {
    $activeEvent = fetchOne("SELECT event_id FROM events WHERE status IN ('ongoing', 'upcoming') ORDER BY start_date DESC LIMIT 1");
    $selectedEventId = $activeEvent['event_id'] ?? $events[0]['event_id'];
}

$selectedEvent = null;
if ($selectedEventId) {
    $selectedEvent = fetchOne("SELECT * FROM events WHERE event_id = ?", [$selectedEventId]);
}

// Get department standings
$standings = [];
if ($selectedEventId) {
    $standings = fetchAll("
        SELECT d.dept_id, d.name, d.abbreviation, d.color_code,
               COALESCE(ds.total_points, 0) as total_points,
               COALESCE(ds.medals_gold, 0) as medals_gold,
               COALESCE(ds.medals_silver, 0) as medals_silver,
               COALESCE(ds.medals_bronze, 0) as medals_bronze,
               COALESCE(ds.matches_played, 0) as matches_played,
               COALESCE(ds.matches_won, 0) as matches_won,
               COALESCE(ds.matches_lost, 0) as matches_lost,
               COALESCE(ds.matches_drawn, 0) as matches_drawn,
               COALESCE(ds.rank_position, 999) as rank_position
        FROM departments d
        LEFT JOIN department_standings ds ON d.dept_id = ds.dept_id AND ds.event_id = ?
        WHERE d.status = 'active'
        ORDER BY ds.total_points DESC, ds.medals_gold DESC, ds.medals_silver DESC, ds.medals_bronze DESC, d.name ASC
    ", [$selectedEventId]);
    
    // Update rank positions
    foreach ($standings as $index => &$dept) {
        $dept['rank_position'] = $index + 1;
    }
}

// Get medal distribution by sport
$medalsBySport = [];
if ($selectedEventId) {
    $medalsBySport = fetchAll("
        SELECT s.name as sport_name, s.category,
               COUNT(CASE WHEN sc.position = 1 THEN 1 END) as gold_count,
               COUNT(CASE WHEN sc.position = 2 THEN 1 END) as silver_count,
               COUNT(CASE WHEN sc.position = 3 THEN 1 END) as bronze_count,
               COUNT(sc.score_id) as total_medals
        FROM sports s
        JOIN matches m ON s.sport_id = m.sport_id
        JOIN scores sc ON m.match_id = sc.match_id
        WHERE m.event_id = ? AND sc.is_final = 1 AND sc.position <= 3
        GROUP BY s.sport_id, s.name, s.category
        ORDER BY total_medals DESC, s.name ASC
    ", [$selectedEventId]);
}

$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Department Standings - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>
    
    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1>Department Standings</h1>
                <nav class="breadcrumb">
                    <a href="../dashboard.php">Dashboard</a>
                    <span>/</span>
                    <a href="index.php">Reports</a>
                    <span>/</span>
                    <span>Standings</span>
                </nav>
            </div>
            <div class="page-actions">
                <button onclick="printStandings()" class="btn btn-secondary">
                    <i class="icon-print"></i>
                    Print
                </button>
                <button onclick="exportStandings()" class="btn btn-primary">
                    <i class="icon-download"></i>
                    Export
                </button>
            </div>
        </div>
        
        <!-- Event Filter -->
        <div class="filters-container">
            <form method="GET" class="filters-form">
                <div class="filter-group">
                    <label for="event_id">Event:</label>
                    <select name="event_id" id="event_id" class="form-control" onchange="this.form.submit()">
                        <option value="">Select Event</option>
                        <?php foreach ($events as $event): ?>
                            <option value="<?php echo $event['event_id']; ?>" 
                                    <?php echo $selectedEventId == $event['event_id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($event['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </form>
        </div>
        
        <?php if ($selectedEvent): ?>
            <div class="event-info">
                <h2><?php echo htmlspecialchars($selectedEvent['name']); ?></h2>
                <p class="event-dates">
                    <?php echo formatDate($selectedEvent['start_date']); ?> - 
                    <?php echo formatDate($selectedEvent['end_date']); ?>
                </p>
                <span class="status-badge status-<?php echo $selectedEvent['status']; ?>">
                    <?php echo ucfirst($selectedEvent['status']); ?>
                </span>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($standings)): ?>
            <!-- Overall Standings -->
            <div class="standings-container">
                <div class="standings-header">
                    <h2>Overall Department Standings</h2>
                    <div class="standings-legend">
                        <span class="legend-item"><span class="medal gold"></span> Gold</span>
                        <span class="legend-item"><span class="medal silver"></span> Silver</span>
                        <span class="legend-item"><span class="medal bronze"></span> Bronze</span>
                    </div>
                </div>
                
                <div class="standings-table-container">
                    <table class="standings-table">
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Department</th>
                                <th>Total Points</th>
                                <th>Gold</th>
                                <th>Silver</th>
                                <th>Bronze</th>
                                <th>Matches</th>
                                <th>Win Rate</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($standings as $dept): ?>
                                <tr class="dept-row" data-dept-id="<?php echo $dept['dept_id']; ?>">
                                    <td class="rank-cell">
                                        <span class="rank-number rank-<?php echo $dept['rank_position']; ?>">
                                            <?php echo $dept['rank_position']; ?>
                                        </span>
                                    </td>
                                    <td class="dept-cell">
                                        <div class="dept-info">
                                            <div class="dept-color" style="background-color: <?php echo htmlspecialchars($dept['color_code']); ?>"></div>
                                            <div class="dept-details">
                                                <strong><?php echo htmlspecialchars($dept['abbreviation']); ?></strong>
                                                <br><small><?php echo htmlspecialchars($dept['name']); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="points-cell">
                                        <strong><?php echo number_format($dept['total_points'], 1); ?></strong>
                                    </td>
                                    <td class="medal-cell">
                                        <span class="medal-count gold"><?php echo $dept['medals_gold']; ?></span>
                                    </td>
                                    <td class="medal-cell">
                                        <span class="medal-count silver"><?php echo $dept['medals_silver']; ?></span>
                                    </td>
                                    <td class="medal-cell">
                                        <span class="medal-count bronze"><?php echo $dept['medals_bronze']; ?></span>
                                    </td>
                                    <td class="matches-cell">
                                        <div class="match-stats">
                                            <strong><?php echo $dept['matches_played']; ?></strong>
                                            <small>(<?php echo $dept['matches_won']; ?>W-<?php echo $dept['matches_lost']; ?>L<?php echo $dept['matches_drawn'] > 0 ? '-' . $dept['matches_drawn'] . 'D' : ''; ?>)</small>
                                        </div>
                                    </td>
                                    <td class="winrate-cell">
                                        <?php 
                                        $winRate = $dept['matches_played'] > 0 ? ($dept['matches_won'] / $dept['matches_played']) * 100 : 0;
                                        ?>
                                        <span class="win-rate"><?php echo number_format($winRate, 1); ?>%</span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- Medal Distribution by Sport -->
            <?php if (!empty($medalsBySport)): ?>
                <div class="medals-by-sport">
                    <h2>Medal Distribution by Sport</h2>
                    <div class="sport-medals-grid">
                        <?php foreach ($medalsBySport as $sport): ?>
                            <div class="sport-medal-card">
                                <h3><?php echo htmlspecialchars($sport['sport_name']); ?></h3>
                                <div class="sport-category">
                                    <span class="category-badge category-<?php echo $sport['category']; ?>">
                                        <?php echo ucfirst(str_replace('_', ' ', $sport['category'])); ?>
                                    </span>
                                </div>
                                <div class="medal-distribution">
                                    <div class="medal-item">
                                        <span class="medal gold"></span>
                                        <span class="medal-count"><?php echo $sport['gold_count']; ?></span>
                                    </div>
                                    <div class="medal-item">
                                        <span class="medal silver"></span>
                                        <span class="medal-count"><?php echo $sport['silver_count']; ?></span>
                                    </div>
                                    <div class="medal-item">
                                        <span class="medal bronze"></span>
                                        <span class="medal-count"><?php echo $sport['bronze_count']; ?></span>
                                    </div>
                                </div>
                                <div class="total-medals">
                                    Total: <?php echo $sport['total_medals']; ?> medals
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
            
        <?php else: ?>
            <div class="no-data">
                <h3>No standings data available</h3>
                <p>Select an event to view department standings.</p>
            </div>
        <?php endif; ?>
    </main>
    
    <script src="../../assets/js/admin.js"></script>
    <script>
        function printStandings() {
            window.print();
        }
        
        function exportStandings() {
            const eventId = document.getElementById('event_id').value;
            if (eventId) {
                window.location.href = 'export_standings.php?event_id=' + eventId + '&format=excel';
            } else {
                alert('Please select an event first.');
            }
        }
        
        // Add click handlers for department rows
        document.querySelectorAll('.dept-row').forEach(row => {
            row.addEventListener('click', function() {
                const deptId = this.dataset.deptId;
                const eventId = document.getElementById('event_id').value;
                if (deptId && eventId) {
                    window.location.href = 'department_detail.php?dept_id=' + deptId + '&event_id=' + eventId;
                }
            });
        });
    </script>
</body>
</html>
