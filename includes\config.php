<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Database Configuration and Core Settings
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

// Prevent direct access
if (!defined('SCIMS_ACCESS')) {
    die('Direct access not permitted');
}

// Error reporting for development (disable in production)
define('DEVELOPMENT_MODE', true); // Set to false in production
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'IMS_db');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// Application Configuration
define('APP_NAME', 'Samar College Intramurals Management System');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'http://localhost/IMS');
define('ADMIN_URL', APP_URL . '/admin');
define('PUBLIC_URL', APP_URL . '/public');

// Security Configuration
define('SESSION_TIMEOUT', 1800); // 30 minutes
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOCKOUT_DURATION', 900); // 15 minutes
define('CSRF_TOKEN_EXPIRE', 3600); // 1 hour

// File Upload Configuration
define('UPLOAD_MAX_SIZE', 2097152); // 2MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif']);
define('UPLOAD_PATH', dirname(__DIR__) . '/assets/images/uploads/');

// Pagination Configuration
define('RECORDS_PER_PAGE', 20);
define('MAX_PAGINATION_LINKS', 10);

// Live Update Configuration
define('SCORE_UPDATE_INTERVAL', 30); // seconds
define('CACHE_DURATION', 300); // 5 minutes

// Timezone Configuration
date_default_timezone_set('Asia/Manila');

/**
 * Database Connection Class
 */
class Database {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            // First try to connect without database to check if it exists
            $dsn_check = "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET;
            $temp_conn = new PDO($dsn_check, DB_USER, DB_PASS, [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]);

            // Check if database exists
            $stmt = $temp_conn->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?");
            $stmt->execute([DB_NAME]);

            if (!$stmt->fetch()) {
                // Database doesn't exist, create it
                $temp_conn->exec("CREATE DATABASE IF NOT EXISTS `" . DB_NAME . "` CHARACTER SET " . DB_CHARSET . " COLLATE " . DB_CHARSET . "_unicode_ci");
                error_log("Database '" . DB_NAME . "' created successfully.");
            }

            // Now connect to the specific database
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
            ];

            $this->connection = new PDO($dsn, DB_USER, DB_PASS, $options);

        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            die("Database connection failed: " . $e->getMessage() . ". Please check your configuration.");
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    public function prepare($sql) {
        return $this->connection->prepare($sql);
    }
    
    public function query($sql) {
        return $this->connection->query($sql);
    }
    
    public function lastInsertId() {
        return $this->connection->lastInsertId();
    }
    
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }
    
    public function commit() {
        return $this->connection->commit();
    }
    
    public function rollback() {
        return $this->connection->rollback();
    }
}

/**
 * Get database instance
 */
function getDB() {
    return Database::getInstance();
}

/**
 * Execute prepared statement with error handling
 */
function executeQuery($sql, $params = []) {
    try {
        $db = getDB();
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } catch (PDOException $e) {
        $error_msg = "Query execution failed: " . $e->getMessage() . " | SQL: " . $sql . " | Params: " . json_encode($params);
        error_log($error_msg);

        // In development, show detailed error
        if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE) {
            throw new Exception($error_msg);
        } else {
            throw new Exception("Database operation failed: " . $e->getMessage());
        }
    }
}

/**
 * Fetch single record
 */
function fetchOne($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt->fetch();
}

/**
 * Fetch multiple records
 */
function fetchAll($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt->fetchAll();
}

/**
 * Insert record and return ID
 */
function insertRecord($table, $data) {
    $columns = implode(',', array_keys($data));
    $placeholders = ':' . implode(', :', array_keys($data));
    
    $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
    executeQuery($sql, $data);
    
    return getDB()->lastInsertId();
}

/**
 * Update record
 */
function updateRecord($table, $data, $where, $whereParams = []) {
    $setParts = [];
    foreach (array_keys($data) as $key) {
        $setParts[] = "{$key} = :{$key}";
    }
    $setClause = implode(', ', $setParts);
    
    $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
    $params = array_merge($data, $whereParams);
    
    return executeQuery($sql, $params);
}

/**
 * Delete record
 */
function deleteRecord($table, $where, $params = []) {
    $sql = "DELETE FROM {$table} WHERE {$where}";
    return executeQuery($sql, $params);
}

/**
 * Check if table exists
 */
function tableExists($tableName) {
    try {
        $db = getDB();
        // Use INFORMATION_SCHEMA which is more reliable across MySQL/MariaDB versions
        $stmt = $db->prepare("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?");
        $stmt->execute([DB_NAME, $tableName]);
        return $stmt->fetchColumn() > 0;
    } catch (PDOException $e) {
        error_log("Error checking table existence: " . $e->getMessage());
        return false;
    }
}

/**
 * Get system setting
 */
function getSetting($key, $default = null) {
    $sql = "SELECT setting_value FROM system_settings WHERE setting_key = :key";
    $result = fetchOne($sql, ['key' => $key]);
    return $result ? $result['setting_value'] : $default;
}

/**
 * Set system setting
 */
function setSetting($key, $value, $description = null) {
    $sql = "INSERT INTO system_settings (setting_key, setting_value, description)
            VALUES (:key, :value, :description)
            ON DUPLICATE KEY UPDATE
            setting_value = :update_value,
            description = COALESCE(:update_description, description),
            updated_at = CURRENT_TIMESTAMP";

    return executeQuery($sql, [
        'key' => $key,
        'value' => $value,
        'description' => $description,
        'update_value' => $value,
        'update_description' => $description
    ]);
}

// Initialize session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Set session timeout
if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT)) {
    session_unset();
    session_destroy();
    session_start();
}
$_SESSION['last_activity'] = time();

// Include helper functions
require_once __DIR__ . '/helpers.php';
?>
