<?php
/**
 * Reset Admin Password Tool
 * Use this to create or reset the admin account
 */

// Define access constant to bypass security check
define('SCIMS_ACCESS', true);

require_once 'includes/config.php';

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $username = $_POST['username'] ?? 'admin';
        $password = $_POST['password'] ?? 'Admin123!';
        $email = $_POST['email'] ?? '<EMAIL>';
        $fullName = $_POST['full_name'] ?? 'System Administrator';
        
        // Validate input
        if (empty($username) || empty($password) || empty($email) || empty($fullName)) {
            throw new Exception('All fields are required');
        }
        
        if (strlen($password) < 8) {
            throw new Exception('Password must be at least 8 characters long');
        }
        
        $db = getDB();
        
        // Check if user exists
        $existingUser = $db->prepare("SELECT admin_id FROM admin_users WHERE username = ?");
        $existingUser->execute([$username]);
        
        $passwordHash = password_hash($password, PASSWORD_DEFAULT);
        
        if ($existingUser->fetch()) {
            // Update existing user
            $stmt = $db->prepare("
                UPDATE admin_users 
                SET password_hash = ?, email = ?, full_name = ?, role = 'super_admin', 
                    status = 'active', failed_login_attempts = 0, locked_until = NULL,
                    updated_at = CURRENT_TIMESTAMP
                WHERE username = ?
            ");
            $stmt->execute([$passwordHash, $email, $fullName, $username]);
            $message = "Admin user '$username' updated successfully!";
        } else {
            // Create new user
            $stmt = $db->prepare("
                INSERT INTO admin_users (username, password_hash, email, full_name, role, status) 
                VALUES (?, ?, ?, ?, 'super_admin', 'active')
            ");
            $stmt->execute([$username, $passwordHash, $email, $fullName]);
            $message = "Admin user '$username' created successfully!";
        }
        
        $message .= "<br><strong>Username:</strong> $username<br><strong>Password:</strong> $password";
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Admin Password - SCIMS</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"], input[type="password"], input[type="email"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
        }
        button:hover {
            background: #005a87;
        }
        .message {
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            margin-bottom: 20px;
        }
        .links {
            text-align: center;
            margin-top: 20px;
        }
        .links a {
            color: #007cba;
            text-decoration: none;
            margin: 0 10px;
        }
        .links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔑 Reset Admin Password</h1>
        
        <div class="info">
            <strong>Note:</strong> This tool will create a new admin account or reset the password for an existing one. 
            Use this if you're unable to log into the admin panel.
        </div>
        
        <?php if ($message): ?>
            <div class="message success">
                ✅ <?= $message ?>
            </div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="message error">
                ❌ <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>
        
        <form method="POST">
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" name="username" value="admin" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" value="Admin123!" required>
                <small style="color: #666;">Minimum 8 characters</small>
            </div>
            
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="full_name">Full Name:</label>
                <input type="text" id="full_name" name="full_name" value="System Administrator" required>
            </div>
            
            <button type="submit">🔄 Create/Reset Admin Account</button>
        </form>
        
        <div class="links">
            <a href="debug-login.php">🔍 Debug Login Issues</a>
            <a href="test-db.php">🗄️ Test Database</a>
            <a href="admin/">🚪 Admin Login</a>
        </div>
    </div>
</body>
</html>
