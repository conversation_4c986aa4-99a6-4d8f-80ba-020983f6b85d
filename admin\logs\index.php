<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Activity Logs - Coming Soon
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication
requireAuth();

// Only super admin can access this
if (getCurrentUser()['role'] !== 'super_admin') {
    header('Location: ../dashboard.php?message=Access denied&type=error');
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Activity Logs - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>
    
    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1>Activity Logs</h1>
                <nav class="breadcrumb">
                    <a href="../dashboard.php">Dashboard</a>
                    <span>/</span>
                    <span>Activity Logs</span>
                </nav>
            </div>
        </div>
        
        <div class="coming-soon">
            <div class="coming-soon-content">
                <h2>📋 Activity Logs</h2>
                <p>This module is currently under development and will be available in a future update.</p>
                
                <div class="planned-features">
                    <h3>Planned Features:</h3>
                    <ul>
                        <li>✅ User activity tracking</li>
                        <li>✅ System event logging</li>
                        <li>✅ Error and warning logs</li>
                        <li>✅ Security audit trails</li>
                        <li>✅ Log filtering and search</li>
                        <li>✅ Log export and archiving</li>
                    </ul>
                </div>
                
                <div class="log-info">
                    <h3>Current Logging Status:</h3>
                    <div class="status-cards">
                        <div class="status-card success">
                            <h4>✅ Activity Logging</h4>
                            <p>User activities are being logged to the database</p>
                        </div>
                        <div class="status-card info">
                            <h4>📊 Log Storage</h4>
                            <p>Logs are stored in the <code>activity_logs</code> table</p>
                        </div>
                        <div class="status-card warning">
                            <h4>⚠️ Log Viewer</h4>
                            <p>Log viewing interface is under development</p>
                        </div>
                    </div>
                </div>
                
                <div class="recent-activity">
                    <h3>Recent System Activity:</h3>
                    <div class="activity-preview">
                        <p><em>Activity log viewer will be available in the next update.</em></p>
                        <p>For now, you can check the database directly or use the system health monitor.</p>
                    </div>
                </div>
                
                <div class="alternative-actions">
                    <h3>Available Actions:</h3>
                    <div class="action-cards">
                        <a href="../system_health.php" class="action-card">
                            <i class="icon-heart"></i>
                            <h4>System Health</h4>
                            <p>Check system status and diagnostics</p>
                        </a>
                        <a href="../users/" class="action-card">
                            <i class="icon-admin"></i>
                            <h4>Admin Users</h4>
                            <p>Manage administrator accounts</p>
                        </a>
                        <a href="../settings/" class="action-card">
                            <i class="icon-settings"></i>
                            <h4>System Settings</h4>
                            <p>Configure system preferences</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <script src="../../assets/js/admin.js"></script>
</body>
</html>
