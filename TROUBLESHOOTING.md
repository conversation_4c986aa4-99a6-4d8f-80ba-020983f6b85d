# SCIMS Troubleshooting Guide

## 🔧 Common Issues and Solutions

### 1. Fatal Error: Cannot redeclare insertRecord()

**Error Message:**
```
Fatal error: Cannot redeclare insertRecord() (previously declared in C:\xampp\htdocs\IMS\includes\config.php:156) in C:\xampp\htdocs\IMS\includes\functions.php on line 330
```

**Solution:**
✅ **FIXED** - Removed duplicate function declarations from `functions.php`. The functions are now only declared in `config.php`.

### 2. Database Connection Issues

**Symptoms:**
- "Database connection failed" error
- Admin login not working
- Tables not found errors

**Solutions:**

#### Step 1: Check Database Configuration
1. Open `includes/config.php`
2. Verify database settings:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'IMS_db');
   define('DB_USER', 'root');
   define('DB_PASS', '');
   ```

#### Step 2: Test Database Connection
1. Navigate to `http://localhost/IMS/test-db.php`
2. Check the connection status
3. If connection fails, verify:
   - XAMPP/WAMP is running
   - MySQL service is started
   - Database credentials are correct

#### Step 3: Run Setup Script
If database doesn't exist:
1. Go to `http://localhost/IMS/setup.php`
2. Follow the 5-step installation wizard
3. Create admin user when prompted

### 3. Admin Login Issues

**Symptoms:**
- Cannot access admin panel
- "Access denied" messages
- Login form not working

**Solutions:**

#### Default Admin Credentials
After running setup, use:
- **Username**: admin
- **Password**: Admin123!

#### Reset Admin Password
If you forgot the password:
1. Access your database via phpMyAdmin
2. Go to `admin_users` table
3. Update password hash:
   ```sql
   UPDATE admin_users 
   SET password_hash = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi' 
   WHERE username = 'admin';
   ```
   (This sets password to "password")

### 4. Permission Errors

**Symptoms:**
- "Insufficient permissions" messages
- Cannot access certain admin sections

**Solutions:**
1. Check user role in database
2. Super admin has all permissions
3. Regular admin has limited permissions
4. Update user role if needed:
   ```sql
   UPDATE admin_users SET role = 'super_admin' WHERE username = 'your_username';
   ```

### 5. CSS/JavaScript Not Loading

**Symptoms:**
- Unstyled pages
- JavaScript features not working
- Layout appears broken

**Solutions:**
1. Check file paths in HTML
2. Verify web server is serving static files
3. Clear browser cache
4. Check browser console for 404 errors

### 6. File Permission Issues (Linux/Mac)

**Symptoms:**
- Cannot write to files
- Upload errors
- Log file errors

**Solutions:**
```bash
# Set proper permissions
chmod 755 assets/images/uploads/
chmod 755 logs/
chmod 755 backups/
chmod 644 includes/config.php
```

### 7. PHP Version Compatibility

**Requirements:**
- PHP 8.0 or higher
- PDO MySQL extension
- JSON extension
- Session support

**Check PHP Version:**
```php
<?php
echo "PHP Version: " . PHP_VERSION;
echo "PDO MySQL: " . (extension_loaded('pdo_mysql') ? 'Yes' : 'No');
?>
```

## 🚀 Quick Setup Checklist

### Prerequisites
- [ ] XAMPP/WAMP/LAMP stack installed
- [ ] Apache and MySQL services running
- [ ] PHP 8.0+ with PDO MySQL extension

### Installation Steps
1. [ ] Extract SCIMS files to web directory
2. [ ] Navigate to `http://localhost/IMS/test-db.php`
3. [ ] If database doesn't exist, run `http://localhost/IMS/setup.php`
4. [ ] Complete 5-step setup wizard
5. [ ] Login to admin panel at `http://localhost/IMS/admin/`
6. [ ] Change default admin password

### Verification Steps
1. [ ] Database connection successful
2. [ ] All tables created
3. [ ] Admin login working
4. [ ] Can create events, departments, sports, venues
5. [ ] CSS and JavaScript loading properly

## 📞 Getting Help

### Debug Information
When reporting issues, include:
1. PHP version
2. Database type and version
3. Web server (Apache/Nginx)
4. Operating system
5. Error messages (full text)
6. Steps to reproduce

### Log Files
Check these locations for error logs:
- `logs/error.log` (SCIMS application logs)
- `C:\xampp\apache\logs\error.log` (XAMPP Apache logs)
- `/var/log/apache2/error.log` (Linux Apache logs)

### Browser Developer Tools
1. Press F12 to open developer tools
2. Check Console tab for JavaScript errors
3. Check Network tab for failed requests
4. Check Elements tab for CSS issues

## 🔄 Reset Instructions

### Complete Reset
To start fresh:
1. Drop database: `DROP DATABASE IMS_db;`
2. Delete `includes/config.php`
3. Run setup script again

### Partial Reset
To keep database but reset admin:
1. Truncate admin_users table
2. Run setup script (will skip database creation)
3. Create new admin user

## ✅ Success Indicators

You'll know SCIMS is working correctly when:
- [ ] Admin dashboard loads without errors
- [ ] Can create and manage events
- [ ] Department management works
- [ ] Sports catalog displays properly
- [ ] Venue management functions
- [ ] All forms submit successfully
- [ ] No JavaScript console errors
- [ ] Responsive design works on mobile

## 📋 Common File Locations

```
IMS/
├── admin/                  # Admin panel
├── public/                 # Public interface
├── includes/config.php     # Database configuration
├── setup.php              # Installation script
├── test-db.php            # Database test page
└── logs/                  # Error logs
```

Remember: Always backup your database before making changes!
