-- Samar College Intramurals Management System (SCIMS) Database Schema
-- Database: IMS_db
-- Version: 1.0
-- Created: 2024

-- Create database
CREATE DATABASE IF NOT EXISTS IMS_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE IMS_db;

-- Drop tables if they exist (for clean installation)
SET FOREIGN_KEY_CHECKS = 0;
DROP TABLE IF EXISTS match_officials;
DROP TABLE IF EXISTS scores;
DROP TABLE IF EXISTS match_participants;
DROP TABLE IF EXISTS matches;
DROP TABLE IF EXISTS department_standings;
DROP TABLE IF EXISTS event_sports;
DROP TABLE IF EXISTS announcements;
DROP TABLE IF EXISTS referees;
DROP TABLE IF EXISTS venues;
DROP TABLE IF EXISTS sports;
DROP TABLE IF EXISTS departments;
DROP TABLE IF EXISTS events;
DROP TABLE IF EXISTS admin_users;
DROP TABLE IF EXISTS system_settings;
SET FOREIGN_KEY_CHECKS = 1;

-- 1. Admin Users Table
CREATE TABLE admin_users (
    admin_id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20) NULL,
    role ENUM('super_admin', 'admin', 'organizer') DEFAULT 'admin',
    status ENUM('active', 'inactive') DEFAULT 'active',
    last_login TIMESTAMP NULL,
    failed_login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 2. Events Table
CREATE TABLE events (
    event_id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status ENUM('upcoming', 'ongoing', 'completed') DEFAULT 'upcoming',
    description TEXT,
    logo_path VARCHAR(255),
    point_system TEXT, -- Stores point distribution (1st: 15, 2nd: 12, etc.)
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES admin_users(admin_id) ON DELETE SET NULL,
    INDEX idx_event_status (status),
    INDEX idx_event_dates (start_date, end_date)
);

-- 3. Departments Table
CREATE TABLE departments (
    dept_id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    abbreviation VARCHAR(10) UNIQUE NOT NULL,
    color_code VARCHAR(7) DEFAULT '#000000',
    contact_person VARCHAR(100),
    email VARCHAR(100),
    phone VARCHAR(20),
    logo_path VARCHAR(255),
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_dept_status (status)
);

-- 4. Sports Table
CREATE TABLE sports (
    sport_id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    category ENUM('team', 'individual', 'performing_arts', 'academic') NOT NULL,
    scoring_type ENUM('points', 'time', 'distance', 'subjective') NOT NULL,
    max_participants INT DEFAULT 1,
    min_participants INT DEFAULT 1,
    description TEXT,
    rules TEXT,
    equipment_needed TEXT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_sport_category (category),
    INDEX idx_sport_status (status)
);

-- 5. Venues Table
CREATE TABLE venues (
    venue_id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    location VARCHAR(200),
    capacity INT DEFAULT 0,
    facilities TEXT,
    status ENUM('available', 'maintenance', 'occupied') DEFAULT 'available',
    contact_person VARCHAR(100),
    booking_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_venue_status (status)
);

-- 6. Matches Table
CREATE TABLE matches (
    match_id INT PRIMARY KEY AUTO_INCREMENT,
    sport_id INT NOT NULL,
    venue_id INT,
    event_id INT NOT NULL,
    match_date DATE NOT NULL,
    match_time TIME NOT NULL,
    status ENUM('scheduled', 'ongoing', 'completed', 'cancelled') DEFAULT 'scheduled',
    round_type ENUM('preliminary', 'quarterfinal', 'semifinal', 'final', 'round_robin') DEFAULT 'preliminary',
    match_number VARCHAR(20),
    notes TEXT,
    winner_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (sport_id) REFERENCES sports(sport_id) ON DELETE CASCADE,
    FOREIGN KEY (venue_id) REFERENCES venues(venue_id) ON DELETE SET NULL,
    FOREIGN KEY (event_id) REFERENCES events(event_id) ON DELETE CASCADE,
    INDEX idx_match_date_time (match_date, match_time),
    INDEX idx_match_status (status),
    INDEX idx_match_event (event_id)
);

-- 7. Match Participants Table
CREATE TABLE match_participants (
    participant_id INT PRIMARY KEY AUTO_INCREMENT,
    match_id INT NOT NULL,
    dept_id INT NOT NULL,
    participant_name VARCHAR(100) NOT NULL,
    participant_type ENUM('team', 'individual') NOT NULL,
    jersey_number VARCHAR(10),
    position_role VARCHAR(50),
    is_captain BOOLEAN DEFAULT FALSE,
    status ENUM('registered', 'confirmed', 'disqualified') DEFAULT 'registered',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (match_id) REFERENCES matches(match_id) ON DELETE CASCADE,
    FOREIGN KEY (dept_id) REFERENCES departments(dept_id) ON DELETE CASCADE,
    INDEX idx_participant_match (match_id),
    INDEX idx_participant_dept (dept_id)
);

-- 8. Scores Table
CREATE TABLE scores (
    score_id INT PRIMARY KEY AUTO_INCREMENT,
    match_id INT NOT NULL,
    participant_id INT NOT NULL,
    points DECIMAL(10,2) DEFAULT 0,
    position INT,
    time_score TIME NULL, -- For time-based sports
    distance_score DECIMAL(10,2) NULL, -- For distance-based sports
    subjective_score TEXT, -- For subjective scoring (judges scores)
    is_final BOOLEAN DEFAULT FALSE,
    recorded_by INT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (match_id) REFERENCES matches(match_id) ON DELETE CASCADE,
    FOREIGN KEY (participant_id) REFERENCES match_participants(participant_id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES admin_users(admin_id) ON DELETE SET NULL,
    INDEX idx_score_match (match_id),
    INDEX idx_score_participant (participant_id)
);

-- 9. Referees Table
CREATE TABLE referees (
    referee_id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    contact_info VARCHAR(200),
    email VARCHAR(100),
    phone VARCHAR(20),
    specialization VARCHAR(100),
    certification_level ENUM('local', 'regional', 'national', 'international') DEFAULT 'local',
    status ENUM('active', 'inactive') DEFAULT 'active',
    experience_years INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_referee_status (status),
    INDEX idx_referee_specialization (specialization)
);

-- 10. Match Officials Table
CREATE TABLE match_officials (
    official_id INT PRIMARY KEY AUTO_INCREMENT,
    match_id INT NOT NULL,
    referee_id INT NOT NULL,
    role ENUM('head_referee', 'assistant', 'scorer', 'timekeeper') NOT NULL,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (match_id) REFERENCES matches(match_id) ON DELETE CASCADE,
    FOREIGN KEY (referee_id) REFERENCES referees(referee_id) ON DELETE CASCADE,
    UNIQUE KEY unique_match_role (match_id, role),
    INDEX idx_official_match (match_id)
);

-- 11. Announcements Table
CREATE TABLE announcements (
    announcement_id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    date_posted TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    status ENUM('active', 'archived') DEFAULT 'active',
    target_audience ENUM('all', 'departments', 'officials', 'public') DEFAULT 'all',
    posted_by INT,
    expires_at TIMESTAMP NULL,
    FOREIGN KEY (posted_by) REFERENCES admin_users(admin_id) ON DELETE SET NULL,
    INDEX idx_announcement_status (status),
    INDEX idx_announcement_priority (priority),
    INDEX idx_announcement_date (date_posted)
);

-- 12. Department Standings Table
CREATE TABLE department_standings (
    standing_id INT PRIMARY KEY AUTO_INCREMENT,
    dept_id INT NOT NULL,
    event_id INT NOT NULL,
    total_points DECIMAL(10,2) DEFAULT 0,
    rank_position INT DEFAULT 0,
    medals_gold INT DEFAULT 0,
    medals_silver INT DEFAULT 0,
    medals_bronze INT DEFAULT 0,
    matches_played INT DEFAULT 0,
    matches_won INT DEFAULT 0,
    matches_lost INT DEFAULT 0,
    matches_drawn INT DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (dept_id) REFERENCES departments(dept_id) ON DELETE CASCADE,
    FOREIGN KEY (event_id) REFERENCES events(event_id) ON DELETE CASCADE,
    UNIQUE KEY unique_dept_event (dept_id, event_id),
    INDEX idx_standings_event (event_id),
    INDEX idx_standings_rank (rank_position)
);

-- 13. Event Sports Table (Many-to-Many relationship)
CREATE TABLE event_sports (
    event_sport_id INT PRIMARY KEY AUTO_INCREMENT,
    event_id INT NOT NULL,
    sport_id INT NOT NULL,
    max_teams_per_dept INT DEFAULT 1,
    registration_deadline DATE,
    status ENUM('open', 'closed', 'completed') DEFAULT 'open',
    FOREIGN KEY (event_id) REFERENCES events(event_id) ON DELETE CASCADE,
    FOREIGN KEY (sport_id) REFERENCES sports(sport_id) ON DELETE CASCADE,
    UNIQUE KEY unique_event_sport (event_id, sport_id)
);

-- 14. System Settings Table
CREATE TABLE system_settings (
    setting_id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    description TEXT,
    updated_by INT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES admin_users(admin_id) ON DELETE SET NULL
);

-- Insert default admin user (password: Admin123!)
INSERT INTO admin_users (username, password_hash, email, full_name, role) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>', 'System Administrator', 'super_admin');

-- Insert sample departments
INSERT INTO departments (name, abbreviation, color_code, contact_person, email, phone) VALUES
('College of Engineering', 'COE', '#FF6B35', 'Dr. Maria Santos', '<EMAIL>', '***********'),
('College of Business Administration', 'CBA', '#004E89', 'Prof. Juan Dela Cruz', '<EMAIL>', '***********'),
('College of Arts and Sciences', 'CAS', '#1A936F', 'Dr. Ana Rodriguez', '<EMAIL>', '***********'),
('College of Education', 'COED', '#C5282F', 'Prof. Pedro Martinez', '<EMAIL>', '***********'),
('College of Information Technology', 'CIT', '#7209B7', 'Dr. Lisa Garcia', '<EMAIL>', '***********');

-- Insert sample sports
INSERT INTO sports (name, category, scoring_type, max_participants, min_participants, description) VALUES
('Basketball', 'team', 'points', 12, 5, 'Standard basketball game with 5 players on court'),
('Volleyball', 'team', 'points', 12, 6, 'Indoor volleyball with 6 players per team'),
('Track and Field - 100m', 'individual', 'time', 1, 1, '100 meter sprint race'),
('Track and Field - Long Jump', 'individual', 'distance', 1, 1, 'Long jump competition'),
('Chess', 'individual', 'points', 1, 1, 'Individual chess matches'),
('Swimming - 50m Freestyle', 'individual', 'time', 1, 1, '50 meter freestyle swimming'),
('Badminton Singles', 'individual', 'points', 1, 1, 'Singles badminton match'),
('Table Tennis', 'individual', 'points', 1, 1, 'Individual table tennis match'),
('Debate', 'team', 'subjective', 3, 3, 'Academic debate competition'),
('Dance Competition', 'team', 'subjective', 15, 5, 'Cultural dance performance');

-- Insert sample venues
INSERT INTO venues (name, location, capacity, facilities, status) VALUES
('Main Gymnasium', 'Sports Complex Building A', 2000, 'Basketball court, sound system, scoreboard', 'available'),
('Volleyball Court', 'Sports Complex Building B', 500, 'Volleyball net, bleachers', 'available'),
('Swimming Pool', 'Aquatic Center', 300, 'Olympic-size pool, timing system', 'available'),
('Track and Field Oval', 'Athletic Field', 1000, 'Running track, field events area', 'available'),
('Chess Hall', 'Academic Building Room 201', 100, 'Tables, chairs, chess sets', 'available'),
('Auditorium', 'Main Building', 800, 'Stage, sound system, lighting', 'available');

-- Insert sample event
INSERT INTO events (name, start_date, end_date, status, description, point_system) VALUES
('Samar College Intramurals 2024', '2024-02-15', '2024-02-25', 'upcoming', 'Annual intramural sports competition',
'{"1st": 15, "2nd": 12, "3rd": 10, "4th": 8, "5th": 6, "participation": 3}');

-- Insert system settings
INSERT INTO system_settings (setting_key, setting_value, description) VALUES
('site_name', 'Samar College Intramurals Management System', 'Website title'),
('max_login_attempts', '5', 'Maximum failed login attempts before lockout'),
('session_timeout', '1800', 'Session timeout in seconds (30 minutes)'),
('score_update_interval', '30', 'Live score update interval in seconds'),
('backup_retention_days', '30', 'Number of days to retain database backups');
