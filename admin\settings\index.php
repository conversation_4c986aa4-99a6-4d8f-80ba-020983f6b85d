<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * System Settings Management
 *
 * @version 1.0
 * <AUTHOR> Development Team
 */

declare(strict_types=1);

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication
requireAuth();

// Only super admin can access this
if (getCurrentUser()['role'] !== 'super_admin') {
    header('Location: ../dashboard.php?message=Access denied&type=error');
    exit;
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        switch ($_POST['action']) {
            case 'update_general':
                updateGeneralSettings($_POST);
                $message = 'General settings updated successfully';
                $messageType = 'success';
                break;

            case 'update_security':
                updateSecuritySettings($_POST);
                $message = 'Security settings updated successfully';
                $messageType = 'success';
                break;

            case 'update_event':
                updateEventSettings($_POST);
                $message = 'Event settings updated successfully';
                $messageType = 'success';
                break;

            case 'update_notification':
                updateNotificationSettings($_POST);
                $message = 'Notification settings updated successfully';
                $messageType = 'success';
                break;

            default:
                throw new Exception('Invalid action');
        }
    } catch (Exception $e) {
        $message = 'Error: ' . $e->getMessage();
        $messageType = 'error';
    }
}

/**
 * Helper Functions for Settings Management
 */

function getAllSettings(): array {
    $result = fetchAll("SELECT setting_key, setting_value, description FROM system_settings ORDER BY setting_key");
    $settings = [];
    foreach ($result as $row) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }
    return $settings;
}

function updateGeneralSettings(array $data): void {
    $validSettings = [
        'site_name' => 'Website title and branding',
        'site_description' => 'Website description for meta tags',
        'contact_email' => 'Primary contact email address',
        'contact_phone' => 'Primary contact phone number',
        'timezone' => 'System timezone setting',
        'date_format' => 'Default date display format',
        'time_format' => 'Default time display format'
    ];

    foreach ($validSettings as $key => $description) {
        if (isset($data[$key])) {
            $value = sanitizeInput($data[$key]);
            setSetting($key, $value, $description);
        }
    }
}

function updateSecuritySettings(array $data): void {
    $validSettings = [
        'max_login_attempts' => 'Maximum failed login attempts before lockout',
        'lockout_duration' => 'Account lockout duration in minutes',
        'session_timeout' => 'Session timeout in seconds',
        'password_min_length' => 'Minimum password length requirement',
        'require_password_complexity' => 'Require complex passwords (1=yes, 0=no)',
        'enable_two_factor' => 'Enable two-factor authentication (1=yes, 0=no)'
    ];

    foreach ($validSettings as $key => $description) {
        if (isset($data[$key])) {
            $value = sanitizeInput($data[$key]);
            // Validate numeric settings
            if (in_array($key, ['max_login_attempts', 'lockout_duration', 'session_timeout', 'password_min_length'])) {
                $value = max(1, (int)$value);
            }
            setSetting($key, (string)$value, $description);
        }
    }
}

function updateEventSettings(array $data): void {
    $validSettings = [
        'default_point_system' => 'Default point distribution for events',
        'allow_score_updates' => 'Allow score updates after finalization (1=yes, 0=no)',
        'score_update_interval' => 'Live score update interval in seconds',
        'auto_advance_rounds' => 'Automatically advance tournament rounds (1=yes, 0=no)',
        'require_score_approval' => 'Require approval for score entries (1=yes, 0=no)'
    ];

    foreach ($validSettings as $key => $description) {
        if (isset($data[$key])) {
            $value = sanitizeInput($data[$key]);
            setSetting($key, $value, $description);
        }
    }
}

function updateNotificationSettings(array $data): void {
    $validSettings = [
        'email_notifications' => 'Enable email notifications (1=yes, 0=no)',
        'smtp_host' => 'SMTP server hostname',
        'smtp_port' => 'SMTP server port',
        'smtp_username' => 'SMTP authentication username',
        'smtp_password' => 'SMTP authentication password',
        'smtp_encryption' => 'SMTP encryption method (tls/ssl)',
        'notification_from_email' => 'From email address for notifications',
        'notification_from_name' => 'From name for notifications'
    ];

    foreach ($validSettings as $key => $description) {
        if (isset($data[$key])) {
            $value = sanitizeInput($data[$key]);
            setSetting($key, $value, $description);
        }
    }
}

// Get current settings
$settings = getAllSettings();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Settings - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>

    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1>System Settings</h1>
                <nav class="breadcrumb">
                    <a href="../dashboard.php">Dashboard</a>
                    <span>/</span>
                    <span>Settings</span>
                </nav>
            </div>
        </div>

        <!-- Settings Tab Navigation -->
        <div class="profile-tabs-container">
            <nav class="profile-tabs">
                <a href="index.php" class="profile-tab active">
                    <i class="icon-settings"></i>
                    General
                </a>
                <a href="security.php" class="profile-tab">
                    <i class="icon-shield"></i>
                    Security
                </a>
                <a href="events.php" class="profile-tab">
                    <i class="icon-trophy"></i>
                    Events
                </a>
                <a href="notifications.php" class="profile-tab">
                    <i class="icon-mail"></i>
                    Notifications
                </a>
                <a href="system.php" class="profile-tab">
                    <i class="icon-info"></i>
                    System Info
                </a>
            </nav>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?>">
                <i class="icon-<?php echo $messageType === 'success' ? 'check' : 'warning'; ?>"></i>
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <div class="settings-container">
            <!-- General Settings Panel -->
            <div class="settings-content">
                <div class="settings-panel active" id="general-panel">
                    <div class="panel-header">
                        <h2>General Settings</h2>
                        <p>Configure basic system information and display preferences</p>
                    </div>

                    <form method="POST" class="settings-form">
                        <input type="hidden" name="action" value="update_general">

                        <div class="form-grid">
                            <div class="form-group">
                                <label for="site_name">Site Name</label>
                                <input type="text" id="site_name" name="site_name"
                                       value="<?php echo htmlspecialchars($settings['site_name'] ?? APP_NAME); ?>"
                                       required maxlength="100">
                                <small>The name displayed in the header and browser title</small>
                            </div>

                            <div class="form-group">
                                <label for="site_description">Site Description</label>
                                <textarea id="site_description" name="site_description" rows="3" maxlength="255"><?php echo htmlspecialchars($settings['site_description'] ?? ''); ?></textarea>
                                <small>Brief description for search engines and meta tags</small>
                            </div>

                            <div class="form-group">
                                <label for="contact_email">Contact Email</label>
                                <input type="email" id="contact_email" name="contact_email"
                                       value="<?php echo htmlspecialchars($settings['contact_email'] ?? ''); ?>"
                                       maxlength="100">
                                <small>Primary contact email for the system</small>
                            </div>

                            <div class="form-group">
                                <label for="contact_phone">Contact Phone</label>
                                <input type="tel" id="contact_phone" name="contact_phone"
                                       value="<?php echo htmlspecialchars($settings['contact_phone'] ?? ''); ?>"
                                       maxlength="20">
                                <small>Primary contact phone number</small>
                            </div>

                            <div class="form-group">
                                <label for="timezone">Timezone</label>
                                <select id="timezone" name="timezone">
                                    <?php
                                    $currentTz = $settings['timezone'] ?? date_default_timezone_get();
                                    $timezones = [
                                        'Asia/Manila' => 'Asia/Manila (Philippine Time)',
                                        'UTC' => 'UTC (Coordinated Universal Time)',
                                        'Asia/Singapore' => 'Asia/Singapore',
                                        'Asia/Tokyo' => 'Asia/Tokyo',
                                        'America/New_York' => 'America/New_York (Eastern Time)'
                                    ];
                                    foreach ($timezones as $value => $label): ?>
                                        <option value="<?php echo $value; ?>" <?php echo $currentTz === $value ? 'selected' : ''; ?>>
                                            <?php echo $label; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <small>System timezone for date and time display</small>
                            </div>

                            <div class="form-group">
                                <label for="date_format">Date Format</label>
                                <select id="date_format" name="date_format">
                                    <?php
                                    $currentFormat = $settings['date_format'] ?? 'Y-m-d';
                                    $formats = [
                                        'Y-m-d' => date('Y-m-d') . ' (YYYY-MM-DD)',
                                        'm/d/Y' => date('m/d/Y') . ' (MM/DD/YYYY)',
                                        'd/m/Y' => date('d/m/Y') . ' (DD/MM/YYYY)',
                                        'F j, Y' => date('F j, Y') . ' (Month Day, Year)'
                                    ];
                                    foreach ($formats as $value => $label): ?>
                                        <option value="<?php echo $value; ?>" <?php echo $currentFormat === $value ? 'selected' : ''; ?>>
                                            <?php echo $label; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <small>Default date display format</small>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="icon-save"></i>
                                Save General Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </main>

    <script src="../../assets/js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Settings tab navigation enhancement
            const profileTabs = document.querySelectorAll('.profile-tab');
            profileTabs.forEach(tab => {
                tab.addEventListener('click', function(e) {
                    // Add loading state for better UX
                    if (!this.classList.contains('active')) {
                        this.style.opacity = '0.7';
                        this.innerHTML += ' <i class="icon-spinner" style="animation: spin 1s linear infinite;"></i>';
                    }
                });
            });

            // Add CSS for spinner animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>
