<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Event Creation Wizard
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication and permission
requireAuth();
requirePermission('manage_events');

// Initialize session for wizard data
if (!isset($_SESSION['event_wizard'])) {
    $_SESSION['event_wizard'] = [
        'step' => 1,
        'data' => []
    ];
}

$step = (int)($_GET['step'] ?? $_SESSION['event_wizard']['step']);
$wizardData = $_SESSION['event_wizard']['data'];
$errors = [];
$success = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('Invalid security token');
        }
        
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'next':
                $errors = validateStep($step, $_POST);
                if (empty($errors)) {
                    // Save step data
                    saveStepData($step, $_POST);
                    
                    if ($step < 5) {
                        $step++;
                        $_SESSION['event_wizard']['step'] = $step;
                        header("Location: create.php?step={$step}");
                        exit;
                    }
                }
                break;
                
            case 'previous':
                if ($step > 1) {
                    $step--;
                    $_SESSION['event_wizard']['step'] = $step;
                    header("Location: create.php?step={$step}");
                    exit;
                }
                break;
                
            case 'finish':
                $eventId = createEvent();
                if ($eventId) {
                    // Clear wizard data
                    unset($_SESSION['event_wizard']);
                    logActivity('event_created', "Event created with ID: {$eventId}");
                    redirect('view.php?id=' . $eventId, 'Event created successfully!', 'success');
                }
                break;
        }
    } catch (Exception $e) {
        $errors[] = $e->getMessage();
    }
}

/**
 * Validate step data
 */
function validateStep($step, $data) {
    $errors = [];
    
    switch ($step) {
        case 1: // Basic Information
            if (empty($data['name'])) $errors[] = 'Event name is required';
            if (empty($data['start_date'])) $errors[] = 'Start date is required';
            if (empty($data['end_date'])) $errors[] = 'End date is required';
            
            if (!empty($data['start_date']) && !empty($data['end_date'])) {
                if (strtotime($data['start_date']) > strtotime($data['end_date'])) {
                    $errors[] = 'End date must be after start date';
                }
                if (strtotime($data['start_date']) < strtotime('today')) {
                    $errors[] = 'Start date cannot be in the past';
                }
            }
            break;
            
        case 2: // Sports Selection
            if (empty($data['selected_sports'])) {
                $errors[] = 'Please select at least one sport';
            }
            break;
            
        case 3: // Department Registration
            if (empty($data['selected_departments'])) {
                $errors[] = 'Please select at least two departments';
            } elseif (count($data['selected_departments']) < 2) {
                $errors[] = 'At least two departments are required for competition';
            }
            break;
            
        case 4: // Point System
            if (empty($data['point_system'])) {
                $errors[] = 'Please configure the point system';
            }
            break;
    }
    
    return $errors;
}

/**
 * Save step data to session
 */
function saveStepData($step, $data) {
    switch ($step) {
        case 1:
            $_SESSION['event_wizard']['data']['basic'] = [
                'name' => sanitizeInput($data['name']),
                'start_date' => $data['start_date'],
                'end_date' => $data['end_date'],
                'description' => sanitizeInput($data['description'] ?? ''),
                'status' => 'upcoming'
            ];
            break;
            
        case 2:
            $_SESSION['event_wizard']['data']['sports'] = $data['selected_sports'] ?? [];
            $_SESSION['event_wizard']['data']['sport_configs'] = $data['sport_configs'] ?? [];
            break;
            
        case 3:
            $_SESSION['event_wizard']['data']['departments'] = $data['selected_departments'] ?? [];
            break;
            
        case 4:
            $_SESSION['event_wizard']['data']['point_system'] = $data['point_system'] ?? [];
            break;
    }
}

/**
 * Create the event with all wizard data
 */
function createEvent() {
    $db = getDB();
    
    try {
        $db->beginTransaction();
        
        $wizardData = $_SESSION['event_wizard']['data'];
        $currentUser = getCurrentUser();
        
        // Create event
        $eventData = $wizardData['basic'];
        $eventData['point_system'] = json_encode($wizardData['point_system']);
        $eventData['created_by'] = $currentUser['admin_id'];
        
        $eventId = insertRecord('events', $eventData);
        
        // Add sports to event
        if (!empty($wizardData['sports'])) {
            foreach ($wizardData['sports'] as $sportId) {
                $sportConfig = $wizardData['sport_configs'][$sportId] ?? [];
                insertRecord('event_sports', [
                    'event_id' => $eventId,
                    'sport_id' => $sportId,
                    'max_teams_per_dept' => $sportConfig['max_teams'] ?? 1,
                    'registration_deadline' => $eventData['start_date'],
                    'status' => 'open'
                ]);
            }
        }
        
        // Initialize department standings
        if (!empty($wizardData['departments'])) {
            foreach ($wizardData['departments'] as $deptId) {
                insertRecord('department_standings', [
                    'dept_id' => $deptId,
                    'event_id' => $eventId,
                    'total_points' => 0,
                    'rank_position' => 0
                ]);
            }
        }
        
        $db->commit();
        return $eventId;
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
}

// Get data for current step
$stepData = [];
switch ($step) {
    case 2:
        $stepData['sports'] = fetchAll("SELECT * FROM sports WHERE status = 'active' ORDER BY category, name");
        break;
    case 3:
        $stepData['departments'] = fetchAll("SELECT * FROM departments WHERE status = 'active' ORDER BY name");
        break;
}

$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Event - Step <?php echo $step; ?> - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>
    
    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1>Create New Event</h1>
                <nav class="breadcrumb">
                    <a href="../dashboard.php">Dashboard</a>
                    <span>/</span>
                    <a href="index.php">Events</a>
                    <span>/</span>
                    <span>Create</span>
                </nav>
            </div>
        </div>
        
        <!-- Wizard Progress -->
        <div class="wizard-container">
            <div class="wizard-progress">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: <?php echo ($step / 5) * 100; ?>%"></div>
                </div>
                <div class="wizard-steps">
                    <?php for ($i = 1; $i <= 5; $i++): ?>
                        <div class="wizard-step <?php echo $i <= $step ? 'completed' : ''; ?> <?php echo $i === $step ? 'active' : ''; ?>">
                            <div class="step-number"><?php echo $i; ?></div>
                            <div class="step-title">
                                <?php 
                                $stepTitles = [
                                    1 => 'Basic Info',
                                    2 => 'Sports',
                                    3 => 'Departments',
                                    4 => 'Point System',
                                    5 => 'Confirmation'
                                ];
                                echo $stepTitles[$i];
                                ?>
                            </div>
                        </div>
                    <?php endfor; ?>
                </div>
            </div>
            
            <?php if (!empty($errors)): ?>
                <div class="alert alert-error">
                    <ul>
                        <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success"><?php echo htmlspecialchars($success); ?></div>
            <?php endif; ?>
            
            <!-- Step Content -->
            <div class="wizard-content">
                <form method="POST" class="wizard-form">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                    
                    <?php if ($step === 1): ?>
                        <!-- Step 1: Basic Information -->
                        <div class="step-content">
                            <h2>Basic Event Information</h2>
                            <p>Enter the basic details for your intramural event.</p>
                            
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="name">Event Name *</label>
                                    <input type="text" id="name" name="name" 
                                           value="<?php echo htmlspecialchars($wizardData['basic']['name'] ?? ''); ?>" 
                                           required class="form-control">
                                </div>
                                
                                <div class="form-group">
                                    <label for="start_date">Start Date *</label>
                                    <input type="date" id="start_date" name="start_date" 
                                           value="<?php echo $wizardData['basic']['start_date'] ?? ''; ?>" 
                                           required class="form-control" min="<?php echo date('Y-m-d'); ?>">
                                </div>
                                
                                <div class="form-group">
                                    <label for="end_date">End Date *</label>
                                    <input type="date" id="end_date" name="end_date" 
                                           value="<?php echo $wizardData['basic']['end_date'] ?? ''; ?>" 
                                           required class="form-control" min="<?php echo date('Y-m-d'); ?>">
                                </div>
                                
                                <div class="form-group full-width">
                                    <label for="description">Description</label>
                                    <textarea id="description" name="description" rows="4" 
                                              class="form-control" placeholder="Enter event description..."><?php echo htmlspecialchars($wizardData['basic']['description'] ?? ''); ?></textarea>
                                </div>
                            </div>
                        </div>
                        
                    <?php elseif ($step === 2): ?>
                        <!-- Step 2: Sports Selection -->
                        <div class="step-content">
                            <h2>Select Sports</h2>
                            <p>Choose the sports that will be included in this event.</p>
                            
                            <div class="sports-selection">
                                <?php 
                                $sportsByCategory = [];
                                foreach ($stepData['sports'] as $sport) {
                                    $sportsByCategory[$sport['category']][] = $sport;
                                }
                                ?>
                                
                                <?php foreach ($sportsByCategory as $category => $sports): ?>
                                    <div class="sport-category">
                                        <h3><?php echo ucfirst(str_replace('_', ' ', $category)); ?> Sports</h3>
                                        <div class="sports-grid">
                                            <?php foreach ($sports as $sport): ?>
                                                <div class="sport-card">
                                                    <label class="sport-checkbox">
                                                        <input type="checkbox" name="selected_sports[]" 
                                                               value="<?php echo $sport['sport_id']; ?>"
                                                               <?php echo in_array($sport['sport_id'], $wizardData['sports'] ?? []) ? 'checked' : ''; ?>>
                                                        <div class="sport-info">
                                                            <h4><?php echo htmlspecialchars($sport['name']); ?></h4>
                                                            <p><?php echo htmlspecialchars($sport['description'] ?? ''); ?></p>
                                                            <div class="sport-meta">
                                                                <span class="sport-type"><?php echo ucfirst($sport['scoring_type']); ?></span>
                                                                <span class="sport-participants">
                                                                    <?php echo $sport['min_participants']; ?>-<?php echo $sport['max_participants']; ?> players
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </label>
                                                    
                                                    <div class="sport-config" style="display: none;">
                                                        <label>Max teams per department:</label>
                                                        <input type="number" name="sport_configs[<?php echo $sport['sport_id']; ?>][max_teams]" 
                                                               value="<?php echo $wizardData['sport_configs'][$sport['sport_id']]['max_teams'] ?? 1; ?>" 
                                                               min="1" max="5" class="form-control">
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        
                    <?php elseif ($step === 3): ?>
                        <!-- Step 3: Department Registration -->
                        <div class="step-content">
                            <h2>Select Participating Departments</h2>
                            <p>Choose which departments will participate in this event.</p>
                            
                            <div class="departments-selection">
                                <div class="select-all-container">
                                    <label class="checkbox-container">
                                        <input type="checkbox" id="selectAllDepts">
                                        <span class="checkmark"></span>
                                        Select All Departments
                                    </label>
                                </div>
                                
                                <div class="departments-grid">
                                    <?php foreach ($stepData['departments'] as $dept): ?>
                                        <div class="department-card">
                                            <label class="department-checkbox">
                                                <input type="checkbox" name="selected_departments[]" 
                                                       value="<?php echo $dept['dept_id']; ?>"
                                                       <?php echo in_array($dept['dept_id'], $wizardData['departments'] ?? []) ? 'checked' : ''; ?>>
                                                <div class="department-info">
                                                    <div class="dept-color" style="background-color: <?php echo htmlspecialchars($dept['color_code']); ?>"></div>
                                                    <div class="dept-details">
                                                        <h4><?php echo htmlspecialchars($dept['abbreviation']); ?></h4>
                                                        <p><?php echo htmlspecialchars($dept['name']); ?></p>
                                                        <?php if ($dept['contact_person']): ?>
                                                            <small>Contact: <?php echo htmlspecialchars($dept['contact_person']); ?></small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </label>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                        
                    <?php elseif ($step === 4): ?>
                        <!-- Step 4: Point System Configuration -->
                        <div class="step-content">
                            <h2>Configure Point System</h2>
                            <p>Set up how points will be awarded for different positions.</p>
                            
                            <div class="point-system-config">
                                <div class="preset-systems">
                                    <h3>Choose a Preset or Customize</h3>
                                    <div class="preset-buttons">
                                        <button type="button" class="btn btn-outline" onclick="loadPreset('standard')">
                                            Standard (15-12-10-8-6-3)
                                        </button>
                                        <button type="button" class="btn btn-outline" onclick="loadPreset('olympic')">
                                            Olympic (10-8-6-4-2-1)
                                        </button>
                                        <button type="button" class="btn btn-outline" onclick="loadPreset('simple')">
                                            Simple (5-3-1)
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="point-inputs">
                                    <div class="form-grid">
                                        <div class="form-group">
                                            <label for="points_1st">1st Place Points</label>
                                            <input type="number" id="points_1st" name="point_system[1st]" 
                                                   value="<?php echo $wizardData['point_system']['1st'] ?? 15; ?>" 
                                                   min="1" class="form-control">
                                        </div>
                                        <div class="form-group">
                                            <label for="points_2nd">2nd Place Points</label>
                                            <input type="number" id="points_2nd" name="point_system[2nd]" 
                                                   value="<?php echo $wizardData['point_system']['2nd'] ?? 12; ?>" 
                                                   min="1" class="form-control">
                                        </div>
                                        <div class="form-group">
                                            <label for="points_3rd">3rd Place Points</label>
                                            <input type="number" id="points_3rd" name="point_system[3rd]" 
                                                   value="<?php echo $wizardData['point_system']['3rd'] ?? 10; ?>" 
                                                   min="1" class="form-control">
                                        </div>
                                        <div class="form-group">
                                            <label for="points_4th">4th Place Points</label>
                                            <input type="number" id="points_4th" name="point_system[4th]" 
                                                   value="<?php echo $wizardData['point_system']['4th'] ?? 8; ?>" 
                                                   min="0" class="form-control">
                                        </div>
                                        <div class="form-group">
                                            <label for="points_5th">5th Place Points</label>
                                            <input type="number" id="points_5th" name="point_system[5th]" 
                                                   value="<?php echo $wizardData['point_system']['5th'] ?? 6; ?>" 
                                                   min="0" class="form-control">
                                        </div>
                                        <div class="form-group">
                                            <label for="points_participation">Participation Points</label>
                                            <input type="number" id="points_participation" name="point_system[participation]" 
                                                   value="<?php echo $wizardData['point_system']['participation'] ?? 3; ?>" 
                                                   min="0" class="form-control">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                    <?php elseif ($step === 5): ?>
                        <!-- Step 5: Confirmation -->
                        <div class="step-content">
                            <h2>Confirm Event Creation</h2>
                            <p>Review your event details before creating the event.</p>
                            
                            <div class="confirmation-summary">
                                <div class="summary-section">
                                    <h3>Basic Information</h3>
                                    <div class="summary-item">
                                        <strong>Event Name:</strong> <?php echo htmlspecialchars($wizardData['basic']['name'] ?? ''); ?>
                                    </div>
                                    <div class="summary-item">
                                        <strong>Dates:</strong> 
                                        <?php echo formatDate($wizardData['basic']['start_date'] ?? ''); ?> - 
                                        <?php echo formatDate($wizardData['basic']['end_date'] ?? ''); ?>
                                    </div>
                                    <?php if (!empty($wizardData['basic']['description'])): ?>
                                        <div class="summary-item">
                                            <strong>Description:</strong> <?php echo htmlspecialchars($wizardData['basic']['description']); ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="summary-section">
                                    <h3>Selected Sports (<?php echo count($wizardData['sports'] ?? []); ?>)</h3>
                                    <div class="summary-list">
                                        <?php 
                                        if (!empty($wizardData['sports'])) {
                                            $selectedSports = fetchAll("SELECT name FROM sports WHERE sport_id IN (" . implode(',', array_map('intval', $wizardData['sports'])) . ")");
                                            foreach ($selectedSports as $sport) {
                                                echo '<span class="summary-tag">' . htmlspecialchars($sport['name']) . '</span>';
                                            }
                                        }
                                        ?>
                                    </div>
                                </div>
                                
                                <div class="summary-section">
                                    <h3>Participating Departments (<?php echo count($wizardData['departments'] ?? []); ?>)</h3>
                                    <div class="summary-list">
                                        <?php 
                                        if (!empty($wizardData['departments'])) {
                                            $selectedDepts = fetchAll("SELECT abbreviation, color_code FROM departments WHERE dept_id IN (" . implode(',', array_map('intval', $wizardData['departments'])) . ")");
                                            foreach ($selectedDepts as $dept) {
                                                echo '<span class="summary-tag dept-tag" style="border-color: ' . htmlspecialchars($dept['color_code']) . '">' . htmlspecialchars($dept['abbreviation']) . '</span>';
                                            }
                                        }
                                        ?>
                                    </div>
                                </div>
                                
                                <div class="summary-section">
                                    <h3>Point System</h3>
                                    <div class="point-summary">
                                        <?php foreach ($wizardData['point_system'] ?? [] as $position => $points): ?>
                                            <div class="point-item">
                                                <span class="position"><?php echo ucfirst($position); ?>:</span>
                                                <span class="points"><?php echo $points; ?> points</span>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Wizard Navigation -->
                    <div class="wizard-navigation">
                        <?php if ($step > 1): ?>
                            <button type="submit" name="action" value="previous" class="btn btn-secondary">
                                <i class="icon-arrow-left"></i>
                                Previous
                            </button>
                        <?php endif; ?>
                        
                        <div class="nav-spacer"></div>
                        
                        <?php if ($step < 5): ?>
                            <button type="submit" name="action" value="next" class="btn btn-primary">
                                Next
                                <i class="icon-arrow-right"></i>
                            </button>
                        <?php else: ?>
                            <button type="submit" name="action" value="finish" class="btn btn-success">
                                <i class="icon-check"></i>
                                Create Event
                            </button>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
    </main>
    
    <script src="../../assets/js/admin.js"></script>
    <script>
        // Step 2: Sports selection logic
        document.addEventListener('DOMContentLoaded', function() {
            const sportCheckboxes = document.querySelectorAll('input[name="selected_sports[]"]');
            sportCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const sportCard = this.closest('.sport-card');
                    const configDiv = sportCard.querySelector('.sport-config');
                    if (this.checked) {
                        configDiv.style.display = 'block';
                    } else {
                        configDiv.style.display = 'none';
                    }
                });
                
                // Initialize on page load
                if (checkbox.checked) {
                    const sportCard = checkbox.closest('.sport-card');
                    const configDiv = sportCard.querySelector('.sport-config');
                    configDiv.style.display = 'block';
                }
            });
            
            // Step 3: Select all departments
            const selectAllDepts = document.getElementById('selectAllDepts');
            if (selectAllDepts) {
                selectAllDepts.addEventListener('change', function() {
                    const deptCheckboxes = document.querySelectorAll('input[name="selected_departments[]"]');
                    deptCheckboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                });
            }
        });
        
        // Step 4: Point system presets
        function loadPreset(type) {
            const presets = {
                standard: { '1st': 15, '2nd': 12, '3rd': 10, '4th': 8, '5th': 6, 'participation': 3 },
                olympic: { '1st': 10, '2nd': 8, '3rd': 6, '4th': 4, '5th': 2, 'participation': 1 },
                simple: { '1st': 5, '2nd': 3, '3rd': 1, '4th': 0, '5th': 0, 'participation': 0 }
            };
            
            const preset = presets[type];
            if (preset) {
                Object.keys(preset).forEach(position => {
                    const input = document.getElementById('points_' + position);
                    if (input) {
                        input.value = preset[position];
                    }
                });
            }
        }
        
        // Date validation
        document.getElementById('start_date')?.addEventListener('change', function() {
            const endDateInput = document.getElementById('end_date');
            endDateInput.min = this.value;
            if (endDateInput.value && endDateInput.value < this.value) {
                endDateInput.value = this.value;
            }
        });
    </script>
</body>
</html>
