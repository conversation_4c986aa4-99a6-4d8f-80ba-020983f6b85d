<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tournament Config Validation</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { padding: 10px; margin: 5px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        h1 { color: #333; }
        h2 { color: #666; margin-top: 30px; }
        .test-section { margin: 20px 0; }
    </style>
</head>
<body>
    <h1>🏆 Tournament Configuration Validation Report</h1>
    <p><strong>Test Date:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>

    <?php
    // Start output buffering to catch any errors
    ob_start();
    error_reporting(E_ALL);
    ini_set('display_errors', 1);

    $testResults = [];
    $overallSuccess = true;

    function addTestResult($test, $status, $message, $details = '') {
        global $testResults, $overallSuccess;
        $testResults[] = [
            'test' => $test,
            'status' => $status,
            'message' => $message,
            'details' => $details
        ];
        if ($status === 'error') {
            $overallSuccess = false;
        }
    }

    // Test 1: File Existence
    echo "<div class='test-section'><h2>📁 File Existence Tests</h2>";
    
    $requiredFiles = [
        'admin/events/tournament_config.php',
        'includes/config.php',
        'includes/functions.php',
        'admin/includes/header.php'
    ];

    foreach ($requiredFiles as $file) {
        if (file_exists($file)) {
            addTestResult("File: $file", 'success', 'File exists');
            echo "<div class='test-result success'>✅ $file exists</div>";
        } else {
            addTestResult("File: $file", 'error', 'File missing');
            echo "<div class='test-result error'>❌ $file missing</div>";
        }
    }
    echo "</div>";

    // Test 2: PHP Syntax Check
    echo "<div class='test-section'><h2>🔍 PHP Syntax Tests</h2>";
    
    $syntaxCheck = shell_exec('php -l admin/events/tournament_config.php 2>&1');
    if (strpos($syntaxCheck, 'No syntax errors') !== false) {
        addTestResult('PHP Syntax', 'success', 'No syntax errors detected');
        echo "<div class='test-result success'>✅ PHP syntax is valid</div>";
    } else {
        addTestResult('PHP Syntax', 'error', 'Syntax errors found', $syntaxCheck);
        echo "<div class='test-result error'>❌ PHP syntax errors: " . htmlspecialchars($syntaxCheck) . "</div>";
    }
    echo "</div>";

    // Test 3: Database Connection
    echo "<div class='test-section'><h2>🗄️ Database Tests</h2>";
    
    try {
        require_once 'includes/config.php';
        $db = getDB();
        if ($db) {
            addTestResult('Database Connection', 'success', 'Connected successfully');
            echo "<div class='test-result success'>✅ Database connection successful</div>";
            
            // Test required functions
            $functions = ['fetchOne', 'fetchAll', 'executeQuery', 'tableExists'];
            foreach ($functions as $func) {
                if (function_exists($func)) {
                    addTestResult("Function: $func", 'success', 'Function exists');
                    echo "<div class='test-result success'>✅ Function '$func' available</div>";
                } else {
                    addTestResult("Function: $func", 'error', 'Function missing');
                    echo "<div class='test-result error'>❌ Function '$func' missing</div>";
                }
            }
            
        } else {
            addTestResult('Database Connection', 'error', 'Connection failed');
            echo "<div class='test-result error'>❌ Database connection failed</div>";
        }
    } catch (Exception $e) {
        addTestResult('Database Connection', 'error', 'Exception: ' . $e->getMessage());
        echo "<div class='test-result error'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    echo "</div>";

    // Test 4: Required Functions Check
    echo "<div class='test-section'><h2>⚙️ Function Tests</h2>";
    
    // Check if we can include the tournament config file safely
    try {
        // Set up minimal environment
        session_start();
        $_SESSION['admin_id'] = 1;
        $_SESSION['admin_username'] = 'test';
        $_GET['event_sport_id'] = 1;
        $_GET['event_id'] = 1;
        
        // Test if we can parse the file without executing it fully
        $fileContent = file_get_contents('admin/events/tournament_config.php');
        
        // Check for required function definitions
        $requiredFunctions = [
            'renderProfessionalBracketVisualization',
            'renderProfessionalSingleElimination', 
            'renderProfessionalDoubleElimination',
            'renderProfessionalRoundRobin',
            'renderProfessionalMatchCard',
            'getRoundName'
        ];
        
        foreach ($requiredFunctions as $func) {
            if (strpos($fileContent, "function $func") !== false) {
                addTestResult("Function Definition: $func", 'success', 'Function defined');
                echo "<div class='test-result success'>✅ Function '$func' is defined</div>";
            } else {
                addTestResult("Function Definition: $func", 'error', 'Function not defined');
                echo "<div class='test-result error'>❌ Function '$func' not found</div>";
            }
        }
        
    } catch (Exception $e) {
        addTestResult('Function Check', 'error', 'Exception: ' . $e->getMessage());
        echo "<div class='test-result error'>❌ Function check failed: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
    echo "</div>";

    // Test 5: JavaScript Validation
    echo "<div class='test-section'><h2>📜 JavaScript Tests</h2>";
    
    $jsContent = file_get_contents('admin/events/tournament_config.php');
    $requiredJsFunctions = [
        'openFormatModal',
        'closeFormatModal', 
        'selectModalFormat',
        'confirmFormatSelection',
        'regenerateBracket'
    ];
    
    foreach ($requiredJsFunctions as $jsFunc) {
        if (strpos($jsContent, "function $jsFunc") !== false) {
            addTestResult("JS Function: $jsFunc", 'success', 'JavaScript function defined');
            echo "<div class='test-result success'>✅ JavaScript function '$jsFunc' is defined</div>";
        } else {
            addTestResult("JS Function: $jsFunc", 'error', 'JavaScript function not defined');
            echo "<div class='test-result error'>❌ JavaScript function '$jsFunc' not found</div>";
        }
    }
    echo "</div>";

    // Summary
    echo "<div class='test-section'><h2>📊 Test Summary</h2>";
    
    $successCount = count(array_filter($testResults, function($r) { return $r['status'] === 'success'; }));
    $errorCount = count(array_filter($testResults, function($r) { return $r['status'] === 'error'; }));
    $totalTests = count($testResults);
    
    if ($overallSuccess) {
        echo "<div class='test-result success'>";
        echo "<h3>🎉 All Tests Passed!</h3>";
        echo "<p>The tournament configuration page should now work correctly.</p>";
        echo "<p><strong>Results:</strong> $successCount/$totalTests tests passed</p>";
        echo "</div>";
    } else {
        echo "<div class='test-result error'>";
        echo "<h3>⚠️ Some Tests Failed</h3>";
        echo "<p>There are still issues that need to be resolved.</p>";
        echo "<p><strong>Results:</strong> $successCount/$totalTests tests passed, $errorCount failed</p>";
        echo "</div>";
    }
    
    echo "<div class='test-result info'>";
    echo "<h4>🔗 Next Steps:</h4>";
    echo "<ul>";
    echo "<li>Visit the tournament config page: <a href='admin/events/tournament_config.php?event_sport_id=1&event_id=1' target='_blank'>Open Tournament Config</a></li>";
    echo "<li>Test the modal-based format selection</li>";
    echo "<li>Test bracket generation and visualization</li>";
    echo "<li>Verify responsive design on different screen sizes</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "</div>";

    // Capture any output buffer content
    $output = ob_get_clean();
    echo $output;
    ?>
</body>
</html>
