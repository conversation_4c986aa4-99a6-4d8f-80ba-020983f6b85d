<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Events Management - Main Page
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication and permission
requireAuth();
requirePermission('manage_events');

// Handle actions
$action = $_GET['action'] ?? '';
$message = '';
$messageType = 'info';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('Invalid security token');
        }
        
        switch ($action) {
            case 'delete':
                $eventId = (int)($_POST['event_id'] ?? 0);
                if ($eventId) {
                    // Check if event has matches
                    $matchCount = fetchOne("SELECT COUNT(*) as count FROM matches WHERE event_id = ?", [$eventId])['count'];
                    if ($matchCount > 0) {
                        throw new Exception('Cannot delete event with existing matches');
                    }
                    
                    deleteRecord('events', 'event_id = :event_id', ['event_id' => $eventId]);
                    logActivity('event_deleted', "Event ID {$eventId} deleted");
                    $message = 'Event deleted successfully';
                    $messageType = 'success';
                }
                break;
                
            case 'toggle_status':
                $eventId = (int)($_POST['event_id'] ?? 0);
                $newStatus = sanitizeInput($_POST['new_status'] ?? '');
                
                if ($eventId && in_array($newStatus, ['upcoming', 'ongoing', 'completed'])) {
                    updateRecord('events', ['status' => $newStatus], 'event_id = :event_id', ['event_id' => $eventId]);
                    logActivity('event_status_changed', "Event ID {$eventId} status changed to {$newStatus}");
                    $message = 'Event status updated successfully';
                    $messageType = 'success';
                }
                break;
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get events with pagination
$page = (int)($_GET['page'] ?? 1);
$search = sanitizeInput($_GET['search'] ?? '');
$statusFilter = sanitizeInput($_GET['status'] ?? '');

$whereConditions = [];
$params = [];

if ($search) {
    $whereConditions[] = "(name LIKE ? OR description LIKE ?)";
    $params[] = "%{$search}%";
    $params[] = "%{$search}%";
}

if ($statusFilter) {
    $whereConditions[] = "status = ?";
    $params[] = $statusFilter;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// Count total records
$totalRecords = fetchOne("SELECT COUNT(*) as count FROM events {$whereClause}", $params)['count'];
$pagination = paginate($totalRecords, $page);

// Get events
$events = fetchAll("
    SELECT e.*, 
           au.full_name as created_by_name,
           (SELECT COUNT(*) FROM matches WHERE event_id = e.event_id) as match_count,
           (SELECT COUNT(*) FROM department_standings WHERE event_id = e.event_id) as dept_count
    FROM events e
    LEFT JOIN admin_users au ON e.created_by = au.admin_id
    {$whereClause}
    ORDER BY e.start_date DESC
    LIMIT {$pagination['records_per_page']} OFFSET {$pagination['offset']}
", $params);

$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Events Management - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>
    
    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1>Events Management</h1>
                <nav class="breadcrumb">
                    <a href="../dashboard.php">Dashboard</a>
                    <span>/</span>
                    <span>Events</span>
                </nav>
            </div>
            <div class="page-actions">
                <a href="create.php" class="btn btn-primary">
                    <i class="icon-plus"></i>
                    Create New Event
                </a>
            </div>
        </div>
        
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <!-- Filters -->
        <div class="filters-container">
            <form method="GET" class="filters-form">
                <div class="filter-group">
                    <input type="text" name="search" placeholder="Search events..." 
                           value="<?php echo htmlspecialchars($search); ?>" class="form-control">
                </div>
                
                <div class="filter-group">
                    <select name="status" class="form-control">
                        <option value="">All Status</option>
                        <option value="upcoming" <?php echo $statusFilter === 'upcoming' ? 'selected' : ''; ?>>Upcoming</option>
                        <option value="ongoing" <?php echo $statusFilter === 'ongoing' ? 'selected' : ''; ?>>Ongoing</option>
                        <option value="completed" <?php echo $statusFilter === 'completed' ? 'selected' : ''; ?>>Completed</option>
                    </select>
                </div>
                
                <div class="filter-actions">
                    <button type="submit" class="btn btn-secondary">Filter</button>
                    <a href="index.php" class="btn btn-outline">Clear</a>
                </div>
            </form>
        </div>
        
        <!-- Events Table -->
        <div class="table-container">
            <?php if (!empty($events)): ?>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Event Name</th>
                            <th>Dates</th>
                            <th>Status</th>
                            <th>Matches</th>
                            <th>Departments</th>
                            <th>Created By</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($events as $event): ?>
                            <tr>
                                <td>
                                    <div class="event-info">
                                        <h4><a href="details.php?id=<?php echo $event['event_id']; ?>" class="event-name-link"><?php echo htmlspecialchars($event['name']); ?></a></h4>
                                        <?php if ($event['description']): ?>
                                            <p class="event-description"><?php echo htmlspecialchars(substr($event['description'], 0, 100)); ?>...</p>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="event-dates">
                                        <div><?php echo formatDate($event['start_date']); ?></div>
                                        <div>to <?php echo formatDate($event['end_date']); ?></div>
                                    </div>
                                </td>
                                <td>
                                    <span class="status-badge status-<?php echo $event['status']; ?>">
                                        <?php echo ucfirst($event['status']); ?>
                                    </span>
                                </td>
                                <td class="text-center"><?php echo $event['match_count']; ?></td>
                                <td class="text-center"><?php echo $event['dept_count']; ?></td>
                                <td><?php echo htmlspecialchars($event['created_by_name'] ?? 'System'); ?></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-primary"
                                                onclick="editEvent(<?php echo $event['event_id']; ?>)"
                                                title="Edit Event">
                                            <i class="icon-edit"></i>
                                            Edit
                                        </button>

                                        <!-- Status Toggle -->
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-secondary dropdown-toggle"
                                                    data-toggle="dropdown" title="Change Status">
                                                <i class="icon-settings"></i>
                                                <span>Status</span>
                                            </button>
                                            <div class="dropdown-menu">
                                                <?php foreach (['upcoming', 'ongoing', 'completed'] as $status): ?>
                                                    <?php if ($status !== $event['status']): ?>
                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                                            <input type="hidden" name="action" value="toggle_status">
                                                            <input type="hidden" name="event_id" value="<?php echo $event['event_id']; ?>">
                                                            <input type="hidden" name="new_status" value="<?php echo $status; ?>">
                                                            <button type="submit" class="dropdown-item">
                                                                <i class="icon-check"></i>
                                                                Mark as <?php echo ucfirst($status); ?>
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>

                                        <?php if ($event['match_count'] == 0): ?>
                                            <button class="btn btn-sm btn-danger"
                                                    onclick="deleteEvent(<?php echo $event['event_id']; ?>, '<?php echo htmlspecialchars($event['name']); ?>')"
                                                    title="Delete Event">
                                                <i class="icon-trash"></i>
                                                <span>Delete</span>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                
                <!-- Pagination -->
                <?php echo generatePaginationHTML($pagination, 'index.php'); ?>
                
            <?php else: ?>
                <div class="no-data">
                    <h3>No events found</h3>
                    <p>Start by creating your first event.</p>
                    <a href="create.php" class="btn btn-primary">Create New Event</a>
                </div>
            <?php endif; ?>
        </div>
    </main>
    
    <!-- View Event Modal -->
    <div id="viewModal" class="modal">
        <div class="modal-overlay">
            <div class="modal-content modal-lg">
                <div class="modal-header">
                    <h3><i class="icon-eye"></i> Event Details</h3>
                    <button class="modal-close" onclick="closeModal('viewModal')">&times;</button>
                </div>
                <div class="modal-body">
                    <div id="viewEventContent">
                        <div class="loading">Loading event details...</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('viewModal')">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Event Modal -->
    <div id="editModal" class="modal">
        <div class="modal-overlay">
            <div class="modal-content modal-lg">
                <div class="modal-header">
                    <h3><i class="icon-edit"></i> Edit Event</h3>
                    <button class="modal-close" onclick="closeModal('editModal')">&times;</button>
                </div>
                <div class="modal-body">
                    <div id="editEventContent">
                        <div class="loading">Loading event form...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal">
        <div class="modal-overlay">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="icon-trash"></i> Confirm Delete</h3>
                    <button class="modal-close" onclick="closeModal('deleteModal')">&times;</button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the event "<span id="eventName"></span>"?</p>
                    <p class="text-warning"><i class="icon-warning"></i> This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <form id="deleteForm" method="POST">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="event_id" id="deleteEventId">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('deleteModal')">
                            <i class="icon-cancel"></i> Cancel
                        </button>
                        <button type="submit" class="btn btn-danger">
                            <i class="icon-trash"></i> Delete Event
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <script src="../../assets/js/admin.js"></script>
    <script>
        // View Event Function
        function viewEvent(eventId) {
            const content = document.getElementById('viewEventContent');
            content.innerHTML = '<div class="loading"><i class="icon-spinner"></i> Loading event details...</div>';
            openModal('viewModal');

            // Fetch event details
            fetch(`view_ajax.php?id=${eventId}`)
                .then(response => response.text())
                .then(html => {
                    content.innerHTML = html;
                })
                .catch(error => {
                    content.innerHTML = '<div class="alert alert-error">Error loading event details. Please try again.</div>';
                });
        }

        // Edit Event Function
        function editEvent(eventId) {
            const content = document.getElementById('editEventContent');
            content.innerHTML = '<div class="loading"><i class="icon-spinner"></i> Loading event form...</div>';
            openModal('editModal');

            // Fetch edit form
            fetch(`edit_ajax.php?id=${eventId}`)
                .then(response => response.text())
                .then(html => {
                    content.innerHTML = html;
                    // Initialize form validation and handlers
                    initializeEditForm();
                })
                .catch(error => {
                    content.innerHTML = '<div class="alert alert-error">Error loading edit form. Please try again.</div>';
                });
        }

        // Initialize Edit Form
        function initializeEditForm() {
            const form = document.getElementById('editEventForm');
            if (form) {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const formData = new FormData(form);
                    const submitBtn = form.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;

                    submitBtn.innerHTML = '<i class="icon-spinner"></i> Saving...';
                    submitBtn.disabled = true;

                    fetch('edit_ajax.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            closeModal('editModal');
                            location.reload(); // Refresh the page to show updated data
                        } else {
                            // Show error message
                            const errorDiv = document.getElementById('editFormErrors');
                            if (errorDiv) {
                                errorDiv.innerHTML = `<div class="alert alert-error">${data.message}</div>`;
                            }
                        }
                    })
                    .catch(error => {
                        const errorDiv = document.getElementById('editFormErrors');
                        if (errorDiv) {
                            errorDiv.innerHTML = '<div class="alert alert-error">Error saving event. Please try again.</div>';
                        }
                    })
                    .finally(() => {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    });
                });
            }
        }

        // Delete Event Function
        function deleteEvent(eventId, eventName) {
            document.getElementById('eventName').textContent = eventName;
            document.getElementById('deleteEventId').value = eventId;
            openModal('deleteModal');
        }

        // Modal Functions
        function openModal(modalId) {
            document.getElementById(modalId).classList.add('show');
            document.body.style.overflow = 'hidden';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('show');
            document.body.style.overflow = '';
        }

        // Initialize dropdowns and other functionality
        document.addEventListener('DOMContentLoaded', function() {
            const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
            dropdownToggles.forEach(toggle => {
                toggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    const dropdown = this.parentElement;
                    dropdown.classList.toggle('show');
                });
            });

            // Close dropdowns when clicking outside
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.dropdown')) {
                    document.querySelectorAll('.dropdown').forEach(dropdown => {
                        dropdown.classList.remove('show');
                    });
                }
            });

            // Close modals when clicking overlay
            document.querySelectorAll('.modal-overlay').forEach(overlay => {
                overlay.addEventListener('click', function(e) {
                    if (e.target === this) {
                        const modal = this.closest('.modal');
                        if (modal) {
                            closeModal(modal.id);
                        }
                    }
                });
            });

            // Add spinner animation CSS
            const style = document.createElement('style');
            style.textContent = `
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
                .icon-spinner {
                    animation: spin 1s linear infinite;
                }
                .loading {
                    text-align: center;
                    padding: 2rem;
                    color: var(--gray-600);
                }
                .modal-lg {
                    max-width: 800px;
                }
            `;
            document.head.appendChild(style);

            // Add CSS for event name links
            const linkStyle = document.createElement('style');
            linkStyle.textContent = `
                .event-name-link {
                    color: var(--primary-600);
                    text-decoration: none;
                    font-weight: 600;
                    transition: color 0.2s ease;
                }

                .event-name-link:hover {
                    color: var(--primary-700);
                    text-decoration: underline;
                }

                .event-name-link:focus {
                    outline: 2px solid var(--primary-500);
                    outline-offset: 2px;
                    border-radius: 2px;
                }

                .event-info h4 {
                    margin: 0 0 0.5rem 0;
                    font-size: 1.1rem;
                }

                /* Ensure button text is visible */
                .btn {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.5rem 1rem;
                    border: none;
                    border-radius: 4px;
                    font-size: 0.875rem;
                    font-weight: 500;
                    text-decoration: none;
                    cursor: pointer;
                    transition: all 0.2s;
                    white-space: nowrap;
                }

                .btn-primary {
                    background-color: #3b82f6;
                    color: white;
                }

                .btn-primary:hover {
                    background-color: #2563eb;
                }

                .btn-secondary {
                    background-color: #f3f4f6;
                    color: #374151;
                    border: 1px solid #d1d5db;
                }

                .btn-secondary:hover {
                    background-color: #e5e7eb;
                    color: #1f2937;
                }

                .btn-sm {
                    padding: 0.375rem 0.75rem;
                    font-size: 0.8125rem;
                }

                .action-buttons {
                    display: flex;
                    gap: 0.5rem;
                    align-items: center;
                }
            `;
            document.head.appendChild(linkStyle);
        });
    </script>
</body>
</html>
