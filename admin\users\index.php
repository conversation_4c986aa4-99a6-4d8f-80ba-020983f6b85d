<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Admin Users Management - Coming Soon
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication
requireAuth();

// Only super admin can access this
if (getCurrentUser()['role'] !== 'super_admin') {
    header('Location: ../dashboard.php?message=Access denied&type=error');
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Users Management - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>
    
    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1>Admin Users Management</h1>
                <nav class="breadcrumb">
                    <a href="../dashboard.php">Dashboard</a>
                    <span>/</span>
                    <span>Admin Users</span>
                </nav>
            </div>
        </div>
        
        <div class="coming-soon">
            <div class="coming-soon-content">
                <h2>👨‍💼 Admin Users Management</h2>
                <p>This module is currently under development and will be available in a future update.</p>
                
                <div class="planned-features">
                    <h3>Planned Features:</h3>
                    <ul>
                        <li>✅ User account creation and management</li>
                        <li>✅ Role-based access control</li>
                        <li>✅ Permission management</li>
                        <li>✅ Activity monitoring</li>
                        <li>✅ Password policies</li>
                        <li>✅ Session management</li>
                    </ul>
                </div>
                
                <div class="current-user-info">
                    <h3>Current User Information:</h3>
                    <div class="user-card">
                        <div class="user-avatar">
                            <?php echo strtoupper(substr(getCurrentUser()['full_name'], 0, 1)); ?>
                        </div>
                        <div class="user-details">
                            <h4><?php echo htmlspecialchars(getCurrentUser()['full_name']); ?></h4>
                            <p>Username: <?php echo htmlspecialchars(getCurrentUser()['username']); ?></p>
                            <p>Role: <?php echo ucfirst(getCurrentUser()['role']); ?></p>
                            <p>Last Login: <?php echo formatDate(getCurrentUser()['last_login']); ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="alternative-actions">
                    <h3>Available Actions:</h3>
                    <div class="action-cards">
                        <a href="../settings/" class="action-card">
                            <i class="icon-settings"></i>
                            <h4>System Settings</h4>
                            <p>Configure system preferences</p>
                        </a>
                        <a href="../logs/" class="action-card">
                            <i class="icon-file"></i>
                            <h4>Activity Logs</h4>
                            <p>View system activity logs</p>
                        </a>
                        <a href="../backup/" class="action-card">
                            <i class="icon-database"></i>
                            <h4>Backup System</h4>
                            <p>Backup and restore data</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <script src="../../assets/js/admin.js"></script>
</body>
</html>
