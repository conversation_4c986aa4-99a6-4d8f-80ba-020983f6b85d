<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Tournament Bracket Viewer
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication and permission
requireAuth();
requirePermission('manage_events');

$eventSportId = (int)($_GET['event_sport_id'] ?? 0);
$eventId = (int)($_GET['event_id'] ?? 0);

if (!$eventSportId || !$eventId) {
    header('Location: details.php?id=' . $eventId . '&message=Invalid parameters&type=error');
    exit;
}

// Get event sport details
$eventSport = fetchOne("
    SELECT es.*, s.name as sport_name, s.category as sport_category,
           e.name as event_name, e.status as event_status
    FROM event_sports es
    JOIN sports s ON es.sport_id = s.sport_id
    JOIN events e ON es.event_id = e.event_id
    WHERE es.event_sport_id = ? AND es.event_id = ?
", [$eventSportId, $eventId]);

if (!$eventSport) {
    header('Location: details.php?id=' . $eventId . '&message=Event sport not found&type=error');
    exit;
}

// Get tournament configuration
$tournamentConfig = null;
try {
    $tournamentConfig = fetchOne("SELECT * FROM tournament_configs WHERE event_sport_id = ?", [$eventSportId]);
} catch (Exception $e) {
    $tournamentConfig = null;
}

// Sample bracket data for demonstration
$sampleBracket = [
    'format' => 'single_elimination',
    'size' => 8,
    'rounds' => [
        [
            ['participant1' => 'Team A', 'participant2' => 'Team B', 'winner' => 'Team A', 'score' => '2-1'],
            ['participant1' => 'Team C', 'participant2' => 'Team D', 'winner' => 'Team C', 'score' => '3-0'],
            ['participant1' => 'Team E', 'participant2' => 'Team F', 'winner' => 'Team F', 'score' => '1-2'],
            ['participant1' => 'Team G', 'participant2' => 'Team H', 'winner' => 'Team G', 'score' => '4-1']
        ],
        [
            ['participant1' => 'Team A', 'participant2' => 'Team C', 'winner' => 'Team A', 'score' => '2-0'],
            ['participant1' => 'Team F', 'participant2' => 'Team G', 'winner' => 'Team F', 'score' => '3-2']
        ],
        [
            ['participant1' => 'Team A', 'participant2' => 'Team F', 'winner' => 'Team A', 'score' => '1-0']
        ]
    ]
];

$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tournament Bracket - <?php echo htmlspecialchars($eventSport['sport_name']); ?> - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>
    
    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1>
                    <i class="icon-tournament"></i>
                    Tournament Bracket
                </h1>
                <div class="sport-info">
                    <span class="sport-name"><?php echo htmlspecialchars($eventSport['sport_name']); ?></span>
                    <span class="event-name">in <?php echo htmlspecialchars($eventSport['event_name']); ?></span>
                    <span class="category-badge category-<?php echo $eventSport['sport_category']; ?>">
                        <?php echo ucfirst(str_replace('_', ' ', $eventSport['sport_category'])); ?>
                    </span>
                </div>
                <nav class="breadcrumb">
                    <a href="../dashboard.php">Dashboard</a>
                    <span>/</span>
                    <a href="index.php">Events</a>
                    <span>/</span>
                    <a href="details.php?id=<?php echo $eventId; ?>">Event Details</a>
                    <span>/</span>
                    <span>Tournament Bracket</span>
                </nav>
            </div>
            <div class="page-actions">
                <a href="tournament_config.php?event_sport_id=<?php echo $eventSportId; ?>&event_id=<?php echo $eventId; ?>" class="btn btn-secondary">
                    <i class="icon-settings"></i>
                    Configure Tournament
                </a>
                <a href="details.php?id=<?php echo $eventId; ?>" class="btn btn-secondary">
                    <i class="icon-arrow-left"></i>
                    Back to Event
                </a>
            </div>
        </div>
        
        <?php if (!$tournamentConfig): ?>
            <div class="alert alert-info">
                <h4><i class="icon-info"></i> Tournament Not Configured</h4>
                <p>This tournament hasn't been configured yet. Configure the tournament to generate brackets.</p>
                <a href="tournament_config.php?event_sport_id=<?php echo $eventSportId; ?>&event_id=<?php echo $eventId; ?>" class="btn btn-primary">
                    <i class="icon-settings"></i> Configure Tournament
                </a>
            </div>
        <?php else: ?>
            <!-- Tournament Bracket Visualization -->
            <div class="bracket-container">
                <div class="bracket-header">
                    <div class="bracket-info">
                        <h2><?php echo ucfirst(str_replace('_', ' ', $tournamentConfig['tournament_format'])); ?> Tournament</h2>
                        <div class="bracket-meta">
                            <span class="bracket-size"><?php echo $tournamentConfig['bracket_size']; ?> Participants</span>
                            <span class="seeding-type"><?php echo ucfirst($tournamentConfig['seeding_type']); ?> Seeding</span>
                        </div>
                    </div>
                    <div class="bracket-actions">
                        <button class="btn btn-primary" onclick="exportBracket()">
                            <i class="icon-download"></i> Export PDF
                        </button>
                        <button class="btn btn-secondary" onclick="printBracket()">
                            <i class="icon-print"></i> Print
                        </button>
                    </div>
                </div>
                
                <!-- Single Elimination Bracket -->
                <div class="bracket-visualization" id="bracketVisualization">
                    <div class="bracket-rounds">
                        <?php 
                        $rounds = $sampleBracket['rounds'];
                        $totalRounds = count($rounds);
                        
                        foreach ($rounds as $roundIndex => $matches): 
                            $roundNumber = $roundIndex + 1;
                            $isLastRound = $roundIndex === $totalRounds - 1;
                        ?>
                            <div class="bracket-round" data-round="<?php echo $roundNumber; ?>">
                                <div class="round-header">
                                    <h3><?php echo $isLastRound ? 'Final' : 'Round ' . $roundNumber; ?></h3>
                                </div>
                                <div class="round-matches">
                                    <?php foreach ($matches as $matchIndex => $match): ?>
                                        <div class="match-container">
                                            <div class="match-bracket">
                                                <div class="match-participant <?php echo ($match['winner'] ?? '') === $match['participant1'] ? 'winner' : ''; ?>">
                                                    <span class="participant-name"><?php echo htmlspecialchars($match['participant1']); ?></span>
                                                    <span class="participant-score"><?php echo explode('-', $match['score'] ?? '0-0')[0]; ?></span>
                                                </div>
                                                <div class="match-participant <?php echo ($match['winner'] ?? '') === $match['participant2'] ? 'winner' : ''; ?>">
                                                    <span class="participant-name"><?php echo htmlspecialchars($match['participant2']); ?></span>
                                                    <span class="participant-score"><?php echo explode('-', $match['score'] ?? '0-0')[1]; ?></span>
                                                </div>
                                            </div>
                                            <?php if (!$isLastRound): ?>
                                                <div class="match-connector"></div>
                                            <?php endif; ?>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- Champion Display -->
                <?php if (!empty($rounds[count($rounds) - 1][0]['winner'])): ?>
                    <div class="champion-display">
                        <div class="champion-trophy">🏆</div>
                        <div class="champion-info">
                            <h2>Tournament Champion</h2>
                            <h3><?php echo htmlspecialchars($rounds[count($rounds) - 1][0]['winner']); ?></h3>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
        
        <!-- Match Schedule -->
        <div class="match-schedule">
            <div class="section-header">
                <h2><i class="icon-calendar"></i> Match Schedule</h2>
                <button class="btn btn-primary btn-sm" onclick="scheduleMatches()">
                    <i class="icon-plus"></i>
                    Schedule Matches
                </button>
            </div>
            
            <div class="schedule-grid">
                <!-- Sample scheduled matches -->
                <div class="schedule-day">
                    <h3>Day 1 - Quarter Finals</h3>
                    <div class="day-matches">
                        <div class="scheduled-match">
                            <div class="match-time">08:00 AM</div>
                            <div class="match-teams">Team A vs Team B</div>
                            <div class="match-venue">Court 1</div>
                            <div class="match-status status-scheduled">Scheduled</div>
                        </div>
                        <div class="scheduled-match">
                            <div class="match-time">10:00 AM</div>
                            <div class="match-teams">Team C vs Team D</div>
                            <div class="match-venue">Court 1</div>
                            <div class="match-status status-scheduled">Scheduled</div>
                        </div>
                    </div>
                </div>
                
                <div class="schedule-day">
                    <h3>Day 2 - Semi Finals</h3>
                    <div class="day-matches">
                        <div class="scheduled-match">
                            <div class="match-time">02:00 PM</div>
                            <div class="match-teams">Winner QF1 vs Winner QF2</div>
                            <div class="match-venue">Court 1</div>
                            <div class="match-status status-pending">Pending</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <script src="../../assets/js/admin.js"></script>
    
    <style>
        /* Bracket Visualization Styles */
        .bracket-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .bracket-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--gray-200, #e5e7eb);
        }
        
        .bracket-info h2 {
            margin: 0 0 0.5rem 0;
            color: var(--gray-900, #111827);
        }
        
        .bracket-meta {
            display: flex;
            gap: 1rem;
        }
        
        .bracket-meta span {
            padding: 0.25rem 0.75rem;
            background: var(--gray-100, #f3f4f6);
            border-radius: 20px;
            font-size: 0.875rem;
            color: var(--gray-600, #4b5563);
        }
        
        .bracket-actions {
            display: flex;
            gap: 1rem;
        }
        
        .bracket-visualization {
            overflow-x: auto;
            padding: 1rem 0;
        }
        
        .bracket-rounds {
            display: flex;
            gap: 3rem;
            min-width: fit-content;
        }
        
        .bracket-round {
            display: flex;
            flex-direction: column;
            min-width: 200px;
        }
        
        .round-header {
            text-align: center;
            margin-bottom: 1rem;
        }
        
        .round-header h3 {
            margin: 0;
            padding: 0.5rem 1rem;
            background: var(--primary-100, #dbeafe);
            color: var(--primary-700, #1d4ed8);
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
        }
        
        .round-matches {
            display: flex;
            flex-direction: column;
            gap: 2rem;
            flex: 1;
            justify-content: center;
        }
        
        .match-container {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .match-bracket {
            border: 2px solid var(--gray-300, #d1d5db);
            border-radius: 8px;
            overflow: hidden;
            background: white;
            min-width: 180px;
        }
        
        .match-participant {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 1rem;
            border-bottom: 1px solid var(--gray-200, #e5e7eb);
            transition: all 0.3s ease;
        }
        
        .match-participant:last-child {
            border-bottom: none;
        }
        
        .match-participant.winner {
            background: var(--green-50, #f0fdf4);
            border-left: 4px solid var(--green-500, #22c55e);
            font-weight: 600;
        }
        
        .participant-name {
            font-size: 0.875rem;
            color: var(--gray-700, #374151);
        }
        
        .participant-score {
            font-weight: bold;
            color: var(--gray-900, #111827);
            background: var(--gray-100, #f3f4f6);
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            min-width: 24px;
            text-align: center;
        }
        
        .winner .participant-score {
            background: var(--green-100, #dcfce7);
            color: var(--green-700, #15803d);
        }
        
        .match-connector {
            width: 2rem;
            height: 2px;
            background: var(--gray-300, #d1d5db);
            position: relative;
        }
        
        .match-connector::before,
        .match-connector::after {
            content: '';
            position: absolute;
            width: 2px;
            height: 1rem;
            background: var(--gray-300, #d1d5db);
            right: 0;
        }
        
        .match-connector::before {
            top: -1rem;
        }
        
        .match-connector::after {
            bottom: -1rem;
        }
        
        .champion-display {
            text-align: center;
            margin-top: 2rem;
            padding: 2rem;
            background: linear-gradient(135deg, var(--yellow-50, #fefce8), var(--yellow-100, #fef3c7));
            border-radius: 12px;
            border: 2px solid var(--yellow-300, #fcd34d);
        }
        
        .champion-trophy {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        .champion-info h2 {
            margin: 0 0 0.5rem 0;
            color: var(--yellow-800, #92400e);
        }
        
        .champion-info h3 {
            margin: 0;
            color: var(--yellow-900, #78350f);
            font-size: 1.5rem;
        }
        
        /* Match Schedule Styles */
        .match-schedule {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 2rem;
        }
        
        .schedule-grid {
            display: grid;
            gap: 2rem;
        }
        
        .schedule-day h3 {
            margin: 0 0 1rem 0;
            color: var(--gray-900, #111827);
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--primary-200, #bfdbfe);
        }
        
        .day-matches {
            display: grid;
            gap: 1rem;
        }
        
        .scheduled-match {
            display: grid;
            grid-template-columns: auto 1fr auto auto;
            gap: 1rem;
            align-items: center;
            padding: 1rem;
            background: var(--gray-50, #f9fafb);
            border-radius: 8px;
            border-left: 4px solid var(--primary-500, #3b82f6);
        }
        
        .match-time {
            font-weight: 600;
            color: var(--primary-600, #2563eb);
        }
        
        .match-teams {
            font-weight: 500;
            color: var(--gray-700, #374151);
        }
        
        .match-venue {
            color: var(--gray-500, #6b7280);
            font-size: 0.875rem;
        }
        
        .match-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .status-scheduled {
            background: var(--green-100, #dcfce7);
            color: var(--green-700, #15803d);
        }
        
        .status-pending {
            background: var(--yellow-100, #fef3c7);
            color: var(--yellow-700, #a16207);
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .bracket-header {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }
            
            .bracket-actions {
                justify-content: center;
            }
            
            .bracket-rounds {
                gap: 1.5rem;
            }
            
            .match-bracket {
                min-width: 150px;
            }
            
            .scheduled-match {
                grid-template-columns: 1fr;
                text-align: center;
            }
        }
    </style>
    
    <script>
        function exportBracket() {
            alert('Bracket export functionality will be implemented in the next phase.');
        }
        
        function printBracket() {
            window.print();
        }
        
        function scheduleMatches() {
            alert('Match scheduling functionality will be implemented in the next phase.');
        }
    </script>
</body>
</html>
