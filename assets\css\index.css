/**
 * Samar College Intramurals Management System (SCIMS)
 * Public Interface Styles
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

/* CSS Custom Properties (Variables) */
:root {
    /* Primary Colors */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    
    /* Secondary Colors */
    --secondary-color: #64748b;
    --secondary-dark: #475569;
    --secondary-light: #94a3b8;
    
    /* Status Colors */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #06b6d4;
    
    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* Layout */
    --header-height: 80px;
    --container-max-width: 1200px;
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    
    /* Typography */
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    
    /* Transitions */
    --transition: all 0.2s ease-in-out;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-700);
    background-color: var(--white);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Container */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header Styles */
.site-header {
    background: var(--white);
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: var(--header-height);
}

.site-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    color: var(--gray-900);
}

.site-logo img {
    width: 40px;
    height: 40px;
}

.site-logo h1 {
    font-size: var(--font-size-xl);
    font-weight: 700;
}

.main-nav {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-item a {
    text-decoration: none;
    color: var(--gray-700);
    font-weight: 500;
    transition: var(--transition);
    padding: 0.5rem 0;
    border-bottom: 2px solid transparent;
}

.nav-item a:hover,
.nav-item a.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--gray-700);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    padding: 4rem 0;
    text-align: center;
}

.hero-content h1 {
    font-size: var(--font-size-5xl);
    font-weight: 700;
    margin-bottom: 1rem;
}

.hero-content p {
    font-size: var(--font-size-xl);
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.hero-stat {
    text-align: center;
}

.hero-stat h3 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.hero-stat p {
    opacity: 0.8;
}

/* Section Styles */
.section {
    padding: 4rem 0;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-header h2 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.section-header p {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

/* Card Styles */
.card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
}

.card-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.card-content {
    padding: 1.5rem;
}

.card-footer {
    padding: 1rem 1.5rem;
    background: var(--gray-50);
    border-top: 1px solid var(--gray-200);
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.btn-secondary {
    background: var(--gray-200);
    color: var(--gray-700);
}

.btn-secondary:hover {
    background: var(--gray-300);
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* Live Ticker */
.live-ticker {
    background: var(--gray-900);
    color: var(--white);
    padding: 1rem 0;
    overflow: hidden;
}

.ticker-content {
    display: flex;
    align-items: center;
    gap: 2rem;
    animation: scroll 30s linear infinite;
}

.ticker-item {
    white-space: nowrap;
    font-weight: 500;
}

@keyframes scroll {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
}

/* Schedule Table */
.schedule-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.schedule-table th,
.schedule-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--gray-200);
}

.schedule-table th {
    background: var(--gray-50);
    font-weight: 600;
    color: var(--gray-900);
}

.schedule-table tr:hover {
    background: var(--gray-50);
}

/* Standings Table */
.standings-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.standings-table th,
.standings-table td {
    padding: 1rem;
    text-align: center;
    border-bottom: 1px solid var(--gray-200);
}

.standings-table th {
    background: var(--gray-50);
    font-weight: 600;
    color: var(--gray-900);
}

.standings-table .dept-name {
    text-align: left;
}

.standings-table .rank {
    font-weight: 700;
    font-size: var(--font-size-lg);
}

.dept-color {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

/* Medal Styles */
.medal {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    font-weight: 700;
    font-size: var(--font-size-xs);
    margin: 0 0.25rem;
}

.medal.gold {
    background: #ffd700;
    color: #b8860b;
}

.medal.silver {
    background: #c0c0c0;
    color: #696969;
}

.medal.bronze {
    background: #cd7f32;
    color: #8b4513;
}

/* Footer */
.site-footer {
    background: var(--gray-900);
    color: var(--white);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: var(--gray-300);
    text-decoration: none;
    transition: var(--transition);
}

.footer-section ul li a:hover {
    color: var(--white);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--gray-700);
    color: var(--gray-400);
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-nav {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .hero-content h1 {
        font-size: var(--font-size-3xl);
    }
    
    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .card-grid {
        grid-template-columns: 1fr;
    }
    
    .schedule-table,
    .standings-table {
        font-size: var(--font-size-sm);
    }
    
    .schedule-table th,
    .schedule-table td,
    .standings-table th,
    .standings-table td {
        padding: 0.5rem;
    }
}
