<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Tournament Finalization Page - Step 4
 * Final configuration, rules, scoring systems, and tournament publishing
 * 
 * @version 2.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication and admin permission
requireAuth();
requirePermission('manage_events');

// Get parameters
$eventSportId = filter_input(INPUT_GET, 'event_sport_id', FILTER_VALIDATE_INT);
$eventId = filter_input(INPUT_GET, 'event_id', FILTER_VALIDATE_INT);

if (!$eventSportId || !$eventId) {
    header('Location: index.php?error=invalid_parameters');
    exit;
}

// Fetch event sport details
$eventSport = fetchOne("
    SELECT es.*, s.name as sport_name, s.category as sport_category, s.scoring_type,
           e.name as event_name, e.start_date, e.end_date, e.point_system
    FROM event_sports es
    INNER JOIN sports s ON es.sport_id = s.sport_id
    INNER JOIN events e ON es.event_id = e.event_id
    WHERE es.event_sport_id = ? AND es.event_id = ?
", [$eventSportId, $eventId]);

if (!$eventSport) {
    header('Location: index.php?error=event_sport_not_found');
    exit;
}

// Fetch tournament configuration from Step 2
$tournamentConfig = fetchOne("
    SELECT * FROM tournament_configs 
    WHERE event_sport_id = ?
", [$eventSportId]);

// Fetch tournament schedule from Step 3
$tournamentSchedule = fetchOne("
    SELECT * FROM tournament_schedules 
    WHERE config_id = ?
", [$tournamentConfig['config_id'] ?? 0]);

// Check if previous steps are completed
$stepsCompleted = [
    'bracket_setup' => $tournamentConfig && $tournamentConfig['status'] === 'active',
    'scheduling' => $tournamentSchedule && $tournamentSchedule['status'] === 'published'
];

// Initialize variables
$message = '';
$messageType = '';
$csrfToken = bin2hex(random_bytes(32));
$_SESSION['csrf_token'] = $csrfToken;

// Default scoring systems by sport category
$scoringSystems = [
    'team' => [
        'points_based' => [
            'name' => 'Points-Based Scoring',
            'description' => 'Traditional point accumulation system',
            'settings' => ['win_points' => 3, 'draw_points' => 1, 'loss_points' => 0]
        ],
        'set_based' => [
            'name' => 'Set-Based Scoring',
            'description' => 'Best of sets format (volleyball, tennis)',
            'settings' => ['sets_to_win' => 3, 'points_per_set' => 25]
        ],
        'time_based' => [
            'name' => 'Time-Based Scoring',
            'description' => 'Score within time periods',
            'settings' => ['periods' => 4, 'period_duration' => 15]
        ]
    ],
    'individual' => [
        'ranking_based' => [
            'name' => 'Ranking-Based Scoring',
            'description' => 'Position-based point allocation',
            'settings' => ['1st_place' => 15, '2nd_place' => 12, '3rd_place' => 10]
        ],
        'time_performance' => [
            'name' => 'Time Performance',
            'description' => 'Best time wins (track events)',
            'settings' => ['format' => 'mm:ss.ms', 'lower_better' => true]
        ],
        'distance_performance' => [
            'name' => 'Distance Performance',
            'description' => 'Furthest distance wins (field events)',
            'settings' => ['format' => 'meters', 'higher_better' => true]
        ]
    ],
    'performing_arts' => [
        'judge_scoring' => [
            'name' => 'Judge Panel Scoring',
            'description' => 'Multiple judges with criteria-based scoring',
            'settings' => ['num_judges' => 5, 'max_score' => 10, 'criteria' => ['technique', 'creativity', 'presentation']]
        ],
        'audience_voting' => [
            'name' => 'Audience Voting',
            'description' => 'Public voting system',
            'settings' => ['voting_weight' => 30, 'judge_weight' => 70]
        ]
    ],
    'academic' => [
        'written_oral' => [
            'name' => 'Written + Oral Examination',
            'description' => 'Combined written and oral assessment',
            'settings' => ['written_weight' => 60, 'oral_weight' => 40, 'max_score' => 100]
        ],
        'quiz_bowl' => [
            'name' => 'Quiz Bowl Format',
            'description' => 'Question-based competition',
            'settings' => ['questions_per_round' => 20, 'time_per_question' => 30]
        ]
    ],
    'pageant' => [
        'multi_criteria' => [
            'name' => 'Multi-Criteria Judging',
            'description' => 'Multiple categories with weighted scoring',
            'settings' => ['categories' => ['talent', 'interview', 'evening_gown'], 'weights' => [40, 30, 30]]
        ]
    ]
];

// Tournament rules templates
$rulesTemplates = [
    'general' => [
        'Eligibility' => 'All participants must be currently enrolled students',
        'Registration' => 'Teams must register before the deadline',
        'Equipment' => 'Standard equipment will be provided by the organizers',
        'Conduct' => 'Unsportsmanlike conduct will result in disqualification',
        'Disputes' => 'All disputes will be resolved by tournament officials'
    ],
    'team_sports' => [
        'Team Size' => 'Maximum roster size and playing positions',
        'Substitutions' => 'Rules for player substitutions during matches',
        'Timeouts' => 'Number and duration of timeouts allowed',
        'Overtime' => 'Procedures for tied matches',
        'Forfeits' => 'Conditions that result in match forfeiture'
    ],
    'individual_sports' => [
        'Attempts' => 'Number of attempts allowed per event',
        'Equipment Check' => 'All equipment must be inspected before competition',
        'False Starts' => 'Rules regarding false starts and penalties',
        'Measurements' => 'Official measurement procedures',
        'Appeals' => 'Process for appealing results'
    ]
];

// Fetch available officials/referees
$availableOfficials = fetchAll("
    SELECT referee_id, name, specialization, certification_level, experience_years
    FROM referees 
    WHERE status = 'active'
    ORDER BY certification_level DESC, experience_years DESC
");

// Get scheduled matches that need officials
$scheduledMatches = [];
if ($tournamentSchedule) {
    $scheduledMatches = fetchAll("
        SELECT sm.*, v.name as venue_name, v.location as venue_location
        FROM scheduled_matches sm
        LEFT JOIN venues v ON sm.venue_id = v.venue_id
        WHERE sm.schedule_id = ?
        ORDER BY sm.scheduled_date, sm.scheduled_time
    ", [$tournamentSchedule['schedule_id']]);
}

// Fetch existing tournament finalization data
$tournamentFinalization = fetchOne("
    SELECT * FROM tournament_finalizations 
    WHERE config_id = ?
", [$tournamentConfig['config_id'] ?? 0]);

// Default point system
$defaultPointSystem = json_decode($eventSport['point_system'] ?? '{}', true) ?: [
    '1st' => 15, '2nd' => 12, '3rd' => 10, '4th' => 8, '5th' => 6, '6th' => 4, '7th' => 2, '8th' => 1
];

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $message = 'Invalid security token. Please try again.';
        $messageType = 'error';
    } else {
        $action = $_POST['action'] ?? '';

        try {
            switch ($action) {
                case 'save_scoring_system':
                    $scoringSystem = $_POST['scoring_system'] ?? '';
                    $scoringSettings = $_POST['scoring_settings'] ?? [];
                    $pointSystem = $_POST['point_system'] ?? [];

                    if (empty($scoringSystem)) {
                        throw new Exception('Please select a scoring system.');
                    }

                    // Create tournament_finalizations table if it doesn't exist
                    if (!tableExists('tournament_finalizations')) {
                        executeQuery("
                            CREATE TABLE tournament_finalizations (
                                finalization_id INT AUTO_INCREMENT PRIMARY KEY,
                                config_id INT NOT NULL,
                                scoring_system VARCHAR(100) NOT NULL,
                                scoring_settings JSON,
                                point_system JSON,
                                tournament_rules JSON,
                                official_assignments JSON,
                                publication_settings JSON,
                                status ENUM('draft', 'configured', 'published', 'active', 'completed') DEFAULT 'draft',
                                published_at TIMESTAMP NULL,
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                FOREIGN KEY (config_id) REFERENCES tournament_configs(config_id) ON DELETE CASCADE
                            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                        ");
                    }

                    $finalizationData = [
                        'config_id' => $tournamentConfig['config_id'],
                        'scoring_system' => $scoringSystem,
                        'scoring_settings' => json_encode($scoringSettings),
                        'point_system' => json_encode($pointSystem ?: $defaultPointSystem),
                        'status' => 'draft'
                    ];

                    if ($tournamentFinalization) {
                        updateRecord('tournament_finalizations', $finalizationData, 'finalization_id = ?', [$tournamentFinalization['finalization_id']]);
                        $message = 'Scoring system updated successfully!';
                    } else {
                        insertRecord('tournament_finalizations', $finalizationData);
                        $message = 'Scoring system configured successfully!';
                    }

                    $messageType = 'success';

                    // Refresh finalization data
                    $tournamentFinalization = fetchOne("
                        SELECT * FROM tournament_finalizations
                        WHERE config_id = ?
                    ", [$tournamentConfig['config_id']]);

                    break;

                case 'save_tournament_rules':
                    if (!$tournamentFinalization) {
                        throw new Exception('Please configure scoring system first.');
                    }

                    $tournamentRules = $_POST['tournament_rules'] ?? [];

                    updateRecord('tournament_finalizations', [
                        'tournament_rules' => json_encode($tournamentRules)
                    ], 'finalization_id = ?', [$tournamentFinalization['finalization_id']]);

                    $message = 'Tournament rules saved successfully!';
                    $messageType = 'success';

                    // Refresh finalization data
                    $tournamentFinalization = fetchOne("
                        SELECT * FROM tournament_finalizations
                        WHERE config_id = ?
                    ", [$tournamentConfig['config_id']]);

                    break;

                case 'assign_officials':
                    if (!$tournamentFinalization) {
                        throw new Exception('Please configure tournament settings first.');
                    }

                    $officialAssignments = $_POST['official_assignments'] ?? [];

                    // Create match_officials table if it doesn't exist
                    if (!tableExists('match_officials')) {
                        executeQuery("
                            CREATE TABLE match_officials (
                                official_id INT AUTO_INCREMENT PRIMARY KEY,
                                match_id VARCHAR(50) NOT NULL,
                                referee_id INT NOT NULL,
                                role ENUM('head_referee', 'assistant', 'scorer', 'timekeeper') NOT NULL,
                                assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                FOREIGN KEY (referee_id) REFERENCES referees(referee_id) ON DELETE CASCADE,
                                UNIQUE KEY unique_match_role (match_id, role)
                            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                        ");
                    }

                    // Clear existing assignments
                    executeQuery("DELETE FROM match_officials WHERE match_id IN (
                        SELECT match_id FROM scheduled_matches WHERE schedule_id = ?
                    )", [$tournamentSchedule['schedule_id']]);

                    // Insert new assignments
                    $assignedCount = 0;
                    foreach ($officialAssignments as $matchId => $assignments) {
                        foreach ($assignments as $role => $refereeId) {
                            if (!empty($refereeId)) {
                                insertRecord('match_officials', [
                                    'match_id' => $matchId,
                                    'referee_id' => (int)$refereeId,
                                    'role' => $role
                                ]);
                                $assignedCount++;
                            }
                        }
                    }

                    // Update finalization record
                    updateRecord('tournament_finalizations', [
                        'official_assignments' => json_encode($officialAssignments)
                    ], 'finalization_id = ?', [$tournamentFinalization['finalization_id']]);

                    $message = "Successfully assigned {$assignedCount} officials to matches!";
                    $messageType = 'success';

                    break;

                case 'publish_tournament':
                    if (!$tournamentFinalization) {
                        throw new Exception('Please complete tournament configuration first.');
                    }

                    // Validate that all required configurations are complete
                    $validationErrors = validateTournamentReadiness($tournamentConfig, $tournamentSchedule, $tournamentFinalization);

                    if (!empty($validationErrors)) {
                        throw new Exception('Tournament not ready for publication: ' . implode(', ', $validationErrors));
                    }

                    // Update all related records to published/active status
                    updateRecord('tournament_configs', ['status' => 'active'], 'config_id = ?', [$tournamentConfig['config_id']]);
                    updateRecord('tournament_schedules', ['status' => 'active'], 'schedule_id = ?', [$tournamentSchedule['schedule_id']]);
                    updateRecord('tournament_finalizations', [
                        'status' => 'published',
                        'published_at' => date('Y-m-d H:i:s')
                    ], 'finalization_id = ?', [$tournamentFinalization['finalization_id']]);

                    $message = 'Tournament published successfully! The tournament is now live and ready for competition.';
                    $messageType = 'success';

                    // Refresh all data
                    $tournamentConfig = fetchOne("SELECT * FROM tournament_configs WHERE event_sport_id = ?", [$eventSportId]);
                    $tournamentSchedule = fetchOne("SELECT * FROM tournament_schedules WHERE config_id = ?", [$tournamentConfig['config_id']]);
                    $tournamentFinalization = fetchOne("SELECT * FROM tournament_finalizations WHERE config_id = ?", [$tournamentConfig['config_id']]);

                    break;

                default:
                    throw new Exception('Invalid action specified.');
            }

        } catch (Exception $e) {
            $message = $e->getMessage();
            $messageType = 'error';
        }
    }
}

/**
 * Validate tournament readiness for publication
 */
function validateTournamentReadiness($config, $schedule, $finalization) {
    $errors = [];

    if (!$config || $config['status'] !== 'active') {
        $errors[] = 'Tournament bracket not configured';
    }

    if (!$schedule || $schedule['status'] !== 'published') {
        $errors[] = 'Tournament schedule not published';
    }

    if (!$finalization) {
        $errors[] = 'Tournament finalization not configured';
    } else {
        $scoringSettings = json_decode($finalization['scoring_settings'], true);
        $tournamentRules = json_decode($finalization['tournament_rules'], true);

        if (empty($scoringSettings)) {
            $errors[] = 'Scoring system not configured';
        }

        if (empty($tournamentRules)) {
            $errors[] = 'Tournament rules not defined';
        }
    }

    return $errors;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tournament Finalization - <?php echo htmlspecialchars($eventSport['sport_name']); ?> | <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <style>
        /* Tournament Finalization Specific Styles */
        .finalization-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }

        .tournament-header {
            background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
            color: white;
            padding: 30px;
            border-radius: var(--border-radius-lg);
            margin-bottom: 30px;
            box-shadow: var(--shadow-lg);
        }

        .tournament-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .tournament-header .sport-icon {
            font-size: 3rem;
        }

        .tournament-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .meta-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: var(--border-radius);
            backdrop-filter: blur(10px);
        }

        .meta-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-bottom: 5px;
        }

        .meta-value {
            font-size: 1.1rem;
            font-weight: 600;
        }

        .wizard-navigation {
            display: flex;
            justify-content: center;
            margin-bottom: 40px;
        }

        .wizard-steps {
            display: flex;
            align-items: center;
            background: white;
            padding: 20px 40px;
            border-radius: 50px;
            box-shadow: var(--shadow);
            gap: 30px;
        }

        .wizard-step {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 20px;
            border-radius: 25px;
            transition: var(--transition);
            cursor: pointer;
            text-decoration: none;
            color: var(--gray-600);
        }

        .wizard-step.active {
            background: #7c3aed;
            color: white;
            transform: scale(1.05);
        }

        .wizard-step.completed {
            background: var(--success-color);
            color: white;
        }

        .wizard-step.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: currentColor;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .wizard-step:not(.active):not(.completed) .step-number {
            background: var(--gray-300);
            color: var(--gray-600);
        }

        .step-title {
            font-weight: 600;
            white-space: nowrap;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            min-height: 600px;
        }

        .config-panel {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow);
            overflow: hidden;
        }

        .panel-header {
            background: var(--gray-50);
            padding: 20px 25px;
            border-bottom: 1px solid var(--gray-200);
        }

        .panel-header h3 {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 5px;
        }

        .panel-header p {
            color: var(--gray-600);
            font-size: 0.9rem;
        }

        .panel-content {
            padding: 25px;
        }

        /* Prerequisites Check */
        .prerequisites-check {
            background: white;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .prerequisites-header {
            background: var(--gray-50);
            padding: 20px 25px;
            border-bottom: 1px solid var(--gray-200);
        }

        .prerequisites-content {
            padding: 25px;
        }

        .prerequisite-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px;
            border: 1px solid var(--gray-200);
            border-radius: var(--border-radius);
            margin-bottom: 10px;
        }

        .prerequisite-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .prerequisite-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .status-completed {
            background: var(--success-color);
            color: white;
        }

        .status-pending {
            background: var(--warning-color);
            color: white;
        }

        .status-missing {
            background: var(--error-color);
            color: white;
        }

        /* Configuration Sections */
        .config-section {
            margin-bottom: 30px;
        }

        .config-section h4 {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 15px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-group label {
            font-weight: 600;
            color: var(--gray-700);
            font-size: 0.9rem;
        }

        .form-control {
            padding: 10px 12px;
            border: 1px solid var(--gray-300);
            border-radius: var(--border-radius);
            font-size: 0.95rem;
            transition: var(--transition);
        }

        .form-control:focus {
            outline: none;
            border-color: #7c3aed;
            box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
        }

        /* Scoring System Cards */
        .scoring-systems-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .scoring-card {
            border: 2px solid var(--gray-200);
            border-radius: var(--border-radius-lg);
            padding: 20px;
            cursor: pointer;
            transition: var(--transition);
            background: white;
        }

        .scoring-card:hover {
            border-color: #7c3aed;
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .scoring-card.selected {
            border-color: #7c3aed;
            background: #7c3aed;
            color: white;
        }

        .scoring-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .scoring-icon {
            font-size: 2rem;
        }

        .scoring-name {
            font-size: 1.2rem;
            font-weight: 600;
        }

        .scoring-description {
            margin-bottom: 15px;
            line-height: 1.5;
        }

        /* Rules Editor */
        .rules-editor {
            border: 1px solid var(--gray-300);
            border-radius: var(--border-radius);
            overflow: hidden;
        }

        .rule-item {
            display: flex;
            border-bottom: 1px solid var(--gray-200);
        }

        .rule-item:last-child {
            border-bottom: none;
        }

        .rule-label {
            background: var(--gray-50);
            padding: 15px;
            width: 200px;
            font-weight: 600;
            color: var(--gray-700);
            border-right: 1px solid var(--gray-200);
        }

        .rule-input {
            flex: 1;
            padding: 15px;
        }

        .rule-input textarea {
            width: 100%;
            border: none;
            outline: none;
            resize: vertical;
            min-height: 60px;
            font-family: inherit;
        }

        /* Officials Assignment */
        .officials-grid {
            display: grid;
            gap: 20px;
        }

        .match-official-item {
            border: 1px solid var(--gray-200);
            border-radius: var(--border-radius);
            padding: 20px;
            background: white;
        }

        .match-info {
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--gray-200);
        }

        .match-title {
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 5px;
        }

        .match-details {
            font-size: 0.9rem;
            color: var(--gray-600);
        }

        .officials-assignment {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        /* Publication Panel */
        .publication-panel {
            text-align: center;
            padding: 40px 20px;
        }

        .publication-status {
            font-size: 1.2rem;
            margin-bottom: 20px;
        }

        .publication-actions {
            display: flex;
            flex-direction: column;
            gap: 15px;
            align-items: center;
        }

        .btn-publish {
            background: #7c3aed;
            color: white;
            padding: 15px 30px;
            font-size: 1.1rem;
            border-radius: var(--border-radius-lg);
        }

        .btn-publish:hover {
            background: #5b21b6;
        }

        .btn-publish:disabled {
            background: var(--gray-400);
            cursor: not-allowed;
        }

        /* Enhanced Responsive Design */
        @media (max-width: 1200px) {
            .finalization-container {
                padding: 16px;
            }

            .wizard-steps {
                padding: 20px 36px;
                gap: 28px;
            }

            .wizard-step {
                min-width: 130px;
                padding: 12px 20px;
            }

            .tournament-meta {
                grid-template-columns: repeat(2, 1fr);
                gap: 16px;
            }

            .scoring-systems-grid {
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
            }
        }

        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 24px;
            }

            .tournament-header h1 {
                font-size: 2.2rem;
            }

            .scoring-systems-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .wizard-steps {
                padding: 20px 32px;
                gap: 24px;
            }

            .left-panel {
                order: 1;
            }

            .right-panel {
                order: 2;
            }

            .finalization-actions {
                padding: 20px;
                gap: 16px;
            }
        }

        @media (max-width: 768px) {
            .finalization-container {
                padding: 12px;
            }

            .wizard-steps {
                flex-direction: column;
                gap: 16px;
                padding: 20px;
                border-radius: 20px;
            }

            .wizard-step {
                width: 100%;
                min-width: auto;
                justify-content: flex-start;
                padding: 16px 20px;
            }

            .wizard-step::after {
                display: none;
            }

            .tournament-meta {
                grid-template-columns: 1fr;
                gap: 16px;
                text-align: center;
            }

            .tournament-header {
                padding: 24px 20px;
                text-align: center;
            }

            .tournament-header h1 {
                font-size: 1.8rem;
                flex-direction: column;
                gap: 12px;
            }

            .panel-content {
                padding: 20px;
            }

            .config-section {
                margin-bottom: 32px;
            }

            .form-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .btn {
                width: 100%;
                min-height: 48px;
                font-size: 1rem;
                justify-content: center;
            }

            .panel-header h3 {
                font-size: 1.3rem;
            }

            .panel-header p {
                font-size: 0.9rem;
            }
        }

        @media (max-width: 480px) {
            .finalization-container {
                padding: 8px;
            }

            .tournament-header {
                padding: 20px 16px;
            }

            .tournament-header h1 {
                font-size: 1.5rem;
            }

            .wizard-steps {
                padding: 16px;
                gap: 12px;
            }

            .wizard-step {
                padding: 12px 16px;
                font-size: 0.9rem;
            }

            .step-number {
                width: 24px;
                height: 24px;
                font-size: 0.7rem;
            }

            .step-title {
                font-size: 0.85rem;
            }

            .panel-content {
                padding: 16px;
            }

            .config-section h4 {
                font-size: 1.1rem;
            }

            .form-control {
                font-size: 0.9rem;
                padding: 12px;
            }

            .btn {
                padding: 12px 20px;
                font-size: 0.9rem;
            }

            .meta-item {
                padding: 12px;
            }

            .meta-label {
                font-size: 0.8rem;
            }

            .meta-value {
                font-size: 0.9rem;
            }
        }

        /* Enhanced Form Validation Styles */
        .field-error {
            border-color: #ef4444 !important;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
            animation: shake 0.3s ease-in-out;
        }

        .field-success {
            border-color: #10b981 !important;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1) !important;
        }

        .field-error-message {
            animation: fadeInUp 0.3s ease-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(100%); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideOutRight {
            from { opacity: 1; transform: translateX(0); }
            to { opacity: 0; transform: translateX(100%); }
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
            outline: none;
        }

        .form-control.field-error:focus {
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .form-control.field-success:focus {
            border-color: #10b981;
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
        }

        .btn.loading {
            position: relative;
            color: transparent !important;
            pointer-events: none;
        }

        .btn.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>

    <main class="admin-main">
        <div class="finalization-container">
            <!-- Tournament Header -->
            <div class="tournament-header">
                <h1>
                    <span class="sport-icon">🏁</span>
                    Tournament Finalization
                </h1>
                <p style="font-size: 1.1rem; opacity: 0.9; margin-bottom: 0;">
                    Final configuration and publishing for <strong><?php echo htmlspecialchars($eventSport['sport_name']); ?></strong>
                    in <?php echo htmlspecialchars($eventSport['event_name']); ?>
                </p>

                <div class="tournament-meta">
                    <div class="meta-item">
                        <div class="meta-label">Tournament Format</div>
                        <div class="meta-value">
                            <?php echo $tournamentConfig ? ucfirst(str_replace('_', ' ', $tournamentConfig['tournament_format'])) : 'Not Configured'; ?>
                        </div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">Scheduled Matches</div>
                        <div class="meta-value"><?php echo count($scheduledMatches); ?> Matches</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">Available Officials</div>
                        <div class="meta-value"><?php echo count($availableOfficials); ?> Officials</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">Tournament Status</div>
                        <div class="meta-value">
                            <?php
                            if ($tournamentFinalization && $tournamentFinalization['status'] === 'published') {
                                echo 'Published';
                            } elseif ($tournamentFinalization) {
                                echo ucfirst($tournamentFinalization['status']);
                            } else {
                                echo 'Not Configured';
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Wizard Navigation -->
            <div class="wizard-navigation">
                <div class="wizard-steps">
                    <a href="create.php?event_id=<?php echo $eventId; ?>" class="wizard-step completed">
                        <div class="step-number">1</div>
                        <div class="step-title">Event Setup</div>
                    </a>
                    <a href="tournament_config.php?event_sport_id=<?php echo $eventSportId; ?>&event_id=<?php echo $eventId; ?>"
                       class="wizard-step <?php echo $stepsCompleted['bracket_setup'] ? 'completed' : 'disabled'; ?>">
                        <div class="step-number">2</div>
                        <div class="step-title">Bracket Setup</div>
                    </a>
                    <a href="tournament_schedule.php?event_sport_id=<?php echo $eventSportId; ?>&event_id=<?php echo $eventId; ?>"
                       class="wizard-step <?php echo $stepsCompleted['scheduling'] ? 'completed' : 'disabled'; ?>">
                        <div class="step-number">3</div>
                        <div class="step-title">Scheduling</div>
                    </a>
                    <div class="wizard-step active">
                        <div class="step-number">4</div>
                        <div class="step-title">Configuration</div>
                    </div>
                </div>
            </div>

            <!-- Alert Messages -->
            <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType; ?>">
                    <i class="icon-<?php echo $messageType; ?>"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <!-- Prerequisites Check -->
            <div class="prerequisites-check">
                <div class="prerequisites-header">
                    <h3>📋 Prerequisites Check</h3>
                    <p>Verify that all previous steps are completed before finalizing the tournament</p>
                </div>
                <div class="prerequisites-content">
                    <div class="prerequisite-item">
                        <div class="prerequisite-info">
                            <span style="font-size: 1.5rem;">🏆</span>
                            <div>
                                <div style="font-weight: 600;">Tournament Bracket Configuration</div>
                                <div style="font-size: 0.9rem; color: var(--gray-600);">
                                    Bracket format selected and participants configured
                                </div>
                            </div>
                        </div>
                        <div class="prerequisite-status <?php echo $stepsCompleted['bracket_setup'] ? 'status-completed' : 'status-missing'; ?>">
                            <?php echo $stepsCompleted['bracket_setup'] ? 'Completed' : 'Missing'; ?>
                        </div>
                    </div>

                    <div class="prerequisite-item">
                        <div class="prerequisite-info">
                            <span style="font-size: 1.5rem;">📅</span>
                            <div>
                                <div style="font-weight: 600;">Match Scheduling</div>
                                <div style="font-size: 0.9rem; color: var(--gray-600);">
                                    All matches scheduled with venues and times
                                </div>
                            </div>
                        </div>
                        <div class="prerequisite-status <?php echo $stepsCompleted['scheduling'] ? 'status-completed' : 'status-missing'; ?>">
                            <?php echo $stepsCompleted['scheduling'] ? 'Completed' : 'Missing'; ?>
                        </div>
                    </div>

                    <?php if (!$stepsCompleted['bracket_setup'] || !$stepsCompleted['scheduling']): ?>
                        <div style="background: #fef3c7; padding: 15px; border-radius: var(--border-radius); margin-top: 20px; border-left: 4px solid #f59e0b;">
                            <strong>⚠️ Action Required:</strong> Please complete the missing prerequisites before proceeding with tournament finalization.
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <?php if ($stepsCompleted['bracket_setup'] && $stepsCompleted['scheduling']): ?>
                <!-- Main Configuration Content -->
                <div class="main-content">
                    <!-- Left Panel: Scoring & Rules Configuration -->
                    <div class="config-panel">
                        <div class="panel-header">
                            <h3>⚙️ Tournament Configuration</h3>
                            <p>Configure scoring systems and tournament rules</p>
                        </div>
                        <div class="panel-content">
                            <!-- Scoring System Configuration -->
                            <div class="config-section">
                                <h4>Scoring System</h4>
                                <form method="POST" id="scoringSystemForm">
                                    <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                    <input type="hidden" name="action" value="save_scoring_system">

                                    <div class="scoring-systems-grid">
                                        <?php
                                        $availableScoringSystems = $scoringSystems[$eventSport['sport_category']] ?? [];
                                        $currentScoringSystem = $tournamentFinalization ? $tournamentFinalization['scoring_system'] : '';
                                        ?>
                                        <?php foreach ($availableScoringSystems as $systemKey => $system): ?>
                                            <div class="scoring-card <?php echo ($currentScoringSystem === $systemKey) ? 'selected' : ''; ?>"
                                                 onclick="selectScoringSystem('<?php echo $systemKey; ?>')">
                                                <div class="scoring-header">
                                                    <div class="scoring-icon">📊</div>
                                                    <div class="scoring-name"><?php echo htmlspecialchars($system['name']); ?></div>
                                                </div>
                                                <div class="scoring-description">
                                                    <?php echo htmlspecialchars($system['description']); ?>
                                                </div>
                                                <div class="scoring-settings">
                                                    <?php foreach ($system['settings'] as $key => $value): ?>
                                                        <small><?php echo ucfirst(str_replace('_', ' ', $key)); ?>:
                                                        <?php echo is_array($value) ? implode(', ', $value) : $value; ?></small><br>
                                                    <?php endforeach; ?>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>

                                    <input type="hidden" name="scoring_system" id="selected_scoring_system"
                                           value="<?php echo htmlspecialchars($currentScoringSystem); ?>">

                                    <!-- Point System Configuration -->
                                    <div class="form-group">
                                        <label>Point System (Position-based rewards)</label>
                                        <div class="form-grid">
                                            <?php
                                            $currentPointSystem = $tournamentFinalization ?
                                                json_decode($tournamentFinalization['point_system'], true) : $defaultPointSystem;
                                            ?>
                                            <?php foreach ($defaultPointSystem as $position => $points): ?>
                                                <div class="form-group">
                                                    <label><?php echo $position; ?> Place</label>
                                                    <input type="number" name="point_system[<?php echo $position; ?>]"
                                                           class="form-control" min="0" max="50"
                                                           value="<?php echo $currentPointSystem[$position] ?? $points; ?>">
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>

                                    <div style="margin-top: 20px;">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="icon-check"></i>
                                            Save Scoring Configuration
                                        </button>
                                    </div>
                                </form>
                            </div>

                            <!-- Tournament Rules Configuration -->
                            <?php if ($tournamentFinalization): ?>
                                <div class="config-section">
                                    <h4>Tournament Rules</h4>
                                    <form method="POST" id="tournamentRulesForm">
                                        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                        <input type="hidden" name="action" value="save_tournament_rules">

                                        <div class="rules-editor">
                                            <?php
                                            $currentRules = $tournamentFinalization ?
                                                json_decode($tournamentFinalization['tournament_rules'], true) : [];
                                            $applicableRules = array_merge(
                                                $rulesTemplates['general'],
                                                $rulesTemplates[$eventSport['sport_category'] === 'team' ? 'team_sports' : 'individual_sports'] ?? []
                                            );
                                            ?>
                                            <?php foreach ($applicableRules as $ruleCategory => $defaultRule): ?>
                                                <div class="rule-item">
                                                    <div class="rule-label"><?php echo htmlspecialchars($ruleCategory); ?></div>
                                                    <div class="rule-input">
                                                        <textarea name="tournament_rules[<?php echo htmlspecialchars($ruleCategory); ?>]"
                                                                  placeholder="<?php echo htmlspecialchars($defaultRule); ?>"><?php
                                                            echo htmlspecialchars($currentRules[$ruleCategory] ?? $defaultRule);
                                                        ?></textarea>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>

                                        <div style="margin-top: 20px;">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="icon-check"></i>
                                                Save Tournament Rules
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Right Panel: Officials Assignment & Publication -->
                    <div class="config-panel">
                        <div class="panel-header">
                            <h3>👨‍⚖️ Officials & Publication</h3>
                            <p>Assign officials to matches and publish tournament</p>
                        </div>
                        <div class="panel-content">
                            <!-- Officials Assignment -->
                            <?php if ($tournamentFinalization && !empty($scheduledMatches)): ?>
                                <div class="config-section">
                                    <h4>Official Assignments</h4>
                                    <form method="POST" id="officialsForm">
                                        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                        <input type="hidden" name="action" value="assign_officials">

                                        <div class="officials-grid">
                                            <?php foreach (array_slice($scheduledMatches, 0, 5) as $match): // Show first 5 matches ?>
                                                <div class="match-official-item">
                                                    <div class="match-info">
                                                        <div class="match-title">Match <?php echo htmlspecialchars($match['match_id']); ?></div>
                                                        <div class="match-details">
                                                            <?php echo date('M j, Y', strtotime($match['scheduled_date'])); ?> at
                                                            <?php echo date('g:i A', strtotime($match['scheduled_time'])); ?> -
                                                            <?php echo htmlspecialchars($match['venue_name']); ?>
                                                        </div>
                                                    </div>

                                                    <div class="officials-assignment">
                                                        <div class="form-group">
                                                            <label>Head Referee</label>
                                                            <select name="official_assignments[<?php echo $match['match_id']; ?>][head_referee]"
                                                                    class="form-control">
                                                                <option value="">Select Official</option>
                                                                <?php foreach ($availableOfficials as $official): ?>
                                                                    <option value="<?php echo $official['referee_id']; ?>">
                                                                        <?php echo htmlspecialchars($official['name']); ?>
                                                                        (<?php echo ucfirst($official['certification_level']); ?>)
                                                                    </option>
                                                                <?php endforeach; ?>
                                                            </select>
                                                        </div>

                                                        <div class="form-group">
                                                            <label>Scorer</label>
                                                            <select name="official_assignments[<?php echo $match['match_id']; ?>][scorer]"
                                                                    class="form-control">
                                                                <option value="">Select Official</option>
                                                                <?php foreach ($availableOfficials as $official): ?>
                                                                    <option value="<?php echo $official['referee_id']; ?>">
                                                                        <?php echo htmlspecialchars($official['name']); ?>
                                                                    </option>
                                                                <?php endforeach; ?>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>

                                            <?php if (count($scheduledMatches) > 5): ?>
                                                <div style="text-align: center; padding: 20px; color: var(--gray-600);">
                                                    <p>Showing first 5 matches. <?php echo count($scheduledMatches) - 5; ?> more matches available.</p>
                                                    <small>Complete official assignments will be available after initial configuration.</small>
                                                </div>
                                            <?php endif; ?>
                                        </div>

                                        <div style="margin-top: 20px;">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="icon-users"></i>
                                                Assign Officials
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            <?php endif; ?>

                            <!-- Tournament Publication -->
                            <div class="config-section">
                                <h4>Tournament Publication</h4>
                                <div class="publication-panel">
                                    <?php if ($tournamentFinalization && $tournamentFinalization['status'] === 'published'): ?>
                                        <div class="publication-status" style="color: var(--success-color);">
                                            ✅ Tournament Published Successfully!
                                        </div>
                                        <p>Tournament is now live and ready for competition.</p>
                                        <div class="publication-actions">
                                            <a href="../matches/index.php?event_id=<?php echo $eventId; ?>" class="btn btn-primary">
                                                <i class="icon-play"></i>
                                                Manage Live Matches
                                            </a>
                                            <a href="../scores/index.php?event_id=<?php echo $eventId; ?>" class="btn btn-secondary">
                                                <i class="icon-score"></i>
                                                Record Scores
                                            </a>
                                        </div>
                                    <?php else: ?>
                                        <?php
                                        $validationErrors = validateTournamentReadiness($tournamentConfig, $tournamentSchedule, $tournamentFinalization);
                                        $canPublish = empty($validationErrors);
                                        ?>

                                        <div class="publication-status">
                                            <?php if ($canPublish): ?>
                                                🚀 Ready to Publish Tournament
                                            <?php else: ?>
                                                ⚠️ Tournament Not Ready for Publication
                                            <?php endif; ?>
                                        </div>

                                        <?php if (!empty($validationErrors)): ?>
                                            <div style="background: #fef2f2; padding: 15px; border-radius: var(--border-radius); margin: 20px 0; border-left: 4px solid #ef4444;">
                                                <strong>Issues to resolve:</strong>
                                                <ul style="margin: 10px 0 0 20px;">
                                                    <?php foreach ($validationErrors as $error): ?>
                                                        <li><?php echo htmlspecialchars($error); ?></li>
                                                    <?php endforeach; ?>
                                                </ul>
                                            </div>
                                        <?php endif; ?>

                                        <div class="publication-actions">
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                                <input type="hidden" name="action" value="publish_tournament">
                                                <button type="submit" class="btn-publish" <?php echo $canPublish ? '' : 'disabled'; ?>>
                                                    <i class="icon-publish"></i>
                                                    Publish Tournament
                                                </button>
                                            </form>

                                            <?php if ($canPublish): ?>
                                                <small style="color: var(--gray-600);">
                                                    Publishing will make the tournament live and notify all participants.
                                                </small>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <!-- Prerequisites Not Met -->
                <div style="text-align: center; padding: 60px 20px; color: var(--gray-500);">
                    <div style="font-size: 4rem; margin-bottom: 20px; opacity: 0.5;">⚠️</div>
                    <h3>Prerequisites Not Met</h3>
                    <p>Please complete the bracket setup and match scheduling before proceeding with tournament finalization.</p>
                    <div style="margin-top: 30px;">
                        <?php if (!$stepsCompleted['bracket_setup']): ?>
                            <a href="tournament_config.php?event_sport_id=<?php echo $eventSportId; ?>&event_id=<?php echo $eventId; ?>"
                               class="btn btn-primary">
                                <i class="icon-arrow-left"></i>
                                Complete Bracket Setup
                            </a>
                        <?php elseif (!$stepsCompleted['scheduling']): ?>
                            <a href="tournament_schedule.php?event_sport_id=<?php echo $eventSportId; ?>&event_id=<?php echo $eventId; ?>"
                               class="btn btn-primary">
                                <i class="icon-arrow-left"></i>
                                Complete Match Scheduling
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <script>
        // Tournament Finalization JavaScript
        let selectedScoringSystem = '<?php echo $tournamentFinalization ? $tournamentFinalization['scoring_system'] : ''; ?>';

        // Initialize finalization interface
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Tournament Finalization Page Loaded');

            // Initialize enhanced form handling
            initializeFormHandling();

            // Initialize form validation
            initializeFormValidation();

            // Initialize auto-save
            initializeAutoSave();

            // Show helpful message
            setTimeout(() => {
                showNotification('Complete the tournament configuration to finalize your tournament setup.', 'info', 4000);
            }, 1000);
        });

        // Scoring system selection
        function selectScoringSystem(systemKey) {
            // Remove previous selection
            document.querySelectorAll('.scoring-card').forEach(card => {
                card.classList.remove('selected');
            });

            // Add selection to clicked card
            event.currentTarget.classList.add('selected');
            selectedScoringSystem = systemKey;

            // Update hidden input
            document.getElementById('selected_scoring_system').value = systemKey;

            console.log('Selected scoring system:', systemKey);
        }

        // Form validation
        function initializeFormValidation() {
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    if (!validateForm(this)) {
                        e.preventDefault();
                        showNotification('Please complete all required fields.', 'error');
                    }
                });
            });
        }

        // Enhanced Notification System
        function showNotification(message, type = 'info', duration = 4000, persistent = false) {
            const existingNotifications = document.querySelectorAll(`.notification-${type}`);
            existingNotifications.forEach(notification => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            });

            const notification = document.createElement('div');
            notification.className = `alert alert-${type} notification-${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                min-width: 320px;
                max-width: 500px;
                padding: 16px 20px;
                border-radius: 8px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                animation: slideInRight 0.3s ease-out;
                font-size: 0.95rem;
                line-height: 1.4;
            `;

            const typeStyles = {
                success: 'background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white;',
                error: 'background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white;',
                warning: 'background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white;',
                info: 'background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%); color: white;'
            };

            notification.style.cssText += typeStyles[type] || typeStyles.info;

            const icons = { success: '✅', error: '❌', warning: '⚠️', info: 'ℹ️' };

            notification.innerHTML = `
                <div style="display: flex; align-items: flex-start; gap: 12px;">
                    <span style="font-size: 1.2rem; flex-shrink: 0;">${icons[type] || icons.info}</span>
                    <div style="flex: 1;">
                        <div style="font-weight: 600; margin-bottom: 4px;">${type.charAt(0).toUpperCase() + type.slice(1)}</div>
                        <div>${message}</div>
                    </div>
                    ${!persistent ? '<button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: inherit; font-size: 1.2rem; cursor: pointer; padding: 0; margin-left: 8px; opacity: 0.7;">×</button>' : ''}
                </div>
            `;

            document.body.appendChild(notification);

            if (!persistent) {
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.style.animation = 'slideOutRight 0.3s ease-in';
                        setTimeout(() => {
                            if (notification.parentNode) {
                                notification.parentNode.removeChild(notification);
                            }
                        }, 300);
                    }
                }, duration);
            }

            return notification;
        }

        function validateField(field, rules = {}) {
            const value = field.value.trim();
            const fieldName = field.getAttribute('data-field-name') || field.name || 'Field';
            let isValid = true;
            let errorMessage = '';

            // Remove existing error styling
            field.classList.remove('field-error', 'field-success');
            const existingError = field.parentNode.querySelector('.field-error-message');
            if (existingError) {
                existingError.remove();
            }

            // Required validation
            if (rules.required && !value) {
                isValid = false;
                errorMessage = `${fieldName} is required`;
            }

            // Apply visual feedback
            if (isValid && value) {
                field.classList.add('field-success');
            } else if (!isValid) {
                field.classList.add('field-error');

                const errorElement = document.createElement('div');
                errorElement.className = 'field-error-message';
                errorElement.style.cssText = `
                    color: #ef4444;
                    font-size: 0.85rem;
                    margin-top: 4px;
                    display: flex;
                    align-items: center;
                    gap: 4px;
                `;
                errorElement.innerHTML = `<span>⚠️</span> ${errorMessage}`;
                field.parentNode.appendChild(errorElement);
            }

            return { isValid, errorMessage };
        }

        function validateForm(form) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;
            const errors = [];

            requiredFields.forEach(field => {
                const result = validateField(field, { required: true });
                if (!result.isValid) {
                    isValid = false;
                    errors.push(result.errorMessage);
                }
            });

            if (!isValid) {
                showNotification(
                    `Please fix the following issues:<br><br>• ${errors.join('<br>• ')}`,
                    'error',
                    6000
                );
            }

            return isValid;
        }

        // Enhanced form submission handling
        function initializeFormHandling() {
            const forms = document.querySelectorAll('form');

            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.classList.add('loading');
                        submitBtn.disabled = true;
                    }

                    if (validateForm(form)) {
                        showNotification('Validation successful! Saving configuration...', 'success', 3000);

                        setTimeout(() => {
                            form.submit();
                        }, 1000);
                    } else {
                        if (submitBtn) {
                            submitBtn.classList.remove('loading');
                            submitBtn.disabled = false;
                        }
                    }
                });
            });

            // Real-time validation
            const formInputs = document.querySelectorAll('form input, form select, form textarea');
            formInputs.forEach(input => {
                input.addEventListener('blur', function() {
                    if (this.hasAttribute('required')) {
                        validateField(this, { required: true });
                    }
                });
            });
        }

        // Auto-save functionality
        function initializeAutoSave() {
            let autoSaveTimeout;
            const formInputs = document.querySelectorAll('form input, form select, form textarea');

            formInputs.forEach(input => {
                input.addEventListener('change', function() {
                    clearTimeout(autoSaveTimeout);
                    autoSaveTimeout = setTimeout(() => {
                        console.log('Auto-save triggered');
                        showNotification('Changes saved automatically', 'info', 2000);
                    }, 3000);
                });
            });
        }

        // Utility function to show notifications
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type}`;
            notification.style.position = 'fixed';
            notification.style.top = '20px';
            notification.style.right = '20px';
            notification.style.zIndex = '10000';
            notification.style.minWidth = '300px';
            notification.innerHTML = `
                <i class="icon-${type}"></i>
                ${message}
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 4000);
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+S to save current form
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                const activeForm = document.querySelector('form:focus-within');
                if (activeForm && validateForm(activeForm)) {
                    activeForm.submit();
                }
            }
        });
    </script>

    <style>
        /* Additional icon styles */
        .icon-users::before { content: "👥"; }
        .icon-publish::before { content: "📢"; }
        .icon-play::before { content: "▶️"; }
        .icon-score::before { content: "📊"; }
        .icon-arrow-left::before { content: "←"; }
    </style>
</body>
</html>
