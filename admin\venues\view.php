<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Venues Management - View Venue Details
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication
requireAuth();

$venueId = (int)($_GET['id'] ?? 0);

if (!$venueId) {
    header('Location: index.php?message=Invalid venue ID&type=error');
    exit;
}

// Get venue details
$venue = fetchOne("SELECT * FROM venues WHERE venue_id = ?", [$venueId]);

if (!$venue) {
    header('Location: index.php?message=Venue not found&type=error');
    exit;
}

// Get venue statistics
$stats = fetchOne("
    SELECT 
        (SELECT COUNT(*) FROM matches WHERE venue_id = ?) as total_matches,
        (SELECT COUNT(*) FROM matches WHERE venue_id = ? AND status = 'scheduled') as scheduled_matches,
        (SELECT COUNT(*) FROM matches WHERE venue_id = ? AND status = 'ongoing') as ongoing_matches,
        (SELECT COUNT(*) FROM matches WHERE venue_id = ? AND status = 'completed') as completed_matches,
        (SELECT COUNT(*) FROM matches WHERE venue_id = ? AND match_date = CURDATE()) as today_matches
", [$venueId, $venueId, $venueId, $venueId, $venueId]);

// Get recent matches
$recentMatches = fetchAll("
    SELECT m.*, s.name as sport_name, e.name as event_name
    FROM matches m
    JOIN sports s ON m.sport_id = s.sport_id
    JOIN events e ON m.event_id = e.event_id
    WHERE m.venue_id = ?
    ORDER BY m.match_date DESC, m.match_time DESC
    LIMIT 10
", [$venueId]);

// Get upcoming matches
$upcomingMatches = fetchAll("
    SELECT m.*, s.name as sport_name, e.name as event_name
    FROM matches m
    JOIN sports s ON m.sport_id = s.sport_id
    JOIN events e ON m.event_id = e.event_id
    WHERE m.venue_id = ? AND m.match_date >= CURDATE() AND m.status = 'scheduled'
    ORDER BY m.match_date ASC, m.match_time ASC
    LIMIT 10
", [$venueId]);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($venue['name']); ?> - Venue Details - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>
    
    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1><?php echo htmlspecialchars($venue['name']); ?></h1>
                <nav class="breadcrumb">
                    <a href="../dashboard.php">Dashboard</a>
                    <span>/</span>
                    <a href="index.php">Venues</a>
                    <span>/</span>
                    <span>View Details</span>
                </nav>
            </div>
            <div class="page-actions">
                <a href="edit.php?id=<?php echo $venue['venue_id']; ?>" class="btn btn-primary">
                    <i class="icon-edit"></i>
                    Edit Venue
                </a>
                <a href="index.php" class="btn btn-outline">
                    <i class="icon-arrow-left"></i>
                    Back to Venues
                </a>
            </div>
        </div>
        
        <!-- Venue Information -->
        <div class="info-container">
            <div class="info-card">
                <div class="info-header">
                    <h2>Venue Information</h2>
                    <span class="status-badge status-<?php echo $venue['status']; ?>">
                        <?php echo ucfirst($venue['status']); ?>
                    </span>
                </div>
                <div class="info-content">
                    <div class="info-grid">
                        <div class="info-item">
                            <label>Name:</label>
                            <span><?php echo htmlspecialchars($venue['name']); ?></span>
                        </div>
                        
                        <?php if ($venue['location']): ?>
                        <div class="info-item">
                            <label>Location:</label>
                            <span><?php echo htmlspecialchars($venue['location']); ?></span>
                        </div>
                        <?php endif; ?>
                        
                        <div class="info-item">
                            <label>Capacity:</label>
                            <span><?php echo number_format($venue['capacity']); ?> people</span>
                        </div>
                        
                        <div class="info-item">
                            <label>Status:</label>
                            <span class="status-badge status-<?php echo $venue['status']; ?>">
                                <?php echo ucfirst($venue['status']); ?>
                            </span>
                        </div>
                        
                        <div class="info-item">
                            <label>Created:</label>
                            <span><?php echo formatDate($venue['created_at']); ?></span>
                        </div>
                        
                        <div class="info-item">
                            <label>Last Updated:</label>
                            <span><?php echo formatDate($venue['updated_at']); ?></span>
                        </div>
                    </div>
                    
                    <?php if ($venue['description']): ?>
                    <div class="info-description">
                        <label>Description:</label>
                        <p><?php echo nl2br(htmlspecialchars($venue['description'])); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Statistics -->
            <div class="stats-card">
                <div class="stats-header">
                    <h2>Match Statistics</h2>
                </div>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-value"><?php echo $stats['total_matches']; ?></span>
                        <span class="stat-label">Total Matches</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value"><?php echo $stats['scheduled_matches']; ?></span>
                        <span class="stat-label">Scheduled</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value"><?php echo $stats['ongoing_matches']; ?></span>
                        <span class="stat-label">Ongoing</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value"><?php echo $stats['completed_matches']; ?></span>
                        <span class="stat-label">Completed</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value"><?php echo $stats['today_matches']; ?></span>
                        <span class="stat-label">Today</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Matches -->
        <div class="matches-container">
            <!-- Upcoming Matches -->
            <div class="matches-card">
                <div class="matches-header">
                    <h2>Upcoming Matches</h2>
                    <span class="badge"><?php echo count($upcomingMatches); ?></span>
                </div>
                <div class="matches-content">
                    <?php if (empty($upcomingMatches)): ?>
                        <p class="no-data">No upcoming matches scheduled.</p>
                    <?php else: ?>
                        <div class="matches-list">
                            <?php foreach ($upcomingMatches as $match): ?>
                                <div class="match-item">
                                    <div class="match-info">
                                        <h4><?php echo htmlspecialchars($match['sport_name']); ?></h4>
                                        <p><?php echo htmlspecialchars($match['event_name']); ?></p>
                                        <small>
                                            <?php echo formatDate($match['match_date']); ?> at 
                                            <?php echo formatTime($match['match_time']); ?>
                                        </small>
                                    </div>
                                    <div class="match-status">
                                        <span class="status-badge status-<?php echo $match['status']; ?>">
                                            <?php echo ucfirst($match['status']); ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Recent Matches -->
            <div class="matches-card">
                <div class="matches-header">
                    <h2>Recent Matches</h2>
                    <span class="badge"><?php echo count($recentMatches); ?></span>
                </div>
                <div class="matches-content">
                    <?php if (empty($recentMatches)): ?>
                        <p class="no-data">No recent matches found.</p>
                    <?php else: ?>
                        <div class="matches-list">
                            <?php foreach ($recentMatches as $match): ?>
                                <div class="match-item">
                                    <div class="match-info">
                                        <h4><?php echo htmlspecialchars($match['sport_name']); ?></h4>
                                        <p><?php echo htmlspecialchars($match['event_name']); ?></p>
                                        <small>
                                            <?php echo formatDate($match['match_date']); ?> at 
                                            <?php echo formatTime($match['match_time']); ?>
                                        </small>
                                    </div>
                                    <div class="match-status">
                                        <span class="status-badge status-<?php echo $match['status']; ?>">
                                            <?php echo ucfirst($match['status']); ?>
                                        </span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>
    
    <script src="../../assets/js/admin.js"></script>
</body>
</html>
