<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Reports Dashboard
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication
requireAuth();

// Get summary statistics for reports
$totalEvents = fetchOne("SELECT COUNT(*) as count FROM events")['count'];
$totalMatches = fetchOne("SELECT COUNT(*) as count FROM matches")['count'];
$completedMatches = fetchOne("SELECT COUNT(*) as count FROM matches WHERE status = 'completed'")['count'];
$totalDepartments = fetchOne("SELECT COUNT(*) as count FROM departments WHERE status = 'active'")['count'];

// Get recent activity
$recentMatches = fetchAll("
    SELECT m.match_date, m.match_time, s.name as sport_name, e.name as event_name, m.status
    FROM matches m
    JOIN sports s ON m.sport_id = s.sport_id
    JOIN events e ON m.event_id = e.event_id
    ORDER BY m.match_date DESC, m.match_time DESC
    LIMIT 5
");
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports Dashboard - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>
    
    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1>Reports Dashboard</h1>
                <nav class="breadcrumb">
                    <a href="../dashboard.php">Dashboard</a>
                    <span>/</span>
                    <span>Reports</span>
                </nav>
            </div>
        </div>
        
        <!-- Statistics Overview -->
        <div class="stats-container">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="icon-calendar"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($totalEvents); ?></h3>
                        <p>Total Events</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="icon-play"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($totalMatches); ?></h3>
                        <p>Total Matches</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="icon-check"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($completedMatches); ?></h3>
                        <p>Completed Matches</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="icon-building"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($totalDepartments); ?></h3>
                        <p>Active Departments</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Available Reports -->
        <div class="reports-container">
            <h2>Available Reports</h2>
            
            <div class="reports-grid">
                <a href="standings.php" class="report-card">
                    <div class="report-icon">
                        <i class="icon-trophy"></i>
                    </div>
                    <div class="report-content">
                        <h3>Department Standings</h3>
                        <p>View current department rankings and medal counts</p>
                        <span class="report-status available">Available</span>
                    </div>
                </a>
                
                <div class="report-card coming-soon">
                    <div class="report-icon">
                        <i class="icon-chart"></i>
                    </div>
                    <div class="report-content">
                        <h3>Performance Analytics</h3>
                        <p>Detailed performance analysis and trends</p>
                        <span class="report-status coming-soon">Coming Soon</span>
                    </div>
                </div>
                
                <div class="report-card coming-soon">
                    <div class="report-icon">
                        <i class="icon-calendar"></i>
                    </div>
                    <div class="report-content">
                        <h3>Event Summary</h3>
                        <p>Comprehensive event reports and statistics</p>
                        <span class="report-status coming-soon">Coming Soon</span>
                    </div>
                </div>
                
                <div class="report-card coming-soon">
                    <div class="report-icon">
                        <i class="icon-users"></i>
                    </div>
                    <div class="report-content">
                        <h3>Participation Report</h3>
                        <p>Student participation rates and demographics</p>
                        <span class="report-status coming-soon">Coming Soon</span>
                    </div>
                </div>
                
                <a href="../system_health.php" class="report-card">
                    <div class="report-icon">
                        <i class="icon-heart"></i>
                    </div>
                    <div class="report-content">
                        <h3>System Health</h3>
                        <p>System diagnostics and health monitoring</p>
                        <span class="report-status available">Available</span>
                    </div>
                </a>
                
                <div class="report-card coming-soon">
                    <div class="report-icon">
                        <i class="icon-download"></i>
                    </div>
                    <div class="report-content">
                        <h3>Export Tools</h3>
                        <p>Export data to Excel, PDF, and other formats</p>
                        <span class="report-status coming-soon">Coming Soon</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Recent Activity -->
        <div class="activity-container">
            <h2>Recent Match Activity</h2>
            
            <?php if (empty($recentMatches)): ?>
                <div class="no-data">
                    <p>No recent match activity to display.</p>
                </div>
            <?php else: ?>
                <div class="activity-list">
                    <?php foreach ($recentMatches as $match): ?>
                        <div class="activity-item">
                            <div class="activity-info">
                                <h4><?php echo htmlspecialchars($match['sport_name']); ?></h4>
                                <p><?php echo htmlspecialchars($match['event_name']); ?></p>
                                <small>
                                    <?php echo formatDate($match['match_date']); ?> at 
                                    <?php echo formatTime($match['match_time']); ?>
                                </small>
                            </div>
                            <div class="activity-status">
                                <span class="status-badge status-<?php echo $match['status']; ?>">
                                    <?php echo ucfirst($match['status']); ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Quick Actions -->
        <div class="quick-actions">
            <h2>Quick Actions</h2>
            <div class="action-buttons">
                <a href="standings.php" class="btn btn-primary">
                    <i class="icon-trophy"></i>
                    View Standings
                </a>
                <a href="../system_health.php" class="btn btn-secondary">
                    <i class="icon-heart"></i>
                    System Health
                </a>
                <a href="../dashboard.php" class="btn btn-outline">
                    <i class="icon-dashboard"></i>
                    Back to Dashboard
                </a>
            </div>
        </div>
    </main>
    
    <script src="../../assets/js/admin.js"></script>
</body>
</html>
