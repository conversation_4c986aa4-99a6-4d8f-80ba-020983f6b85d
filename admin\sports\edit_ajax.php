<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Sports Management - Edit Sport AJAX Handler
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication
requireAuth();

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('Invalid security token');
        }
        
        $sportId = (int)($_POST['sport_id'] ?? 0);
        $name = sanitizeInput($_POST['name'] ?? '');
        $category = sanitizeInput($_POST['category'] ?? '');
        $scoring_type = sanitizeInput($_POST['scoring_type'] ?? '');
        $description = sanitizeInput($_POST['description'] ?? '');
        $rules = sanitizeInput($_POST['rules'] ?? '');
        $status = sanitizeInput($_POST['status'] ?? 'active');
        
        // Validation
        if (!$sportId) {
            throw new Exception('Invalid sport ID');
        }
        
        if (empty($name)) {
            throw new Exception('Sport name is required');
        }
        
        if (!in_array($category, ['individual', 'team', 'performing_arts', 'academic', 'pageant'])) {
            throw new Exception('Invalid category selected');
        }
        
        if (!in_array($scoring_type, ['points', 'time', 'distance', 'subjective'])) {
            throw new Exception('Invalid scoring type selected');
        }
        
        // Check if sport exists
        $sport = fetchOne("SELECT * FROM sports WHERE sport_id = ?", [$sportId]);
        if (!$sport) {
            throw new Exception('Sport not found');
        }
        
        // Check if name already exists (excluding current sport)
        $existing = fetchOne("SELECT sport_id FROM sports WHERE name = ? AND sport_id != ?", [$name, $sportId]);
        if ($existing) {
            throw new Exception('A sport with this name already exists');
        }
        
        // Update sport
        $sportData = [
            'name' => $name,
            'category' => $category,
            'scoring_type' => $scoring_type,
            'description' => $description,
            'rules' => $rules,
            'status' => $status
        ];
        
        updateRecord('sports', $sportData, 'sport_id = :sport_id', ['sport_id' => $sportId]);
        
        logActivity('sport_updated', "Sport '{$name}' updated (ID: {$sportId})");
        
        echo json_encode(['success' => true, 'message' => 'Sport updated successfully']);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}

// GET request - return edit form
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $sportId = (int)($_GET['id'] ?? 0);
    
    if (!$sportId) {
        echo '<div class="alert alert-error">Invalid sport ID</div>';
        exit;
    }
    
    $sport = fetchOne("SELECT * FROM sports WHERE sport_id = ?", [$sportId]);
    if (!$sport) {
        echo '<div class="alert alert-error">Sport not found</div>';
        exit;
    }
    
    $csrfToken = generateCSRFToken();
?>

<form id="editSportForm">
    <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
    <input type="hidden" name="sport_id" value="<?php echo $sport['sport_id']; ?>">
    
    <div id="editSportErrors"></div>
    
    <div class="form-section">
        <h4>Basic Information</h4>
        
        <div class="form-group">
            <label for="edit_name" class="form-label required">Sport Name</label>
            <input type="text" id="edit_name" name="name" class="form-control" 
                   value="<?php echo htmlspecialchars($sport['name']); ?>" 
                   placeholder="Enter sport name" required>
        </div>
        
        <div class="form-row">
            <div class="form-group">
                <label for="edit_category" class="form-label required">Category</label>
                <div class="custom-select" data-name="category" data-required="true">
                    <div class="select-trigger" tabindex="0" role="combobox" aria-expanded="false" aria-haspopup="listbox">
                        <span class="select-value"><?php
                            switch($sport['category']) {
                                case 'individual': echo 'Individual Sports'; break;
                                case 'team': echo 'Team Sports'; break;
                                case 'performing_arts': echo 'Performing Arts'; break;
                                case 'academic': echo 'Academic'; break;
                                default: echo 'Select Category';
                            }
                        ?></span>
                        <i class="select-arrow">▼</i>
                    </div>
                    <div class="select-options" role="listbox">
                        <div class="select-option" data-value="" role="option">Select Category</div>
                        <div class="select-option tooltip-trigger <?php echo $sport['category'] === 'individual' ? 'selected' : ''; ?>"
                             data-value="individual" role="option"
                             data-tooltip="Sports with single participants competing independently">
                            Individual Sports
                        </div>
                        <div class="select-option tooltip-trigger <?php echo $sport['category'] === 'team' ? 'selected' : ''; ?>"
                             data-value="team" role="option"
                             data-tooltip="Sports requiring multiple participants working together">
                            Team Sports
                        </div>
                        <div class="select-option tooltip-trigger <?php echo $sport['category'] === 'performing_arts' ? 'selected' : ''; ?>"
                             data-value="performing_arts" role="option"
                             data-tooltip="Creative competitions like dance, music, and drama">
                            Performing Arts
                        </div>
                        <div class="select-option tooltip-trigger <?php echo $sport['category'] === 'academic' ? 'selected' : ''; ?>"
                             data-value="academic" role="option"
                             data-tooltip="Knowledge-based competitions like quiz bowl and debate">
                            Academic
                        </div>
                    </div>
                    <input type="hidden" name="category" id="edit_category" value="<?php echo htmlspecialchars($sport['category']); ?>" required>
                </div>
            </div>

            <div class="form-group">
                <label for="edit_scoring_type" class="form-label required">Scoring Type</label>
                <div class="custom-select" data-name="scoring_type" data-required="true">
                    <div class="select-trigger" tabindex="0" role="combobox" aria-expanded="false" aria-haspopup="listbox">
                        <span class="select-value"><?php echo ucfirst($sport['scoring_type']); ?></span>
                        <i class="select-arrow">▼</i>
                    </div>
                    <div class="select-options" role="listbox">
                        <div class="select-option" data-value="" role="option">Select Scoring Type</div>
                        <div class="select-option tooltip-trigger <?php echo $sport['scoring_type'] === 'points' ? 'selected' : ''; ?>"
                             data-value="points" role="option"
                             data-tooltip="Scoring based on accumulated points (e.g., basketball scores, quiz points)">
                            Points
                        </div>
                        <div class="select-option tooltip-trigger <?php echo $sport['scoring_type'] === 'time' ? 'selected' : ''; ?>"
                             data-value="time" role="option"
                             data-tooltip="Scoring based on completion time (e.g., track and field, swimming)">
                            Time
                        </div>
                        <div class="select-option tooltip-trigger <?php echo $sport['scoring_type'] === 'distance' ? 'selected' : ''; ?>"
                             data-value="distance" role="option"
                             data-tooltip="Scoring based on distance achieved (e.g., shot put, long jump)">
                            Distance
                        </div>
                        <div class="select-option tooltip-trigger <?php echo $sport['scoring_type'] === 'subjective' ? 'selected' : ''; ?>"
                             data-value="subjective" role="option"
                             data-tooltip="Scoring based on judges' evaluation (e.g., dance, debate)">
                            Subjective
                        </div>
                    </div>
                    <input type="hidden" name="scoring_type" id="edit_scoring_type" value="<?php echo htmlspecialchars($sport['scoring_type']); ?>" required>
                </div>
            </div>
        </div>

        <div class="form-group">
            <label for="edit_status" class="form-label">Status</label>
            <div class="custom-select" data-name="status">
                <div class="select-trigger" tabindex="0" role="combobox" aria-expanded="false" aria-haspopup="listbox">
                    <span class="select-value"><?php echo ucfirst($sport['status']); ?></span>
                    <i class="select-arrow">▼</i>
                </div>
                <div class="select-options" role="listbox">
                    <div class="select-option tooltip-trigger <?php echo $sport['status'] === 'active' ? 'selected' : ''; ?>"
                         data-value="active" role="option"
                         data-tooltip="Sport is available for event registration">
                        Active
                    </div>
                    <div class="select-option tooltip-trigger <?php echo $sport['status'] === 'inactive' ? 'selected' : ''; ?>"
                         data-value="inactive" role="option"
                         data-tooltip="Sport is temporarily disabled and unavailable">
                        Inactive
                    </div>
                </div>
                <input type="hidden" name="status" id="edit_status" value="<?php echo htmlspecialchars($sport['status']); ?>">
            </div>
        </div>
    </div>
    
    <div class="form-section">
        <h4>Description & Rules</h4>
        
        <div class="form-group">
            <label for="edit_description" class="form-label">Description</label>
            <textarea id="edit_description" name="description" class="form-control" rows="3" 
                      placeholder="Brief description of the sport"><?php echo htmlspecialchars($sport['description']); ?></textarea>
        </div>
        
        <div class="form-group">
            <label for="edit_rules" class="form-label">Rules & Regulations</label>
            <textarea id="edit_rules" name="rules" class="form-control" rows="4" 
                      placeholder="Detailed rules and regulations for this sport"><?php echo htmlspecialchars($sport['rules']); ?></textarea>
        </div>
    </div>
    
    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" onclick="closeModal('editSportModal')">Cancel</button>
        <button type="submit" class="btn btn-primary">
            <i class="icon-save"></i>
            Save Changes
        </button>
    </div>
</form>

<?php
}
?>
