<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Tournament Management Database Tables Creation
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication and admin permission
requireAuth();
requirePermission('manage_system');

$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $pdo = getDB()->getConnection();
        
        // Create tournament_configs table
        $sql = "
        CREATE TABLE IF NOT EXISTS tournament_configs (
            config_id INT AUTO_INCREMENT PRIMARY KEY,
            event_sport_id INT NOT NULL,
            tournament_format ENUM('single_elimination', 'double_elimination', 'round_robin', 'swiss_system', 'group_knockout', 'custom') NOT NULL,
            bracket_size INT NOT NULL DEFAULT 8,
            seeding_type ENUM('random', 'manual', 'ranked') NOT NULL DEFAULT 'random',
            scoring_config JSON,
            schedule_config JSON,
            bracket_data JSON,
            status ENUM('draft', 'configured', 'active', 'completed') NOT NULL DEFAULT 'draft',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (event_sport_id) REFERENCES event_sports(event_sport_id) ON DELETE CASCADE,
            UNIQUE KEY unique_event_sport (event_sport_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($sql);
        
        // Create tournament_brackets table
        $sql = "
        CREATE TABLE IF NOT EXISTS tournament_brackets (
            bracket_id INT AUTO_INCREMENT PRIMARY KEY,
            config_id INT NOT NULL,
            bracket_type ENUM('main', 'winners', 'losers', 'group') NOT NULL DEFAULT 'main',
            bracket_data JSON NOT NULL,
            round_count INT NOT NULL DEFAULT 1,
            status ENUM('pending', 'active', 'completed') NOT NULL DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (config_id) REFERENCES tournament_configs(config_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($sql);
        
        // Create tournament_matches table (enhanced)
        $sql = "
        CREATE TABLE IF NOT EXISTS tournament_matches (
            match_id INT AUTO_INCREMENT PRIMARY KEY,
            bracket_id INT NOT NULL,
            round_number INT NOT NULL,
            match_number INT NOT NULL,
            participant1_id INT,
            participant2_id INT,
            winner_id INT,
            score1 VARCHAR(50),
            score2 VARCHAR(50),
            match_date DATE,
            match_time TIME,
            venue_id INT,
            status ENUM('scheduled', 'in_progress', 'completed', 'cancelled') NOT NULL DEFAULT 'scheduled',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (bracket_id) REFERENCES tournament_brackets(bracket_id) ON DELETE CASCADE,
            FOREIGN KEY (participant1_id) REFERENCES participants(participant_id) ON DELETE SET NULL,
            FOREIGN KEY (participant2_id) REFERENCES participants(participant_id) ON DELETE SET NULL,
            FOREIGN KEY (winner_id) REFERENCES participants(participant_id) ON DELETE SET NULL,
            FOREIGN KEY (venue_id) REFERENCES venues(venue_id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($sql);
        
        // Create tournament_standings table
        $sql = "
        CREATE TABLE IF NOT EXISTS tournament_standings (
            standing_id INT AUTO_INCREMENT PRIMARY KEY,
            config_id INT NOT NULL,
            participant_id INT NOT NULL,
            position INT NOT NULL,
            points DECIMAL(10,2) NOT NULL DEFAULT 0,
            wins INT NOT NULL DEFAULT 0,
            losses INT NOT NULL DEFAULT 0,
            draws INT NOT NULL DEFAULT 0,
            goals_for INT NOT NULL DEFAULT 0,
            goals_against INT NOT NULL DEFAULT 0,
            goal_difference INT GENERATED ALWAYS AS (goals_for - goals_against) STORED,
            matches_played INT GENERATED ALWAYS AS (wins + losses + draws) STORED,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (config_id) REFERENCES tournament_configs(config_id) ON DELETE CASCADE,
            FOREIGN KEY (participant_id) REFERENCES participants(participant_id) ON DELETE CASCADE,
            UNIQUE KEY unique_config_participant (config_id, participant_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($sql);
        
        // Create tournament_judges table (for subjective scoring)
        $sql = "
        CREATE TABLE IF NOT EXISTS tournament_judges (
            judge_id INT AUTO_INCREMENT PRIMARY KEY,
            config_id INT NOT NULL,
            admin_id INT NOT NULL,
            judge_name VARCHAR(255) NOT NULL,
            specialization VARCHAR(255),
            status ENUM('active', 'inactive') NOT NULL DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (config_id) REFERENCES tournament_configs(config_id) ON DELETE CASCADE,
            FOREIGN KEY (admin_id) REFERENCES admin_users(admin_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($sql);
        
        // Create match_scores table (for detailed scoring)
        $sql = "
        CREATE TABLE IF NOT EXISTS match_scores (
            score_id INT AUTO_INCREMENT PRIMARY KEY,
            match_id INT NOT NULL,
            participant_id INT NOT NULL,
            judge_id INT,
            score_type ENUM('final', 'round', 'criteria') NOT NULL DEFAULT 'final',
            score_value DECIMAL(10,2) NOT NULL,
            criteria_name VARCHAR(100),
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (match_id) REFERENCES tournament_matches(match_id) ON DELETE CASCADE,
            FOREIGN KEY (participant_id) REFERENCES participants(participant_id) ON DELETE CASCADE,
            FOREIGN KEY (judge_id) REFERENCES tournament_judges(judge_id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($sql);
        
        // Create tournament_templates table (for reusable configurations)
        $sql = "
        CREATE TABLE IF NOT EXISTS tournament_templates (
            template_id INT AUTO_INCREMENT PRIMARY KEY,
            template_name VARCHAR(255) NOT NULL,
            sport_category ENUM('individual', 'team', 'performing_arts', 'academic') NOT NULL,
            tournament_format ENUM('single_elimination', 'double_elimination', 'round_robin', 'swiss_system', 'group_knockout', 'custom') NOT NULL,
            bracket_size INT NOT NULL,
            seeding_type ENUM('random', 'manual', 'ranked') NOT NULL DEFAULT 'random',
            scoring_config JSON,
            schedule_config JSON,
            description TEXT,
            created_by INT NOT NULL,
            is_public BOOLEAN NOT NULL DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES admin_users(admin_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($sql);
        
        $message = 'Tournament management tables created successfully!';
        $messageType = 'success';
        
    } catch (Exception $e) {
        $message = 'Error creating tables: ' . $e->getMessage();
        $messageType = 'error';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Tournament Tables - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>
    
    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1>Create Tournament Management Tables</h1>
                <nav class="breadcrumb">
                    <a href="../dashboard.php">Dashboard</a>
                    <span>/</span>
                    <a href="index.php">Settings</a>
                    <span>/</span>
                    <span>Tournament Tables</span>
                </nav>
            </div>
        </div>
        
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <div class="card">
            <div class="card-header">
                <h2>Tournament Management Database Setup</h2>
            </div>
            <div class="card-content">
                <p>This will create the necessary database tables for the tournament management system:</p>
                <ul>
                    <li><strong>tournament_configs</strong> - Tournament configuration settings</li>
                    <li><strong>tournament_brackets</strong> - Bracket structures and data</li>
                    <li><strong>tournament_matches</strong> - Enhanced match management</li>
                    <li><strong>tournament_standings</strong> - Tournament standings and statistics</li>
                    <li><strong>tournament_judges</strong> - Judge assignments for subjective scoring</li>
                    <li><strong>match_scores</strong> - Detailed scoring data</li>
                    <li><strong>tournament_templates</strong> - Reusable tournament templates</li>
                </ul>
                
                <form method="POST" onsubmit="return confirm('Create tournament management tables?')">
                    <button type="submit" class="btn btn-primary">
                        <i class="icon-database"></i>
                        Create Tables
                    </button>
                </form>
            </div>
        </div>
    </main>
    
    <script src="../../assets/js/admin.js"></script>
</body>
</html>
