<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Participants Management - Coming Soon
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication
requireAuth();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Participants Management - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>
    
    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1>Participants Management</h1>
                <nav class="breadcrumb">
                    <a href="../dashboard.php">Dashboard</a>
                    <span>/</span>
                    <span>Participants</span>
                </nav>
            </div>
        </div>
        
        <div class="coming-soon">
            <div class="coming-soon-content">
                <h2>👥 Participants Management</h2>
                <p>This module is currently under development and will be available in a future update.</p>
                
                <div class="planned-features">
                    <h3>Planned Features:</h3>
                    <ul>
                        <li>✅ Student registration system</li>
                        <li>✅ Department assignment</li>
                        <li>✅ Sport participation tracking</li>
                        <li>✅ Medical clearance management</li>
                        <li>✅ Performance history</li>
                        <li>✅ Bulk import/export</li>
                    </ul>
                </div>
                
                <div class="alternative-actions">
                    <h3>In the meantime, you can:</h3>
                    <div class="action-cards">
                        <a href="../departments/" class="action-card">
                            <i class="icon-building"></i>
                            <h4>Manage Departments</h4>
                            <p>Organize department structure</p>
                        </a>
                        <a href="../matches/" class="action-card">
                            <i class="icon-play"></i>
                            <h4>Schedule Matches</h4>
                            <p>Create and manage matches</p>
                        </a>
                        <a href="../reports/standings.php" class="action-card">
                            <i class="icon-chart"></i>
                            <h4>View Standings</h4>
                            <p>Check department rankings</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <script src="../../assets/js/admin.js"></script>
</body>
</html>
