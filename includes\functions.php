<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Common Functions Library
 *
 * @version 1.0
 * <AUTHOR> Development Team
 */

// Prevent direct access
if (!defined('SCIMS_ACCESS')) {
    die('Direct access not permitted');
}

/**
 * Sanitize input data
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate email address
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Validate phone number (Philippine format)
 */
function isValidPhone($phone) {
    $pattern = '/^(\+63|0)?[0-9]{10}$/';
    return preg_match($pattern, $phone);
}

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time']) || 
        (time() - $_SESSION['csrf_token_time']) > CSRF_TOKEN_EXPIRE) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        $_SESSION['csrf_token_time'] = time();
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && 
           isset($_SESSION['csrf_token_time']) &&
           (time() - $_SESSION['csrf_token_time']) <= CSRF_TOKEN_EXPIRE &&
           hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Redirect with message
 */
function redirect($url, $message = null, $type = 'info') {
    if ($message) {
        $_SESSION['flash_message'] = $message;
        $_SESSION['flash_type'] = $type;
    }
    header("Location: $url");
    exit();
}

/**
 * Display flash message
 */
function displayFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        $type = $_SESSION['flash_type'] ?? 'info';
        unset($_SESSION['flash_message'], $_SESSION['flash_type']);
        
        $alertClass = [
            'success' => 'alert-success',
            'error' => 'alert-error',
            'warning' => 'alert-warning',
            'info' => 'alert-info'
        ][$type] ?? 'alert-info';
        
        echo "<div class='alert {$alertClass}'>{$message}</div>";
    }
}

/**
 * Format date for display
 */
function formatDate($date, $format = 'M j, Y') {
    if (!$date) return '';
    return date($format, strtotime($date));
}

/**
 * Format time for display
 */
function formatTime($time, $format = 'g:i A') {
    if (!$time) return '';
    return date($format, strtotime($time));
}

/**
 * Get time ago string
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);

    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';

    return formatDate($datetime);
}

/**
 * Convert number to ordinal (1st, 2nd, 3rd, etc.)
 */
function ordinal($number) {
    $ends = array('th','st','nd','rd','th','th','th','th','th','th');
    if ((($number % 100) >= 11) && (($number % 100) <= 13)) {
        return $number . 'th';
    } else {
        return $number . $ends[$number % 10];
    }
}

/**
 * Format datetime for display
 */
function formatDateTime($datetime, $format = 'M j, Y g:i A') {
    if (!$datetime) return '';
    return date($format, strtotime($datetime));
}

/**
 * Calculate age from birthdate
 */
function calculateAge($birthdate) {
    return date_diff(date_create($birthdate), date_create('today'))->y;
}

/**
 * Generate random string
 */
function generateRandomString($length = 10) {
    return substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length/strlen($x)) )),1,$length);
}

/**
 * Upload file with validation
 */
function uploadFile($file, $uploadDir, $allowedTypes = null, $maxSize = null) {
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        throw new Exception('No file uploaded or invalid file');
    }
    
    $allowedTypes = $allowedTypes ?? ALLOWED_IMAGE_TYPES;
    $maxSize = $maxSize ?? UPLOAD_MAX_SIZE;
    
    // Check file size
    if ($file['size'] > $maxSize) {
        throw new Exception('File size exceeds maximum allowed size');
    }
    
    // Check file type
    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($fileExtension, $allowedTypes)) {
        throw new Exception('File type not allowed');
    }
    
    // Generate unique filename
    $filename = uniqid() . '.' . $fileExtension;
    $uploadPath = $uploadDir . $filename;
    
    // Create directory if it doesn't exist
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $uploadPath)) {
        throw new Exception('Failed to upload file');
    }
    
    return $filename;
}

/**
 * Delete file safely
 */
function deleteFile($filepath) {
    if (file_exists($filepath) && is_file($filepath)) {
        return unlink($filepath);
    }
    return false;
}

/**
 * Paginate results
 */
function paginate($totalRecords, $currentPage = 1, $recordsPerPage = null) {
    $recordsPerPage = $recordsPerPage ?? RECORDS_PER_PAGE;
    $totalPages = ceil($totalRecords / $recordsPerPage);
    $currentPage = max(1, min($currentPage, $totalPages));
    $offset = ($currentPage - 1) * $recordsPerPage;
    
    return [
        'total_records' => $totalRecords,
        'total_pages' => $totalPages,
        'current_page' => $currentPage,
        'records_per_page' => $recordsPerPage,
        'offset' => $offset,
        'has_previous' => $currentPage > 1,
        'has_next' => $currentPage < $totalPages,
        'previous_page' => $currentPage > 1 ? $currentPage - 1 : null,
        'next_page' => $currentPage < $totalPages ? $currentPage + 1 : null
    ];
}

/**
 * Generate pagination HTML
 */
function generatePaginationHTML($pagination, $baseUrl) {
    if ($pagination['total_pages'] <= 1) return '';
    
    $html = '<div class="pagination">';
    
    // Previous button
    if ($pagination['has_previous']) {
        $html .= "<a href='{$baseUrl}?page={$pagination['previous_page']}' class='page-link'>Previous</a>";
    }
    
    // Page numbers
    $start = max(1, $pagination['current_page'] - floor(MAX_PAGINATION_LINKS / 2));
    $end = min($pagination['total_pages'], $start + MAX_PAGINATION_LINKS - 1);
    
    if ($start > 1) {
        $html .= "<a href='{$baseUrl}?page=1' class='page-link'>1</a>";
        if ($start > 2) $html .= "<span class='page-ellipsis'>...</span>";
    }
    
    for ($i = $start; $i <= $end; $i++) {
        $activeClass = $i == $pagination['current_page'] ? 'active' : '';
        $html .= "<a href='{$baseUrl}?page={$i}' class='page-link {$activeClass}'>{$i}</a>";
    }
    
    if ($end < $pagination['total_pages']) {
        if ($end < $pagination['total_pages'] - 1) $html .= "<span class='page-ellipsis'>...</span>";
        $html .= "<a href='{$baseUrl}?page={$pagination['total_pages']}' class='page-link'>{$pagination['total_pages']}</a>";
    }
    
    // Next button
    if ($pagination['has_next']) {
        $html .= "<a href='{$baseUrl}?page={$pagination['next_page']}' class='page-link'>Next</a>";
    }
    
    $html .= '</div>';
    return $html;
}

/**
 * Log activity
 */
function logActivity($action, $details = null, $userId = null) {
    $userId = $userId ?? ($_SESSION['admin_id'] ?? null);
    $logData = [
        'user_id' => $userId,
        'action' => $action,
        'details' => $details,
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    // Log to file (you can also log to database)
    $logFile = dirname(__DIR__) . '/logs/activity.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $logEntry = date('Y-m-d H:i:s') . " - User: {$userId} - Action: {$action} - Details: {$details} - IP: {$logData['ip_address']}\n";
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}

/**
 * Check if user is logged in
 */
function isLoggedIn() {
    return isset($_SESSION['admin_id']) && isset($_SESSION['admin_username']);
}

/**
 * Get current user info
 */
function getCurrentUser() {
    if (!isLoggedIn()) return null;

    $sql = "SELECT admin_id, username, email, full_name, phone, role, status, password_hash,
                   created_at, updated_at, last_login, failed_login_attempts
            FROM admin_users WHERE admin_id = :id";
    return fetchOne($sql, ['id' => $_SESSION['admin_id']]);
}

/**
 * Check user permission
 */
function hasPermission($permission) {
    $user = getCurrentUser();
    if (!$user) return false;
    
    // Super admin has all permissions
    if ($user['role'] === 'super_admin') return true;
    
    // Define role permissions
    $permissions = [
        'admin' => ['view_dashboard', 'manage_events', 'manage_matches', 'manage_scores', 'view_reports'],
        'organizer' => ['view_dashboard', 'manage_scores', 'view_reports']
    ];
    
    return in_array($permission, $permissions[$user['role']] ?? []);
}

/**
 * Require permission
 */
function requirePermission($permission) {
    if (!hasPermission($permission)) {
        redirect('/admin/index.php', 'Access denied', 'error');
    }
}








?>
