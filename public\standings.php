<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Public Standings Page
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Get current event
$currentEvent = fetchOne("
    SELECT * FROM events 
    WHERE status IN ('ongoing', 'completed') 
    ORDER BY start_date DESC 
    LIMIT 1
");

// Get department standings
$standings = [];
if ($currentEvent) {
    $standings = fetchAll("
        SELECT d.dept_id, d.name, d.abbreviation, d.color_code,
               COALESCE(ds.total_points, 0) as total_points,
               COALESCE(ds.rank_position, 999) as rank_position,
               COALESCE(ds.medals_gold, 0) as medals_gold,
               COALESCE(ds.medals_silver, 0) as medals_silver,
               COALESCE(ds.medals_bronze, 0) as medals_bronze,
               COALESCE(ds.matches_played, 0) as matches_played,
               COALESCE(ds.matches_won, 0) as matches_won,
               COALESCE(ds.matches_lost, 0) as matches_lost,
               COALESCE(ds.matches_drawn, 0) as matches_drawn,
               ds.last_updated
        FROM departments d
        LEFT JOIN department_standings ds ON d.dept_id = ds.dept_id AND ds.event_id = ?
        WHERE d.status = 'active'
        ORDER BY ds.total_points DESC, ds.medals_gold DESC, ds.medals_silver DESC, ds.medals_bronze DESC, d.name ASC
    ", [$currentEvent['event_id']]);
}

// Get medal summary by sport
$medalsBySport = [];
if ($currentEvent) {
    $medalsBySport = fetchAll("
        SELECT s.name as sport_name,
               d.abbreviation as dept_abbrev,
               d.color_code,
               sc.position,
               COUNT(*) as medal_count
        FROM scores sc
        JOIN match_participants mp ON sc.participant_id = mp.participant_id
        JOIN matches m ON mp.match_id = m.match_id
        JOIN sports s ON m.sport_id = s.sport_id
        JOIN departments d ON mp.dept_id = d.dept_id
        WHERE m.event_id = ? AND sc.is_final = 1 AND sc.position <= 3 AND m.status = 'completed'
        GROUP BY s.sport_id, d.dept_id, sc.position
        ORDER BY s.name, sc.position, d.abbreviation
    ", [$currentEvent['event_id']]);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Department Standings - <?php echo APP_NAME; ?></title>
    <meta name="description" content="View current department standings and medal tallies for Samar College intramural sports.">
    
    <link rel="stylesheet" href="../assets/css/index.css">
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body>
    <!-- Header -->
    <header class="site-header">
        <div class="container">
            <div class="header-content">
                <a href="index.php" class="site-logo">
                    <img src="../assets/images/logo.png" alt="SCIMS Logo" onerror="this.style.display='none'">
                    <h1>SCIMS</h1>
                </a>
                
                <nav class="main-nav">
                    <ul class="nav-menu">
                        <li class="nav-item"><a href="index.php">Home</a></li>
                        <li class="nav-item"><a href="schedule.php">Schedule</a></li>
                        <li class="nav-item"><a href="standings.php" class="active">Standings</a></li>
                        <li class="nav-item"><a href="results.php">Results</a></li>
                    </ul>
                    
                    <button class="mobile-menu-toggle" aria-label="Toggle mobile menu">
                        ☰
                    </button>
                </nav>
            </div>
        </div>
    </header>
    
    <!-- Main Content -->
    <main>
        <section class="section">
            <div class="container">
                <div class="section-header">
                    <h1>Department Standings</h1>
                    <?php if ($currentEvent): ?>
                        <p><?php echo htmlspecialchars($currentEvent['name']); ?></p>
                        <?php if ($currentEvent['status'] === 'ongoing'): ?>
                            <div class="live-indicator">🔴 Live Updates</div>
                        <?php endif; ?>
                    <?php else: ?>
                        <p>No active events found</p>
                    <?php endif; ?>
                </div>
                
                <?php if (!empty($standings)): ?>
                    <!-- Overall Standings -->
                    <div class="standings-container">
                        <div class="table-container">
                            <table class="standings-table" data-live-update="/api/standings-updates.php">
                                <thead>
                                    <tr>
                                        <th class="rank-col">Rank</th>
                                        <th class="dept-col">Department</th>
                                        <th class="points-col">Points</th>
                                        <th class="medals-col">🥇</th>
                                        <th class="medals-col">🥈</th>
                                        <th class="medals-col">🥉</th>
                                        <th class="matches-col">Played</th>
                                        <th class="matches-col">Won</th>
                                        <th class="matches-col">Lost</th>
                                        <th class="matches-col">Drawn</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($standings as $index => $dept): ?>
                                        <tr class="dept-row" data-dept-id="<?php echo $dept['dept_id']; ?>">
                                            <td class="rank">
                                                <div class="rank-badge rank-<?php echo $index + 1; ?>">
                                                    <?php echo $index + 1; ?>
                                                </div>
                                            </td>
                                            <td class="dept-name">
                                                <div class="dept-info">
                                                    <div class="dept-color" style="background-color: <?php echo htmlspecialchars($dept['color_code']); ?>"></div>
                                                    <div>
                                                        <div class="dept-abbrev"><?php echo htmlspecialchars($dept['abbreviation']); ?></div>
                                                        <div class="dept-full-name"><?php echo htmlspecialchars($dept['name']); ?></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="points">
                                                <strong><?php echo number_format($dept['total_points'], 1); ?></strong>
                                            </td>
                                            <td class="medals">
                                                <span class="medal gold"><?php echo $dept['medals_gold']; ?></span>
                                            </td>
                                            <td class="medals">
                                                <span class="medal silver"><?php echo $dept['medals_silver']; ?></span>
                                            </td>
                                            <td class="medals">
                                                <span class="medal bronze"><?php echo $dept['medals_bronze']; ?></span>
                                            </td>
                                            <td class="matches"><?php echo $dept['matches_played']; ?></td>
                                            <td class="matches"><?php echo $dept['matches_won']; ?></td>
                                            <td class="matches"><?php echo $dept['matches_lost']; ?></td>
                                            <td class="matches"><?php echo $dept['matches_drawn']; ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <?php if ($standings[0]['last_updated']): ?>
                            <div class="last-updated">
                                Last updated: <?php echo formatDateTime($standings[0]['last_updated']); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Top 3 Podium -->
                    <div class="podium-container">
                        <h2>Top 3 Departments</h2>
                        <div class="podium">
                            <?php 
                            $topThree = array_slice($standings, 0, 3);
                            $podiumOrder = [1, 0, 2]; // 2nd, 1st, 3rd for visual effect
                            ?>
                            
                            <?php foreach ($podiumOrder as $index): ?>
                                <?php if (isset($topThree[$index])): ?>
                                    <?php $dept = $topThree[$index]; ?>
                                    <div class="podium-position position-<?php echo $index + 1; ?>">
                                        <div class="podium-dept">
                                            <div class="dept-color" style="background-color: <?php echo htmlspecialchars($dept['color_code']); ?>"></div>
                                            <h3><?php echo htmlspecialchars($dept['abbreviation']); ?></h3>
                                            <p><?php echo number_format($dept['total_points'], 1); ?> pts</p>
                                            <div class="medal-count">
                                                <span class="medal gold"><?php echo $dept['medals_gold']; ?></span>
                                                <span class="medal silver"><?php echo $dept['medals_silver']; ?></span>
                                                <span class="medal bronze"><?php echo $dept['medals_bronze']; ?></span>
                                            </div>
                                        </div>
                                        <div class="podium-base">
                                            <div class="position-number"><?php echo $index + 1; ?></div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <!-- Medal Summary by Sport -->
                    <?php if (!empty($medalsBySport)): ?>
                        <div class="medals-by-sport">
                            <h2>Medal Summary by Sport</h2>
                            <div class="sport-medals-grid">
                                <?php 
                                $sportGroups = [];
                                foreach ($medalsBySport as $medal) {
                                    $sportGroups[$medal['sport_name']][] = $medal;
                                }
                                ?>
                                
                                <?php foreach ($sportGroups as $sportName => $medals): ?>
                                    <div class="sport-medal-card">
                                        <h3><?php echo htmlspecialchars($sportName); ?></h3>
                                        <div class="sport-medals">
                                            <?php 
                                            $positions = [1 => [], 2 => [], 3 => []];
                                            foreach ($medals as $medal) {
                                                $positions[$medal['position']][] = $medal;
                                            }
                                            ?>
                                            
                                            <?php foreach ([1, 2, 3] as $position): ?>
                                                <div class="medal-position">
                                                    <div class="medal-icon">
                                                        <?php echo $position === 1 ? '🥇' : ($position === 2 ? '🥈' : '🥉'); ?>
                                                    </div>
                                                    <div class="medal-winners">
                                                        <?php foreach ($positions[$position] as $winner): ?>
                                                            <span class="winner" style="color: <?php echo htmlspecialchars($winner['color_code']); ?>">
                                                                <?php echo htmlspecialchars($winner['dept_abbrev']); ?>
                                                            </span>
                                                        <?php endforeach; ?>
                                                        <?php if (empty($positions[$position])): ?>
                                                            <span class="no-winner">-</span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                <?php else: ?>
                    <div class="no-data">
                        <h3>No standings available</h3>
                        <p>Standings will be displayed once matches begin.</p>
                        <a href="schedule.php" class="btn btn-primary">View Schedule</a>
                    </div>
                <?php endif; ?>
            </div>
        </section>
    </main>
    
    <!-- Footer -->
    <footer class="site-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>SCIMS</h3>
                    <p>Samar College Intramurals Management System</p>
                </div>
                
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.php">Home</a></li>
                        <li><a href="schedule.php">Schedule</a></li>
                        <li><a href="standings.php">Standings</a></li>
                        <li><a href="results.php">Results</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; <?php echo date('Y'); ?> Samar College. All rights reserved.</p>
            </div>
        </div>
    </footer>
    
    <script src="../assets/js/main.js"></script>
</body>
</html>
