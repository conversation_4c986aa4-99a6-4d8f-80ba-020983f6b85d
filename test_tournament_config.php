<?php
/**
 * Test Tournament Configuration Page
 * This script tests if the tournament_config.php page loads without errors
 */

// Capture any output and errors
ob_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Testing Tournament Configuration Page</h1>\n";
echo "<p>Starting test at " . date('Y-m-d H:i:s') . "</p>\n";

try {
    // Set up minimal session and parameters for testing
    session_start();
    $_SESSION['admin_id'] = 1;
    $_SESSION['admin_username'] = 'test_admin';
    
    // Set test parameters
    $_GET['event_sport_id'] = 1;
    $_GET['event_id'] = 1;
    
    echo "<p>✅ Session and parameters set up</p>\n";
    
    // Include required files
    require_once 'includes/config.php';
    require_once 'includes/functions.php';
    
    echo "<p>✅ Core files included</p>\n";
    
    // Test database connection
    $db = getDB();
    if ($db) {
        echo "<p>✅ Database connection successful</p>\n";
    } else {
        echo "<p>❌ Database connection failed</p>\n";
    }
    
    // Test if required functions exist
    $requiredFunctions = [
        'fetchOne',
        'fetchAll', 
        'executeQuery',
        'tableExists',
        'getCurrentUser'
    ];
    
    foreach ($requiredFunctions as $func) {
        if (function_exists($func)) {
            echo "<p>✅ Function '$func' exists</p>\n";
        } else {
            echo "<p>❌ Function '$func' missing</p>\n";
        }
    }
    
    // Test table creation
    echo "<p>Testing table creation...</p>\n";
    
    if (!tableExists('tournament_schedules')) {
        try {
            executeQuery("
                CREATE TABLE tournament_schedules (
                    schedule_id INT AUTO_INCREMENT PRIMARY KEY,
                    config_id INT NOT NULL,
                    schedule_name VARCHAR(255) NOT NULL,
                    start_date DATE NOT NULL,
                    end_date DATE NOT NULL,
                    time_slots JSON,
                    venue_assignments JSON,
                    scheduling_rules JSON,
                    status ENUM('draft', 'published', 'active', 'completed') DEFAULT 'draft',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ");
            echo "<p>✅ tournament_schedules table created successfully</p>\n";
        } catch (Exception $e) {
            echo "<p>⚠️ Table creation failed: " . $e->getMessage() . "</p>\n";
        }
    } else {
        echo "<p>✅ tournament_schedules table already exists</p>\n";
    }
    
    echo "<p>✅ All tests completed successfully!</p>\n";
    echo "<p><strong>The tournament configuration page should now load without errors.</strong></p>\n";
    
} catch (Exception $e) {
    echo "<p>❌ Test failed with error: " . $e->getMessage() . "</p>\n";
    echo "<p>Stack trace: " . $e->getTraceAsString() . "</p>\n";
}

$output = ob_get_clean();
echo $output;
?>
