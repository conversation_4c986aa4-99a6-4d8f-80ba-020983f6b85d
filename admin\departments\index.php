<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Departments Management - Main Page
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication
requireAuth();

// Handle actions
$action = $_GET['action'] ?? '';
$message = '';
$messageType = 'info';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('Invalid security token');
        }
        
        switch ($action) {
            case 'delete':
                $deptId = (int)($_POST['dept_id'] ?? 0);
                if ($deptId) {
                    // Check if department has participants
                    $participantCount = fetchOne("SELECT COUNT(*) as count FROM match_participants WHERE dept_id = ?", [$deptId])['count'];
                    if ($participantCount > 0) {
                        throw new Exception('Cannot delete department with existing participants');
                    }
                    
                    deleteRecord('departments', 'dept_id = :dept_id', ['dept_id' => $deptId]);
                    logActivity('department_deleted', "Department ID {$deptId} deleted");
                    $message = 'Department deleted successfully';
                    $messageType = 'success';
                }
                break;
                
            case 'toggle_status':
                $deptId = (int)($_POST['dept_id'] ?? 0);
                $newStatus = sanitizeInput($_POST['new_status'] ?? '');
                
                if ($deptId && in_array($newStatus, ['active', 'inactive'])) {
                    updateRecord('departments', ['status' => $newStatus], 'dept_id = :dept_id', ['dept_id' => $deptId]);
                    logActivity('department_status_changed', "Department ID {$deptId} status changed to {$newStatus}");
                    $message = 'Department status updated successfully';
                    $messageType = 'success';
                }
                break;
                
            case 'bulk_action':
                $selectedDepts = $_POST['selected_departments'] ?? [];
                $bulkAction = $_POST['bulk_action'] ?? '';
                
                if (!empty($selectedDepts) && $bulkAction) {
                    $count = 0;
                    foreach ($selectedDepts as $deptId) {
                        $deptId = (int)$deptId;
                        if ($deptId) {
                            switch ($bulkAction) {
                                case 'activate':
                                    updateRecord('departments', ['status' => 'active'], 'dept_id = :dept_id', ['dept_id' => $deptId]);
                                    $count++;
                                    break;
                                case 'deactivate':
                                    updateRecord('departments', ['status' => 'inactive'], 'dept_id = :dept_id', ['dept_id' => $deptId]);
                                    $count++;
                                    break;
                            }
                        }
                    }
                    
                    if ($count > 0) {
                        logActivity('department_bulk_action', "Bulk action {$bulkAction} applied to {$count} departments");
                        $message = "Bulk action applied to {$count} departments";
                        $messageType = 'success';
                    }
                }
                break;
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get departments with pagination
$page = (int)($_GET['page'] ?? 1);
$search = sanitizeInput($_GET['search'] ?? '');
$statusFilter = sanitizeInput($_GET['status'] ?? '');

$whereConditions = [];
$params = [];

if ($search) {
    $whereConditions[] = "(name LIKE ? OR abbreviation LIKE ? OR contact_person LIKE ?)";
    $params[] = "%{$search}%";
    $params[] = "%{$search}%";
    $params[] = "%{$search}%";
}

if ($statusFilter) {
    $whereConditions[] = "status = ?";
    $params[] = $statusFilter;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// Count total records
$totalRecords = fetchOne("SELECT COUNT(*) as count FROM departments {$whereClause}", $params)['count'];
$pagination = paginate($totalRecords, $page);

// Get departments
$departments = fetchAll("
    SELECT d.*,
           (SELECT COUNT(*) FROM match_participants mp WHERE mp.dept_id = d.dept_id) as participant_count,
           (SELECT COUNT(*) FROM department_standings ds WHERE ds.dept_id = d.dept_id) as event_count,
           (SELECT COALESCE(SUM(total_points), 0) FROM department_standings ds WHERE ds.dept_id = d.dept_id) as total_points
    FROM departments d
    {$whereClause}
    ORDER BY d.name ASC
    LIMIT {$pagination['records_per_page']} OFFSET {$pagination['offset']}
", $params);

$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Departments Management - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>
    
    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1>Departments Management</h1>
                <nav class="breadcrumb">
                    <a href="../dashboard.php">Dashboard</a>
                    <span>/</span>
                    <span>Departments</span>
                </nav>
            </div>
            <div class="page-actions">
                <a href="create.php" class="btn btn-primary">
                    <i class="icon-plus"></i>
                    Add Department
                </a>
            </div>
        </div>
        
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <!-- Filters and Search -->
        <div class="filters-container">
            <form method="GET" class="filters-form">
                <div class="filter-group">
                    <input type="text" name="search" placeholder="Search departments..." 
                           value="<?php echo htmlspecialchars($search); ?>" class="form-control">
                </div>
                
                <div class="filter-group">
                    <select name="status" class="form-control">
                        <option value="">All Status</option>
                        <option value="active" <?php echo $statusFilter === 'active' ? 'selected' : ''; ?>>Active</option>
                        <option value="inactive" <?php echo $statusFilter === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                    </select>
                </div>
                
                <div class="filter-actions">
                    <button type="submit" class="btn btn-secondary">Filter</button>
                    <a href="index.php" class="btn btn-outline">Clear</a>
                </div>
            </form>
        </div>
        
        <!-- Bulk Actions -->
        <div class="bulk-actions" style="display: none;" id="bulkActions">
            <form method="POST" id="bulkForm">
                <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                <input type="hidden" name="action" value="bulk_action">
                
                <div class="bulk-controls">
                    <span class="selected-count">0 selected</span>
                    <select name="bulk_action" class="form-control">
                        <option value="">Choose action...</option>
                        <option value="activate">Activate</option>
                        <option value="deactivate">Deactivate</option>
                    </select>
                    <button type="submit" class="btn btn-primary">Apply</button>
                    <button type="button" class="btn btn-outline" onclick="clearSelection()">Clear</button>
                </div>
            </form>
        </div>
        
        <!-- Departments Table -->
        <div class="table-container">
            <?php if (!empty($departments)): ?>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>
                                <label class="checkbox-container">
                                    <input type="checkbox" id="selectAll">
                                    <span class="checkmark"></span>
                                </label>
                            </th>
                            <th>Department</th>
                            <th>Contact Information</th>
                            <th>Statistics</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($departments as $dept): ?>
                            <tr>
                                <td>
                                    <label class="checkbox-container">
                                        <input type="checkbox" name="selected_departments[]" 
                                               value="<?php echo $dept['dept_id']; ?>" class="row-checkbox">
                                        <span class="checkmark"></span>
                                    </label>
                                </td>
                                <td>
                                    <div class="department-info">
                                        <div class="dept-header">
                                            <div class="dept-color" style="background-color: <?php echo htmlspecialchars($dept['color_code']); ?>"></div>
                                            <div class="dept-names">
                                                <h4><?php echo htmlspecialchars($dept['abbreviation']); ?></h4>
                                                <p><?php echo htmlspecialchars($dept['name']); ?></p>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="contact-info">
                                        <?php if ($dept['contact_person']): ?>
                                            <div><strong><?php echo htmlspecialchars($dept['contact_person']); ?></strong></div>
                                        <?php endif; ?>
                                        <?php if ($dept['email']): ?>
                                            <div><a href="mailto:<?php echo htmlspecialchars($dept['email']); ?>"><?php echo htmlspecialchars($dept['email']); ?></a></div>
                                        <?php endif; ?>
                                        <?php if ($dept['phone']): ?>
                                            <div><?php echo htmlspecialchars($dept['phone']); ?></div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="dept-stats">
                                        <div class="stat-item">
                                            <span class="stat-value"><?php echo $dept['participant_count']; ?></span>
                                            <span class="stat-label">Participants</span>
                                        </div>
                                        <div class="stat-item">
                                            <span class="stat-value"><?php echo $dept['event_count']; ?></span>
                                            <span class="stat-label">Events</span>
                                        </div>
                                        <div class="stat-item">
                                            <span class="stat-value"><?php echo number_format($dept['total_points'], 1); ?></span>
                                            <span class="stat-label">Total Points</span>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="status-badge status-<?php echo $dept['status']; ?>">
                                        <?php echo ucfirst($dept['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="view.php?id=<?php echo $dept['dept_id']; ?>" 
                                           class="btn btn-xs btn-outline" title="View Details">
                                            <i class="icon-eye"></i>
                                        </a>
                                        <a href="edit.php?id=<?php echo $dept['dept_id']; ?>" 
                                           class="btn btn-xs btn-primary" title="Edit Department">
                                            <i class="icon-edit"></i>
                                        </a>
                                        
                                        <!-- Status Toggle -->
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                            <input type="hidden" name="action" value="toggle_status">
                                            <input type="hidden" name="dept_id" value="<?php echo $dept['dept_id']; ?>">
                                            <input type="hidden" name="new_status" value="<?php echo $dept['status'] === 'active' ? 'inactive' : 'active'; ?>">
                                            <button type="submit" class="btn btn-xs btn-secondary" 
                                                    title="<?php echo $dept['status'] === 'active' ? 'Deactivate' : 'Activate'; ?>">
                                                <i class="icon-<?php echo $dept['status'] === 'active' ? 'pause' : 'play'; ?>"></i>
                                            </button>
                                        </form>
                                        
                                        <?php if ($dept['participant_count'] == 0): ?>
                                            <button class="btn btn-xs btn-danger" 
                                                    onclick="deleteDepartment(<?php echo $dept['dept_id']; ?>, '<?php echo htmlspecialchars($dept['name']); ?>')"
                                                    title="Delete Department">
                                                <i class="icon-trash"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                
                <!-- Pagination -->
                <?php echo generatePaginationHTML($pagination, 'index.php'); ?>
                
            <?php else: ?>
                <div class="no-data">
                    <h3>No departments found</h3>
                    <p>Start by adding your first department.</p>
                    <a href="create.php" class="btn btn-primary">Add Department</a>
                </div>
            <?php endif; ?>
        </div>
    </main>
    
    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal">
        <div class="modal-overlay">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Confirm Delete</h3>
                    <button class="modal-close" onclick="closeModal('deleteModal')">&times;</button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the department "<span id="deptName"></span>"?</p>
                    <p class="text-warning">This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <form id="deleteForm" method="POST">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="dept_id" id="deleteDeptId">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('deleteModal')">Cancel</button>
                        <button type="submit" class="btn btn-danger">Delete Department</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <script src="../../assets/js/admin.js"></script>
    <script>
        // Bulk selection functionality
        document.addEventListener('DOMContentLoaded', function() {
            const selectAll = document.getElementById('selectAll');
            const rowCheckboxes = document.querySelectorAll('.row-checkbox');
            const bulkActions = document.getElementById('bulkActions');
            const selectedCount = document.querySelector('.selected-count');
            
            selectAll.addEventListener('change', function() {
                rowCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                updateBulkActions();
            });
            
            rowCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateBulkActions);
            });
            
            function updateBulkActions() {
                const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
                const count = checkedBoxes.length;
                
                if (count > 0) {
                    bulkActions.style.display = 'block';
                    selectedCount.textContent = count + ' selected';
                } else {
                    bulkActions.style.display = 'none';
                }
                
                // Update select all checkbox
                selectAll.checked = count === rowCheckboxes.length;
                selectAll.indeterminate = count > 0 && count < rowCheckboxes.length;
            }
            
            // Add selected department IDs to bulk form
            document.getElementById('bulkForm').addEventListener('submit', function(e) {
                const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
                checkedBoxes.forEach(checkbox => {
                    const hiddenInput = document.createElement('input');
                    hiddenInput.type = 'hidden';
                    hiddenInput.name = 'selected_departments[]';
                    hiddenInput.value = checkbox.value;
                    this.appendChild(hiddenInput);
                });
            });
        });
        
        function clearSelection() {
            document.querySelectorAll('.row-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });
            document.getElementById('selectAll').checked = false;
            document.getElementById('bulkActions').style.display = 'none';
        }
        
        function deleteDepartment(deptId, deptName) {
            document.getElementById('deptName').textContent = deptName;
            document.getElementById('deleteDeptId').value = deptId;
            openModal('deleteModal');
        }
        
        function openModal(modalId) {
            document.getElementById(modalId).classList.add('show');
        }
        
        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('show');
        }
    </script>
</body>
</html>
