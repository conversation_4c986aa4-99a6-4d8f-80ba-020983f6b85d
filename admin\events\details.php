<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Event Details Management Page
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication and permission
requireAuth();
requirePermission('manage_events');

$eventId = (int)($_GET['id'] ?? 0);

if (!$eventId) {
    header('Location: index.php?message=Invalid event ID&type=error');
    exit;
}

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('Invalid security token');
        }
        
        switch ($_POST['action']) {
            case 'add_sports_bulk':
                $sportIds = $_POST['sport_ids'] ?? [];
                $maxTeams = (int)($_POST['max_teams_per_dept'] ?? 1);
                $deadline = sanitizeInput($_POST['registration_deadline'] ?? '');

                if (!empty($sportIds) && is_array($sportIds)) {
                    $addedCount = 0;
                    $skippedCount = 0;

                    foreach ($sportIds as $sportId) {
                        $sportId = (int)$sportId;
                        if ($sportId) {
                            // Check if sport already exists for this event
                            $existing = fetchOne("SELECT * FROM event_sports WHERE event_id = ? AND sport_id = ?", [$eventId, $sportId]);
                            if (!$existing) {
                                insertRecord('event_sports', [
                                    'event_id' => $eventId,
                                    'sport_id' => $sportId,
                                    'max_teams_per_dept' => $maxTeams,
                                    'registration_deadline' => $deadline ?: null,
                                    'status' => 'open'
                                ]);
                                $addedCount++;
                            } else {
                                $skippedCount++;
                            }
                        }
                    }

                    if ($addedCount > 0) {
                        $message = "Successfully added {$addedCount} sport(s) to the event";
                        if ($skippedCount > 0) {
                            $message .= " ({$skippedCount} already existed)";
                        }
                        $messageType = 'success';
                    } else {
                        $message = 'No new sports were added (all selected sports already exist in this event)';
                        $messageType = 'warning';
                    }
                } else {
                    throw new Exception('Please select at least one sport');
                }
                break;

            case 'add_sport':
                $sportId = (int)($_POST['sport_id'] ?? 0);
                $maxTeams = (int)($_POST['max_teams_per_dept'] ?? 1);
                $deadline = sanitizeInput($_POST['registration_deadline'] ?? '');

                if ($sportId) {
                    // Check if sport already exists for this event
                    $existing = fetchOne("SELECT * FROM event_sports WHERE event_id = ? AND sport_id = ?", [$eventId, $sportId]);
                    if ($existing) {
                        throw new Exception('Sport already added to this event');
                    }

                    insertRecord('event_sports', [
                        'event_id' => $eventId,
                        'sport_id' => $sportId,
                        'max_teams_per_dept' => $maxTeams,
                        'registration_deadline' => $deadline ?: null,
                        'status' => 'open'
                    ]);

                    $message = 'Sport added to event successfully';
                    $messageType = 'success';
                }
                break;
                
            case 'remove_sport':
                $eventSportId = (int)($_POST['event_sport_id'] ?? 0);
                if ($eventSportId) {
                    deleteRecord('event_sports', 'event_sport_id = :id', ['id' => $eventSportId]);
                    $message = 'Sport removed from event successfully';
                    $messageType = 'success';
                }
                break;

            case 'add_standings_bulk':
                $deptIds = $_POST['dept_ids'] ?? [];
                $defaultPoints = (float)($_POST['default_points'] ?? 0);
                $defaultGold = (int)($_POST['default_gold'] ?? 0);
                $defaultSilver = (int)($_POST['default_silver'] ?? 0);
                $defaultBronze = (int)($_POST['default_bronze'] ?? 0);

                if (!empty($deptIds) && is_array($deptIds)) {
                    $addedCount = 0;
                    $skippedCount = 0;

                    // Get current max position
                    $maxPosition = fetchOne("SELECT MAX(rank_position) as max_pos FROM department_standings WHERE event_id = ?", [$eventId]);
                    $nextPosition = ($maxPosition['max_pos'] ?? 0) + 1;

                    foreach ($deptIds as $deptId) {
                        $deptId = (int)$deptId;
                        if ($deptId) {
                            // Check if standing already exists
                            $existing = fetchOne("SELECT * FROM department_standings WHERE event_id = ? AND dept_id = ?", [$eventId, $deptId]);
                            if (!$existing) {
                                insertRecord('department_standings', [
                                    'event_id' => $eventId,
                                    'dept_id' => $deptId,
                                    'rank_position' => $nextPosition,
                                    'total_points' => $defaultPoints,
                                    'medals_gold' => $defaultGold,
                                    'medals_silver' => $defaultSilver,
                                    'medals_bronze' => $defaultBronze
                                ]);
                                $addedCount++;
                                $nextPosition++;
                            } else {
                                $skippedCount++;
                            }
                        }
                    }

                    if ($addedCount > 0) {
                        $message = "Successfully added {$addedCount} department(s) to standings";
                        if ($skippedCount > 0) {
                            $message .= " ({$skippedCount} already existed)";
                        }
                        $messageType = 'success';
                    } else {
                        $message = 'No new departments were added (all selected departments already exist in standings)';
                        $messageType = 'warning';
                    }
                } else {
                    throw new Exception('Please select at least one department');
                }
                break;

            case 'update_standings':
                $deptId = (int)($_POST['dept_id'] ?? 0);
                $points = (float)($_POST['points'] ?? 0);
                $position = (int)($_POST['position'] ?? 0);
                $gold = (int)($_POST['gold'] ?? 0);
                $silver = (int)($_POST['silver'] ?? 0);
                $bronze = (int)($_POST['bronze'] ?? 0);
                
                if ($deptId) {
                    // Check if standing exists
                    $existing = fetchOne("SELECT * FROM department_standings WHERE event_id = ? AND dept_id = ?", [$eventId, $deptId]);
                    
                    $standingData = [
                        'event_id' => $eventId,
                        'dept_id' => $deptId,
                        'total_points' => $points,
                        'rank_position' => $position,
                        'medals_gold' => $gold,
                        'medals_silver' => $silver,
                        'medals_bronze' => $bronze
                    ];
                    
                    if ($existing) {
                        updateRecord('department_standings', $standingData, 'standing_id = :id', ['id' => $existing['standing_id']]);
                    } else {
                        insertRecord('department_standings', $standingData);
                    }
                    
                    $message = 'Department standings updated successfully';
                    $messageType = 'success';
                }
                break;
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get event details
$event = fetchOne("
    SELECT e.*, 
           au.full_name as created_by_name,
           (SELECT COUNT(*) FROM matches WHERE event_id = e.event_id) as match_count,
           (SELECT COUNT(*) FROM department_standings WHERE event_id = e.event_id) as dept_count
    FROM events e
    LEFT JOIN admin_users au ON e.created_by = au.admin_id
    WHERE e.event_id = ?
", [$eventId]);

if (!$event) {
    header('Location: index.php?message=Event not found&type=error');
    exit;
}

// Get event sports
$eventSports = fetchAll("
    SELECT es.*, s.name as sport_name, s.category as sport_category
    FROM event_sports es
    JOIN sports s ON es.sport_id = s.sport_id
    WHERE es.event_id = ?
    ORDER BY s.name
", [$eventId]);

// Get available sports (not yet added to event)
$availableSports = fetchAll("
    SELECT s.* FROM sports s
    WHERE s.status = 'active' 
    AND s.sport_id NOT IN (
        SELECT sport_id FROM event_sports WHERE event_id = ?
    )
    ORDER BY s.name
", [$eventId]);

// Get department standings
$standings = fetchAll("
    SELECT ds.*, d.name as dept_name, d.abbreviation as dept_abbr
    FROM department_standings ds
    JOIN departments d ON ds.dept_id = d.dept_id
    WHERE ds.event_id = ?
    ORDER BY ds.rank_position ASC, ds.total_points DESC
", [$eventId]);

// Get all departments for standings management
$allDepartments = fetchAll("
    SELECT * FROM departments 
    WHERE status = 'active' 
    ORDER BY name
");

$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($event['name']); ?> - Event Details - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>
    
    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1><?php echo htmlspecialchars($event['name']); ?></h1>
                <nav class="breadcrumb">
                    <a href="../dashboard.php">Dashboard</a>
                    <span>/</span>
                    <a href="index.php">Events</a>
                    <span>/</span>
                    <span>Event Details</span>
                </nav>
            </div>
            <div class="page-actions">
                <button class="btn btn-primary" onclick="editEvent(<?php echo $eventId; ?>)">
                    <i class="icon-edit"></i>
                    Edit Event
                </button>
                <a href="index.php" class="btn btn-secondary">
                    <i class="icon-arrow-left"></i>
                    Back to Events
                </a>
            </div>
        </div>
        
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <!-- Event Overview -->
        <div class="event-overview">
            <div class="overview-grid">
                <div class="overview-card">
                    <div class="card-header">
                        <i class="icon-info"></i>
                        <h3>Event Information</h3>
                    </div>
                    <div class="card-content">
                        <div class="info-row">
                            <label>Status:</label>
                            <span class="status-badge status-<?php echo $event['status']; ?>">
                                <?php echo ucfirst($event['status']); ?>
                            </span>
                        </div>
                        <div class="info-row">
                            <label>Start Date:</label>
                            <span><?php echo formatDate($event['start_date']); ?></span>
                        </div>
                        <div class="info-row">
                            <label>End Date:</label>
                            <span><?php echo formatDate($event['end_date']); ?></span>
                        </div>
                        <div class="info-row">
                            <label>Created By:</label>
                            <span><?php echo htmlspecialchars($event['created_by_name'] ?? 'System'); ?></span>
                        </div>
                        <?php if ($event['description']): ?>
                            <div class="info-row">
                                <label>Description:</label>
                                <p><?php echo htmlspecialchars($event['description']); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="overview-card">
                    <div class="card-header">
                        <i class="icon-chart"></i>
                        <h3>Statistics</h3>
                    </div>
                    <div class="card-content">
                        <div class="stats-grid">
                            <div class="stat-item">
                                <span class="stat-value"><?php echo count($eventSports); ?></span>
                                <span class="stat-label">Sports</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value"><?php echo $event['match_count']; ?></span>
                                <span class="stat-label">Matches</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value"><?php echo count($standings); ?></span>
                                <span class="stat-label">Participating Departments</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Event Sports Management -->
        <div class="section">
            <div class="section-header">
                <h2><i class="icon-trophy"></i> Event Sports</h2>
                <button class="btn btn-primary btn-sm" onclick="openModal('addSportModal')">
                    <i class="icon-plus"></i>
                    Add Sport
                </button>
            </div>

            <?php if (!empty($eventSports)): ?>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Sport Name</th>
                                <th>Category</th>
                                <th>Max Teams per Dept</th>
                                <th>Registration Deadline</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($eventSports as $sport): ?>
                                <tr class="sport-row clickable-row"
                                    onclick="openTournamentConfig(<?php echo $sport['event_sport_id']; ?>, '<?php echo htmlspecialchars($sport['sport_name']); ?>', '<?php echo $sport['sport_category']; ?>')"
                                    title="Click to configure tournament for <?php echo htmlspecialchars($sport['sport_name']); ?>">
                                    <td>
                                        <div class="sport-name-cell">
                                            <strong><?php echo htmlspecialchars($sport['sport_name']); ?></strong>
                                            <small class="sport-hint">Click to configure tournament</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="category-badge category-<?php echo $sport['sport_category']; ?>">
                                            <?php echo ucfirst(str_replace('_', ' ', $sport['sport_category'])); ?>
                                        </span>
                                    </td>
                                    <td><?php echo $sport['max_teams_per_dept']; ?></td>
                                    <td><?php echo $sport['registration_deadline'] ? formatDate($sport['registration_deadline']) : 'No deadline'; ?></td>
                                    <td>
                                        <span class="status-badge status-<?php echo $sport['status']; ?>">
                                            <?php echo ucfirst($sport['status']); ?>
                                        </span>
                                    </td>
                                    <td class="actions-cell" onclick="event.stopPropagation();">
                                        <div class="action-buttons">
                                            <button type="button" class="btn btn-sm btn-primary"
                                                    onclick="openTournamentConfig(<?php echo $sport['event_sport_id']; ?>, '<?php echo htmlspecialchars($sport['sport_name']); ?>', '<?php echo $sport['sport_category']; ?>')"
                                                    title="Configure Tournament">
                                                <i class="icon-settings"></i>
                                                Configure
                                            </button>
                                            <form method="POST" style="display: inline;" onsubmit="return confirm('Remove this sport from the event?')">
                                                <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                                <input type="hidden" name="action" value="remove_sport">
                                                <input type="hidden" name="event_sport_id" value="<?php echo $sport['event_sport_id']; ?>">
                                                <button type="submit" class="btn btn-sm btn-danger">
                                                    <i class="icon-trash"></i>
                                                    Remove
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="no-data">
                    <i class="icon-trophy"></i>
                    <h4>No Sports Added</h4>
                    <p>Add sports to this event to get started.</p>
                    <button class="btn btn-primary" onclick="openModal('addSportModal')">
                        <i class="icon-plus"></i>
                        Add First Sport
                    </button>
                </div>
            <?php endif; ?>
        </div>

        <!-- Department Standings -->
        <div class="section">
            <div class="section-header">
                <h2><i class="icon-users"></i> Department Standings</h2>
                <button class="btn btn-primary btn-sm" onclick="openModal('standingsModal')">
                    <i class="icon-edit"></i>
                    Manage Standings
                </button>
            </div>

            <?php if (!empty($standings)): ?>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Position</th>
                                <th>Department</th>
                                <th>Total Points</th>
                                <th>Gold</th>
                                <th>Silver</th>
                                <th>Bronze</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($standings as $standing): ?>
                                <tr>
                                    <td>
                                        <span class="position-badge position-<?php echo $standing['rank_position']; ?>">
                                            #<?php echo $standing['rank_position']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="dept-info">
                                            <strong><?php echo htmlspecialchars($standing['dept_name']); ?></strong>
                                            <span class="dept-abbr"><?php echo htmlspecialchars($standing['dept_abbr']); ?></span>
                                        </div>
                                    </td>
                                    <td><strong><?php echo $standing['total_points']; ?> pts</strong></td>
                                    <td><span class="medal-count gold"><?php echo $standing['medals_gold']; ?></span></td>
                                    <td><span class="medal-count silver"><?php echo $standing['medals_silver']; ?></span></td>
                                    <td><span class="medal-count bronze"><?php echo $standing['medals_bronze']; ?></span></td>
                                    <td>
                                        <button class="btn btn-sm btn-primary"
                                                onclick="editStanding(<?php echo $standing['dept_id']; ?>, '<?php echo htmlspecialchars($standing['dept_name']); ?>', <?php echo $standing['total_points']; ?>, <?php echo $standing['rank_position']; ?>, <?php echo $standing['medals_gold']; ?>, <?php echo $standing['medals_silver']; ?>, <?php echo $standing['medals_bronze']; ?>)">
                                            <i class="icon-edit"></i>
                                            Edit
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="no-data">
                    <i class="icon-users"></i>
                    <h4>No Department Standings</h4>
                    <p>Department standings will appear here as the event progresses.</p>
                    <button class="btn btn-primary" onclick="openModal('standingsModal')">
                        <i class="icon-plus"></i>
                        Add Department Standings
                    </button>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <!-- Add Sports Modal (Bulk Selection) -->
    <div id="addSportModal" class="modal">
        <div class="modal-overlay">
            <div class="modal-content modal-lg">
                <div class="modal-header">
                    <h3><i class="icon-plus"></i> Add Sports to Event</h3>
                    <button class="modal-close" onclick="closeModal('addSportModal')">&times;</button>
                </div>
                <form method="POST" id="addSportsForm">
                    <div class="modal-body">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                        <input type="hidden" name="action" value="add_sports_bulk">

                        <div id="addSportsErrors"></div>

                        <!-- Sports Selection -->
                        <div class="form-group">
                            <div class="selection-header">
                                <label>Select Sports <span class="required">*</span></label>
                                <div class="selection-controls">
                                    <button type="button" class="btn btn-sm btn-secondary" onclick="selectAllSports()">
                                        <i class="icon-check"></i> Select All
                                    </button>
                                    <button type="button" class="btn btn-sm btn-secondary" onclick="deselectAllSports()">
                                        <i class="icon-cancel"></i> Deselect All
                                    </button>
                                </div>
                            </div>

                            <?php if (!empty($availableSports)): ?>
                                <div class="sports-grid">
                                    <?php foreach ($availableSports as $sport): ?>
                                        <div class="sport-item">
                                            <label class="checkbox-label">
                                                <input type="checkbox" name="sport_ids[]" value="<?php echo $sport['sport_id']; ?>"
                                                       class="sport-checkbox" onchange="updateSportSelection()">
                                                <span class="checkmark"></span>
                                                <div class="sport-info">
                                                    <strong><?php echo htmlspecialchars($sport['name']); ?></strong>
                                                    <span class="category-badge category-<?php echo $sport['category']; ?>">
                                                        <?php echo ucfirst($sport['category']); ?>
                                                    </span>
                                                </div>
                                            </label>
                                        </div>
                                    <?php endforeach; ?>
                                </div>

                                <div class="selection-summary">
                                    <span id="selectedCount">0</span> sport(s) selected
                                </div>
                            <?php else: ?>
                                <div class="no-data">
                                    <p>All available sports have already been added to this event.</p>
                                </div>
                            <?php endif; ?>
                        </div>

                        <?php if (!empty($availableSports)): ?>
                            <!-- Bulk Configuration -->
                            <div class="bulk-config">
                                <h4><i class="icon-settings"></i> Default Configuration</h4>
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="bulk_max_teams">Max Teams per Department</label>
                                        <input type="number" id="bulk_max_teams" name="max_teams_per_dept"
                                               value="1" min="1" max="10" class="form-control">
                                    </div>

                                    <div class="form-group">
                                        <label for="bulk_deadline">Registration Deadline (Optional)</label>
                                        <input type="date" id="bulk_deadline" name="registration_deadline"
                                               class="form-control">
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <?php if (!empty($availableSports)): ?>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" onclick="closeModal('addSportModal')">Cancel</button>
                            <button type="submit" class="btn btn-primary" id="addSportsBtn" disabled>
                                <i class="icon-plus"></i>
                                Add Selected Sports
                            </button>
                        </div>
                    <?php else: ?>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" onclick="closeModal('addSportModal')">Close</button>
                        </div>
                    <?php endif; ?>
                </form>
            </div>
        </div>
    </div>

    <!-- Standings Management Modal -->
    <div id="standingsModal" class="modal">
        <div class="modal-overlay">
            <div class="modal-content modal-lg">
                <div class="modal-header">
                    <h3><i class="icon-edit"></i> Edit Department Standing</h3>
                    <button class="modal-close" onclick="closeModal('standingsModal')">&times;</button>
                </div>
                <form method="POST" id="standingsForm">
                    <div class="modal-body">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                        <input type="hidden" name="action" value="update_standings">
                        <input type="hidden" name="dept_id" id="standings_dept_id">

                        <div class="form-grid">
                            <div class="form-group">
                                <label for="standings_dept_name">Department</label>
                                <input type="text" id="standings_dept_name" readonly class="form-control">
                            </div>

                            <div class="form-group">
                                <label for="standings_position">Position</label>
                                <input type="number" id="standings_position" name="position"
                                       min="1" class="form-control" required>
                            </div>

                            <div class="form-group">
                                <label for="standings_points">Total Points</label>
                                <input type="number" id="standings_points" name="points"
                                       step="0.01" min="0" class="form-control" required>
                            </div>

                            <div class="form-group">
                                <label for="standings_gold">Gold Medals</label>
                                <input type="number" id="standings_gold" name="gold"
                                       min="0" class="form-control" required>
                            </div>

                            <div class="form-group">
                                <label for="standings_silver">Silver Medals</label>
                                <input type="number" id="standings_silver" name="silver"
                                       min="0" class="form-control" required>
                            </div>

                            <div class="form-group">
                                <label for="standings_bronze">Bronze Medals</label>
                                <input type="number" id="standings_bronze" name="bronze"
                                       min="0" class="form-control" required>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('standingsModal')">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="icon-save"></i>
                            Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Event Modal -->
    <div id="editModal" class="modal">
        <div class="modal-overlay">
            <div class="modal-content modal-lg">
                <div class="modal-header">
                    <h3><i class="icon-edit"></i> Edit Event</h3>
                    <button class="modal-close" onclick="closeModal('editModal')">&times;</button>
                </div>
                <div class="modal-body">
                    <div id="editEventContent">
                        <div class="loading">Loading event form...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Department Standings Modal -->
    <div id="newStandingModal" class="modal">
        <div class="modal-overlay">
            <div class="modal-content modal-lg">
                <div class="modal-header">
                    <h3><i class="icon-plus"></i> Add Department Standings</h3>
                    <button class="modal-close" onclick="closeModal('newStandingModal')">&times;</button>
                </div>
                <form method="POST" id="addStandingsForm">
                    <div class="modal-body">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                        <input type="hidden" name="action" value="add_standings_bulk">

                        <div id="addStandingsErrors"></div>

                        <!-- Department Selection -->
                        <div class="form-group">
                            <div class="selection-header">
                                <label>Select Departments <span class="required">*</span></label>
                                <div class="selection-controls">
                                    <button type="button" class="btn btn-sm btn-secondary" onclick="selectAllDepartments()">
                                        <i class="icon-check"></i> Select All
                                    </button>
                                    <button type="button" class="btn btn-sm btn-secondary" onclick="deselectAllDepartments()">
                                        <i class="icon-cancel"></i> Deselect All
                                    </button>
                                </div>
                            </div>

                            <?php
                            // Filter out departments that already have standings
                            $existingDeptIds = array_column($standings, 'dept_id');
                            $availableDepartments = array_filter($allDepartments, function($dept) use ($existingDeptIds) {
                                return !in_array($dept['dept_id'], $existingDeptIds);
                            });
                            ?>

                            <?php if (!empty($availableDepartments)): ?>
                                <div class="departments-grid">
                                    <?php foreach ($availableDepartments as $dept): ?>
                                        <div class="dept-item">
                                            <label class="checkbox-label">
                                                <input type="checkbox" name="dept_ids[]" value="<?php echo $dept['dept_id']; ?>"
                                                       class="dept-checkbox" onchange="updateDeptSelection()">
                                                <span class="checkmark"></span>
                                                <div class="dept-info">
                                                    <strong><?php echo htmlspecialchars($dept['name']); ?></strong>
                                                    <span class="dept-abbr"><?php echo htmlspecialchars($dept['abbreviation']); ?></span>
                                                </div>
                                            </label>
                                        </div>
                                    <?php endforeach; ?>
                                </div>

                                <div class="selection-summary">
                                    <span id="selectedDeptCount">0</span> department(s) selected
                                </div>
                            <?php else: ?>
                                <div class="no-data">
                                    <p>All departments already have standings in this event.</p>
                                </div>
                            <?php endif; ?>
                        </div>

                        <?php if (!empty($availableDepartments)): ?>
                            <!-- Default Values -->
                            <div class="bulk-config">
                                <h4><i class="icon-settings"></i> Default Values</h4>
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="default_points">Default Points</label>
                                        <input type="number" id="default_points" name="default_points"
                                               value="0" step="0.01" min="0" class="form-control">
                                    </div>

                                    <div class="form-group">
                                        <label for="default_gold">Default Gold Medals</label>
                                        <input type="number" id="default_gold" name="default_gold"
                                               value="0" min="0" class="form-control">
                                    </div>

                                    <div class="form-group">
                                        <label for="default_silver">Default Silver Medals</label>
                                        <input type="number" id="default_silver" name="default_silver"
                                               value="0" min="0" class="form-control">
                                    </div>

                                    <div class="form-group">
                                        <label for="default_bronze">Default Bronze Medals</label>
                                        <input type="number" id="default_bronze" name="default_bronze"
                                               value="0" min="0" class="form-control">
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <?php if (!empty($availableDepartments)): ?>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" onclick="closeModal('newStandingModal')">Cancel</button>
                            <button type="submit" class="btn btn-primary" id="addStandingsBtn" disabled>
                                <i class="icon-plus"></i>
                                Add Selected Departments
                            </button>
                        </div>
                    <?php else: ?>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" onclick="closeModal('newStandingModal')">Close</button>
                        </div>
                    <?php endif; ?>
                </form>
            </div>
        </div>
    </div>

    <script src="../../assets/js/admin.js"></script>

    <style>
        /* Clickable Sports Row Styling */
        .sport-row.clickable-row {
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .sport-row.clickable-row:hover {
            background-color: var(--primary-50, #eff6ff);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .sport-name-cell {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .sport-hint {
            color: var(--gray-500, #6b7280);
            font-size: 0.75rem;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .sport-row:hover .sport-hint {
            opacity: 1;
        }

        .actions-cell {
            position: relative;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .action-buttons .btn {
            white-space: nowrap;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .sport-hint {
                display: none;
            }

            .action-buttons {
                flex-direction: column;
                gap: 0.25rem;
            }

            .action-buttons .btn {
                font-size: 0.75rem;
                padding: 0.375rem 0.5rem;
            }
        }
    </style>

    <script>
        // Edit Event Function
        function editEvent(eventId) {
            const content = document.getElementById('editEventContent');
            content.innerHTML = '<div class="loading"><i class="icon-spinner"></i> Loading event form...</div>';
            openModal('editModal');

            // Fetch edit form
            fetch(`edit_ajax.php?id=${eventId}`)
                .then(response => response.text())
                .then(html => {
                    content.innerHTML = html;
                    // Initialize form validation and handlers
                    initializeEditForm();
                })
                .catch(error => {
                    content.innerHTML = '<div class="alert alert-error">Error loading edit form. Please try again.</div>';
                });
        }

        // Initialize Edit Form
        function initializeEditForm() {
            const form = document.getElementById('editEventForm');
            if (form) {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const formData = new FormData(form);
                    const submitBtn = form.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;

                    submitBtn.innerHTML = '<i class="icon-spinner"></i> Saving...';
                    submitBtn.disabled = true;

                    fetch('edit_ajax.php', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            closeModal('editModal');
                            location.reload(); // Refresh the page to show updated data
                        } else {
                            // Show error message
                            const errorDiv = document.getElementById('editFormErrors');
                            if (errorDiv) {
                                errorDiv.innerHTML = `<div class="alert alert-error">${data.message}</div>`;
                            }
                        }
                    })
                    .catch(error => {
                        const errorDiv = document.getElementById('editFormErrors');
                        if (errorDiv) {
                            errorDiv.innerHTML = '<div class="alert alert-error">Error saving event. Please try again.</div>';
                        }
                    })
                    .finally(() => {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    });
                });
            }
        }

        // Edit Standing Function
        function editStanding(deptId, deptName, points, position, gold, silver, bronze) {
            document.getElementById('standings_dept_id').value = deptId;
            document.getElementById('standings_dept_name').value = deptName;
            document.getElementById('standings_points').value = points;
            document.getElementById('standings_position').value = position;
            document.getElementById('standings_gold').value = gold;
            document.getElementById('standings_silver').value = silver;
            document.getElementById('standings_bronze').value = bronze;
            openModal('standingsModal');
        }

        // Sports Selection Functions
        function selectAllSports() {
            const checkboxes = document.querySelectorAll('.sport-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
            updateSportSelection();
        }

        function deselectAllSports() {
            const checkboxes = document.querySelectorAll('.sport-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            updateSportSelection();
        }

        function updateSportSelection() {
            const checkboxes = document.querySelectorAll('.sport-checkbox:checked');
            const count = checkboxes.length;
            const countElement = document.getElementById('selectedCount');
            const submitBtn = document.getElementById('addSportsBtn');

            if (countElement) countElement.textContent = count;
            if (submitBtn) submitBtn.disabled = count === 0;
        }

        // Department Selection Functions
        function selectAllDepartments() {
            const checkboxes = document.querySelectorAll('.dept-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
            updateDeptSelection();
        }

        function deselectAllDepartments() {
            const checkboxes = document.querySelectorAll('.dept-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            updateDeptSelection();
        }

        function updateDeptSelection() {
            const checkboxes = document.querySelectorAll('.dept-checkbox:checked');
            const count = checkboxes.length;
            const countElement = document.getElementById('selectedDeptCount');
            const submitBtn = document.getElementById('addStandingsBtn');

            if (countElement) countElement.textContent = count;
            if (submitBtn) submitBtn.disabled = count === 0;
        }

        // Add New Standing Function (Legacy - for single department)
        function addNewStanding() {
            const select = document.getElementById('new_dept_select');
            const selectedOption = select.options[select.selectedIndex];

            if (selectedOption.value) {
                const deptId = selectedOption.value;
                const deptName = selectedOption.dataset.name;

                closeModal('newStandingModal');
                editStanding(deptId, deptName, 0, 0, 0, 0, 0);
                select.selectedIndex = 0; // Reset selection
            }
        }

        // Tournament Configuration Function
        function openTournamentConfig(eventSportId, sportName, sportCategory) {
            // Navigate to tournament configuration page
            window.location.href = `tournament_config.php?event_sport_id=${eventSportId}&event_id=<?php echo $eventId; ?>`;
        }

        // Modal Functions
        function openModal(modalId) {
            document.getElementById(modalId).classList.add('show');
            document.body.style.overflow = 'hidden';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('show');
            document.body.style.overflow = '';
        }

        // Form Submission Handlers
        function initializeBulkForms() {
            // Add Sports Form
            const addSportsForm = document.getElementById('addSportsForm');
            if (addSportsForm) {
                addSportsForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const formData = new FormData(this);
                    const submitBtn = document.getElementById('addSportsBtn');
                    const originalText = submitBtn.innerHTML;
                    const errorDiv = document.getElementById('addSportsErrors');

                    // Validate selection
                    const selectedSports = document.querySelectorAll('.sport-checkbox:checked');
                    if (selectedSports.length === 0) {
                        errorDiv.innerHTML = '<div class="alert alert-error">Please select at least one sport.</div>';
                        return;
                    }

                    submitBtn.innerHTML = '<i class="icon-spinner"></i> Adding Sports...';
                    submitBtn.disabled = true;
                    errorDiv.innerHTML = '';

                    fetch(window.location.href, {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.text())
                    .then(() => {
                        closeModal('addSportModal');
                        location.reload();
                    })
                    .catch(error => {
                        errorDiv.innerHTML = '<div class="alert alert-error">Error adding sports. Please try again.</div>';
                    })
                    .finally(() => {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    });
                });
            }

            // Add Standings Form
            const addStandingsForm = document.getElementById('addStandingsForm');
            if (addStandingsForm) {
                addStandingsForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const formData = new FormData(this);
                    const submitBtn = document.getElementById('addStandingsBtn');
                    const originalText = submitBtn.innerHTML;
                    const errorDiv = document.getElementById('addStandingsErrors');

                    // Validate selection
                    const selectedDepts = document.querySelectorAll('.dept-checkbox:checked');
                    if (selectedDepts.length === 0) {
                        errorDiv.innerHTML = '<div class="alert alert-error">Please select at least one department.</div>';
                        return;
                    }

                    submitBtn.innerHTML = '<i class="icon-spinner"></i> Adding Departments...';
                    submitBtn.disabled = true;
                    errorDiv.innerHTML = '';

                    fetch(window.location.href, {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.text())
                    .then(() => {
                        closeModal('newStandingModal');
                        location.reload();
                    })
                    .catch(error => {
                        errorDiv.innerHTML = '<div class="alert alert-error">Error adding departments. Please try again.</div>';
                    })
                    .finally(() => {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    });
                });
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize bulk forms
            initializeBulkForms();

            // Close modals when clicking overlay
            document.querySelectorAll('.modal-overlay').forEach(overlay => {
                overlay.addEventListener('click', function(e) {
                    if (e.target === this) {
                        const modal = this.closest('.modal');
                        if (modal) {
                            closeModal(modal.id);
                        }
                    }
                });
            });

            // Handle "Manage Standings" button to show department selection or edit form
            const manageStandingsBtn = document.querySelector('[onclick*="standingsModal"]');
            if (manageStandingsBtn) {
                manageStandingsBtn.onclick = function() {
                    openModal('newStandingModal');
                };
            }
        });
    </script>

    <style>
        /* Event Details Specific Styles */
        .event-overview {
            margin-bottom: 2rem;
        }

        .overview-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
        }

        .overview-card {
            background: var(--white);
            border: 1px solid var(--gray-200);
            border-radius: 8px;
            overflow: hidden;
        }

        .card-header {
            background: var(--gray-50);
            padding: 1rem;
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .card-header h3 {
            margin: 0;
            font-size: 1rem;
            color: var(--gray-900);
        }

        .card-content {
            padding: 1.5rem;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--gray-100);
        }

        .info-row:last-child {
            margin-bottom: 0;
            border-bottom: none;
        }

        .info-row label {
            font-weight: 600;
            color: var(--gray-600);
            min-width: 100px;
        }

        .info-row span, .info-row p {
            color: var(--gray-900);
            margin: 0;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            text-align: center;
        }

        .stat-item {
            padding: 1rem;
            background: var(--gray-50);
            border-radius: 6px;
        }

        .stat-value {
            display: block;
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-600);
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--gray-600);
        }

        .section {
            background: var(--white);
            border: 1px solid var(--gray-200);
            border-radius: 8px;
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .section-header {
            background: var(--gray-50);
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-header h2 {
            margin: 0;
            font-size: 1.1rem;
            color: var(--gray-900);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .table-container {
            padding: 1.5rem;
        }

        .category-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .category-badge.category-team {
            background: var(--blue-100);
            color: var(--blue-800);
        }

        .category-badge.category-individual {
            background: var(--green-100);
            color: var(--green-800);
        }

        .category-badge.category-performing_arts {
            background: var(--purple-100);
            color: var(--purple-800);
        }

        .category-badge.category-academic {
            background: var(--orange-100);
            color: var(--orange-800);
        }

        .position-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.875rem;
            font-weight: 600;
            background: var(--gray-100);
            color: var(--gray-700);
        }

        .position-badge.position-1 {
            background: var(--yellow-100);
            color: var(--yellow-800);
        }

        .position-badge.position-2 {
            background: var(--gray-100);
            color: var(--gray-800);
        }

        .position-badge.position-3 {
            background: var(--orange-100);
            color: var(--orange-800);
        }

        .dept-info {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .dept-abbr {
            font-size: 0.75rem;
            color: var(--gray-500);
            text-transform: uppercase;
        }

        .medal-count {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-weight: 600;
            min-width: 2rem;
            text-align: center;
        }

        .medal-count.gold {
            background: var(--yellow-100);
            color: var(--yellow-800);
        }

        .medal-count.silver {
            background: var(--gray-100);
            color: var(--gray-800);
        }

        .medal-count.bronze {
            background: var(--orange-100);
            color: var(--orange-800);
        }

        .no-data {
            text-align: center;
            padding: 3rem 1.5rem;
            color: var(--gray-500);
        }

        .no-data i {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .no-data h4 {
            margin: 0 0 0.5rem 0;
            color: var(--gray-700);
        }

        .no-data p {
            margin: 0 0 1rem 0;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .modal-lg {
            max-width: 800px;
        }

        .loading {
            text-align: center;
            padding: 2rem;
            color: var(--gray-600);
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .icon-spinner {
            animation: spin 1s linear infinite;
        }

        /* Bulk Selection Styles */
        .selection-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .selection-controls {
            display: flex;
            gap: 0.5rem;
        }

        .sports-grid, .departments-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid var(--gray-200, #e5e7eb);
            border-radius: 6px;
            padding: 1rem;
            background: var(--gray-50, #f9fafb);
        }

        .sport-item, .dept-item {
            background: var(--white, #ffffff);
            border: 1px solid var(--gray-200, #e5e7eb);
            border-radius: 6px;
            padding: 0.75rem;
            transition: all 0.2s ease;
        }

        .sport-item:hover, .dept-item:hover {
            border-color: var(--primary-300, #93c5fd);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .checkbox-label {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            cursor: pointer;
            margin: 0;
            width: 100%;
        }

        .checkbox-label input[type="checkbox"] {
            margin: 0;
            width: 18px;
            height: 18px;
            accent-color: var(--primary-600, #2563eb);
        }

        .checkmark {
            display: none; /* Using native checkbox styling */
        }

        .sport-info, .dept-info {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
            flex: 1;
        }

        .sport-info strong, .dept-info strong {
            font-weight: 600;
            color: var(--gray-900, #111827);
        }

        .selection-summary {
            text-align: center;
            padding: 0.75rem;
            background: var(--blue-50, #eff6ff);
            border: 1px solid var(--blue-200, #bfdbfe);
            border-radius: 6px;
            color: var(--blue-800, #1e40af);
            font-weight: 500;
        }

        .bulk-config {
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid var(--gray-200, #e5e7eb);
        }

        .bulk-config h4 {
            margin: 0 0 1rem 0;
            color: var(--gray-700, #374151);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* Enhanced button visibility */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            border: none;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.2s;
            white-space: nowrap;
            background: transparent;
        }

        .btn-primary {
            background: var(--primary-600, #2563eb) !important;
            color: var(--white, #ffffff) !important;
            border: 1px solid var(--primary-600, #2563eb) !important;
        }

        .btn-primary:hover:not(:disabled) {
            background: var(--primary-700, #1d4ed8) !important;
            border-color: var(--primary-700, #1d4ed8) !important;
        }

        .btn-primary:disabled {
            background: var(--gray-400, #9ca3af) !important;
            border-color: var(--gray-400, #9ca3af) !important;
            cursor: not-allowed;
            opacity: 0.6;
        }

        .btn-secondary {
            background: var(--gray-100, #f3f4f6) !important;
            color: var(--gray-700, #374151) !important;
            border: 1px solid var(--gray-300, #d1d5db) !important;
        }

        .btn-secondary:hover {
            background: var(--gray-200, #e5e7eb) !important;
        }

        .btn-sm {
            padding: 0.5rem 0.75rem;
            font-size: 0.8125rem;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
            padding: 1rem 1.5rem;
            border-top: 1px solid var(--gray-200, #e5e7eb);
            background: var(--gray-50, #f9fafb);
        }

        .section-header {
            background: var(--gray-50, #f9fafb);
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--gray-200, #e5e7eb);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-header .btn {
            background: var(--primary-600, #2563eb) !important;
            color: white !important;
            border: 1px solid var(--primary-600, #2563eb) !important;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .overview-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .section-header {
                flex-direction: column;
                gap: 1rem;
                align-items: flex-start;
            }

            .selection-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .sports-grid, .departments-grid {
                grid-template-columns: 1fr;
            }

            .modal-footer {
                flex-direction: column;
            }
        }
    </style>
</body>
</html>
