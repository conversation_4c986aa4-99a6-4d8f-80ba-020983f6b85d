<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Clear System Cache
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

declare(strict_types=1);

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Set JSON response header
header('Content-Type: application/json');

// Require authentication and super admin access
try {
    requireAuth();
    
    if (getCurrentUser()['role'] !== 'super_admin') {
        throw new Exception('Access denied');
    }
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method');
    }
    
    // Clear various types of cache
    $results = [];
    $overallSuccess = true;
    
    // 1. Clear PHP OPcache if available
    if (function_exists('opcache_reset')) {
        try {
            opcache_reset();
            $results['opcache'] = 'PHP OPcache cleared successfully';
        } catch (Exception $e) {
            $results['opcache'] = 'Failed to clear OPcache: ' . $e->getMessage();
            $overallSuccess = false;
        }
    } else {
        $results['opcache'] = 'OPcache not available';
    }
    
    // 2. Clear session files (if using file-based sessions)
    try {
        $sessionPath = session_save_path();
        if (empty($sessionPath)) {
            $sessionPath = sys_get_temp_dir();
        }
        
        $sessionFiles = glob($sessionPath . '/sess_*');
        $clearedSessions = 0;
        
        if ($sessionFiles) {
            foreach ($sessionFiles as $file) {
                if (is_file($file) && unlink($file)) {
                    $clearedSessions++;
                }
            }
        }
        
        $results['sessions'] = "Cleared {$clearedSessions} session files";
    } catch (Exception $e) {
        $results['sessions'] = 'Failed to clear sessions: ' . $e->getMessage();
        $overallSuccess = false;
    }
    
    // 3. Clear temporary files
    try {
        $tempDir = sys_get_temp_dir();
        $tempFiles = glob($tempDir . '/scims_*');
        $clearedTemp = 0;
        
        if ($tempFiles) {
            foreach ($tempFiles as $file) {
                if (is_file($file) && unlink($file)) {
                    $clearedTemp++;
                }
            }
        }
        
        $results['temp_files'] = "Cleared {$clearedTemp} temporary files";
    } catch (Exception $e) {
        $results['temp_files'] = 'Failed to clear temp files: ' . $e->getMessage();
        $overallSuccess = false;
    }
    
    // 4. Clear application cache directory if it exists
    try {
        $cacheDir = dirname(__DIR__, 2) . '/cache';
        $clearedCache = 0;
        
        if (is_dir($cacheDir)) {
            $cacheFiles = glob($cacheDir . '/*');
            
            if ($cacheFiles) {
                foreach ($cacheFiles as $file) {
                    if (is_file($file) && unlink($file)) {
                        $clearedCache++;
                    }
                }
            }
        }
        
        $results['app_cache'] = "Cleared {$clearedCache} application cache files";
    } catch (Exception $e) {
        $results['app_cache'] = 'Failed to clear app cache: ' . $e->getMessage();
        $overallSuccess = false;
    }
    
    // 5. Clear browser cache headers (for future requests)
    try {
        // Set cache-busting headers
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');
        
        $results['browser_cache'] = 'Browser cache headers set for future requests';
    } catch (Exception $e) {
        $results['browser_cache'] = 'Failed to set cache headers: ' . $e->getMessage();
        $overallSuccess = false;
    }
    
    // 6. Clear database query cache (if MySQL query cache is enabled)
    try {
        $db = getDB();
        $db->query('RESET QUERY CACHE');
        $results['query_cache'] = 'Database query cache cleared';
    } catch (Exception $e) {
        $results['query_cache'] = 'Query cache not available or failed: ' . $e->getMessage();
        // Don't mark as failure since query cache might not be enabled
    }
    
    // 7. Update cache timestamp in settings
    try {
        setSetting('last_cache_clear', date('Y-m-d H:i:s'), 'Last time system cache was cleared');
        $results['timestamp'] = 'Cache clear timestamp updated';
    } catch (Exception $e) {
        $results['timestamp'] = 'Failed to update timestamp: ' . $e->getMessage();
        $overallSuccess = false;
    }
    
    // Log the cache clear operation
    $currentUser = getCurrentUser();
    error_log("SCIMS Cache Clear: Performed by {$currentUser['username']} at " . date('Y-m-d H:i:s'));
    
    // Return results
    echo json_encode([
        'success' => $overallSuccess,
        'message' => $overallSuccess ? 'Cache cleared successfully' : 'Cache clearing completed with some errors',
        'details' => $results,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}

/**
 * Recursively delete directory contents
 */
function clearDirectory(string $dir): int {
    $count = 0;
    
    if (!is_dir($dir)) {
        return $count;
    }
    
    $files = array_diff(scandir($dir), ['.', '..']);
    
    foreach ($files as $file) {
        $path = $dir . DIRECTORY_SEPARATOR . $file;
        
        if (is_dir($path)) {
            $count += clearDirectory($path);
            rmdir($path);
        } else {
            if (unlink($path)) {
                $count++;
            }
        }
    }
    
    return $count;
}

/**
 * Get cache statistics
 */
function getCacheStats(): array {
    $stats = [];
    
    // OPcache stats
    if (function_exists('opcache_get_status')) {
        $opcacheStatus = opcache_get_status();
        if ($opcacheStatus) {
            $stats['opcache'] = [
                'enabled' => $opcacheStatus['opcache_enabled'],
                'cache_full' => $opcacheStatus['cache_full'],
                'memory_usage' => $opcacheStatus['memory_usage'],
                'opcache_statistics' => $opcacheStatus['opcache_statistics']
            ];
        }
    }
    
    // Session stats
    $sessionPath = session_save_path() ?: sys_get_temp_dir();
    $sessionFiles = glob($sessionPath . '/sess_*');
    $stats['sessions'] = [
        'count' => count($sessionFiles ?: []),
        'path' => $sessionPath
    ];
    
    // Memory usage
    $stats['memory'] = [
        'current' => memory_get_usage(true),
        'peak' => memory_get_peak_usage(true),
        'limit' => ini_get('memory_limit')
    ];
    
    return $stats;
}
?>
