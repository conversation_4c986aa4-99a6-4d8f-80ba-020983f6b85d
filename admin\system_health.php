<?php
/**
 * SCIMS System Health Check
 * Comprehensive system diagnostics and error detection
 */

define('SCIMS_ACCESS', true);
require_once '../includes/config.php';

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$pageTitle = 'System Health Check';
include 'includes/header.php';

// Health check results
$healthChecks = [];
$overallHealth = true;

// 1. Database Connection Test
try {
    $db = getDB();
    $healthChecks['database'] = [
        'status' => 'success',
        'message' => 'Database connection successful',
        'details' => 'Connected to ' . DB_NAME . ' on ' . DB_HOST
    ];
} catch (Exception $e) {
    $healthChecks['database'] = [
        'status' => 'error',
        'message' => 'Database connection failed',
        'details' => $e->getMessage()
    ];
    $overallHealth = false;
}

// 2. Required Tables Check
$requiredTables = [
    'admin_users', 'events', 'departments', 'sports', 'venues', 
    'matches', 'match_participants', 'scores', 'department_standings'
];

$missingTables = [];
foreach ($requiredTables as $table) {
    try {
        $exists = tableExists($table);
        if (!$exists) {
            $missingTables[] = $table;
        }
    } catch (Exception $e) {
        $missingTables[] = $table . ' (error checking)';
    }
}

if (empty($missingTables)) {
    $healthChecks['tables'] = [
        'status' => 'success',
        'message' => 'All required tables exist',
        'details' => count($requiredTables) . ' tables verified'
    ];
} else {
    $healthChecks['tables'] = [
        'status' => 'error',
        'message' => 'Missing tables detected',
        'details' => 'Missing: ' . implode(', ', $missingTables)
    ];
    $overallHealth = false;
}

// 3. File Permissions Check
$criticalFiles = [
    '../includes/config.php',
    '../includes/functions.php',
    '../includes/helpers.php'
];

$permissionIssues = [];
foreach ($criticalFiles as $file) {
    if (!file_exists($file)) {
        $permissionIssues[] = $file . ' (missing)';
    } elseif (!is_readable($file)) {
        $permissionIssues[] = $file . ' (not readable)';
    }
}

if (empty($permissionIssues)) {
    $healthChecks['permissions'] = [
        'status' => 'success',
        'message' => 'File permissions OK',
        'details' => 'All critical files accessible'
    ];
} else {
    $healthChecks['permissions'] = [
        'status' => 'warning',
        'message' => 'File permission issues',
        'details' => implode(', ', $permissionIssues)
    ];
}

// 4. PHP Configuration Check
$phpChecks = [];
if (version_compare(PHP_VERSION, '7.4.0', '<')) {
    $phpChecks[] = 'PHP version too old (' . PHP_VERSION . ')';
}
if (!extension_loaded('pdo_mysql')) {
    $phpChecks[] = 'PDO MySQL extension missing';
}
if (!extension_loaded('mbstring')) {
    $phpChecks[] = 'Mbstring extension missing';
}

if (empty($phpChecks)) {
    $healthChecks['php'] = [
        'status' => 'success',
        'message' => 'PHP configuration OK',
        'details' => 'PHP ' . PHP_VERSION . ' with required extensions'
    ];
} else {
    $healthChecks['php'] = [
        'status' => 'error',
        'message' => 'PHP configuration issues',
        'details' => implode(', ', $phpChecks)
    ];
    $overallHealth = false;
}

// 5. Sample Data Check
try {
    $adminCount = fetchOne("SELECT COUNT(*) as count FROM admin_users")['count'];
    $deptCount = fetchOne("SELECT COUNT(*) as count FROM departments")['count'];
    $sportCount = fetchOne("SELECT COUNT(*) as count FROM sports")['count'];
    
    if ($adminCount > 0 && $deptCount > 0 && $sportCount > 0) {
        $healthChecks['data'] = [
            'status' => 'success',
            'message' => 'Sample data present',
            'details' => "Admins: $adminCount, Departments: $deptCount, Sports: $sportCount"
        ];
    } else {
        $healthChecks['data'] = [
            'status' => 'warning',
            'message' => 'Limited sample data',
            'details' => "Admins: $adminCount, Departments: $deptCount, Sports: $sportCount"
        ];
    }
} catch (Exception $e) {
    $healthChecks['data'] = [
        'status' => 'error',
        'message' => 'Cannot check sample data',
        'details' => $e->getMessage()
    ];
}

// 6. Module Accessibility Check
$modules = [
    'Dashboard' => 'dashboard.php',
    'Events' => 'events/',
    'Sports' => 'sports/',
    'Departments' => 'departments/',
    'Matches' => 'matches/',
    'Scores' => 'scores/',
    'Reports' => 'reports/standings.php'
];

$moduleIssues = [];
foreach ($modules as $name => $path) {
    $fullPath = __DIR__ . '/' . $path;
    if (is_dir($fullPath)) {
        $fullPath .= '/index.php';
    }
    
    if (!file_exists($fullPath)) {
        $moduleIssues[] = "$name ($path)";
    }
}

if (empty($moduleIssues)) {
    $healthChecks['modules'] = [
        'status' => 'success',
        'message' => 'All modules accessible',
        'details' => count($modules) . ' modules verified'
    ];
} else {
    $healthChecks['modules'] = [
        'status' => 'error',
        'message' => 'Module accessibility issues',
        'details' => 'Issues: ' . implode(', ', $moduleIssues)
    ];
    $overallHealth = false;
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="icon-heartbeat"></i> System Health Check
                    </h3>
                    <div class="card-tools">
                        <span class="badge badge-<?php echo $overallHealth ? 'success' : 'danger'; ?>">
                            <?php echo $overallHealth ? 'HEALTHY' : 'ISSUES DETECTED'; ?>
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    
                    <?php if (!$overallHealth): ?>
                    <div class="alert alert-danger">
                        <h5><i class="icon-warning"></i> System Issues Detected!</h5>
                        Critical issues have been found that may affect system functionality. Please review the details below.
                    </div>
                    <?php else: ?>
                    <div class="alert alert-success">
                        <h5><i class="icon-check"></i> System Healthy!</h5>
                        All critical system components are functioning properly.
                    </div>
                    <?php endif; ?>

                    <div class="row">
                        <?php foreach ($healthChecks as $check => $result): ?>
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-2">
                                        <?php
                                        $iconClass = 'icon-';
                                        $badgeClass = 'badge-';
                                        switch ($result['status']) {
                                            case 'success':
                                                $iconClass .= 'check text-success';
                                                $badgeClass .= 'success';
                                                break;
                                            case 'warning':
                                                $iconClass .= 'warning text-warning';
                                                $badgeClass .= 'warning';
                                                break;
                                            case 'error':
                                                $iconClass .= 'x text-danger';
                                                $badgeClass .= 'danger';
                                                break;
                                        }
                                        ?>
                                        <i class="<?php echo $iconClass; ?>"></i>
                                        <div>
                                            <h6 class="mb-0"><?php echo ucfirst(str_replace('_', ' ', $check)); ?></h6>
                                            <span class="badge <?php echo $badgeClass; ?>"><?php echo strtoupper($result['status']); ?></span>
                                        </div>
                                    </div>
                                    <p class="mb-1"><strong><?php echo htmlspecialchars($result['message']); ?></strong></p>
                                    <small class="text-muted"><?php echo htmlspecialchars($result['details']); ?></small>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>

                    <?php if (!$overallHealth): ?>
                    <div class="mt-4">
                        <h5>Recommended Actions:</h5>
                        <ul class="list-unstyled">
                            <?php if (isset($healthChecks['database']) && $healthChecks['database']['status'] === 'error'): ?>
                            <li><i class="icon-chevron-right text-primary"></i> <strong>Database Issues:</strong> Run the <a href="../sql/setup_database.php" target="_blank">database setup script</a></li>
                            <?php endif; ?>
                            
                            <?php if (isset($healthChecks['tables']) && $healthChecks['tables']['status'] === 'error'): ?>
                            <li><i class="icon-chevron-right text-primary"></i> <strong>Missing Tables:</strong> Execute the database setup to create missing tables</li>
                            <?php endif; ?>
                            
                            <?php if (isset($healthChecks['php']) && $healthChecks['php']['status'] === 'error'): ?>
                            <li><i class="icon-chevron-right text-primary"></i> <strong>PHP Issues:</strong> Update PHP version or install missing extensions</li>
                            <?php endif; ?>
                            
                            <?php if (isset($healthChecks['modules']) && $healthChecks['modules']['status'] === 'error'): ?>
                            <li><i class="icon-chevron-right text-primary"></i> <strong>Module Issues:</strong> Check file permissions and ensure all module files exist</li>
                            <?php endif; ?>
                        </ul>
                    </div>
                    <?php endif; ?>

                    <div class="mt-4">
                        <a href="dashboard.php" class="btn btn-primary">
                            <i class="icon-chevron-left"></i> Back to Dashboard
                        </a>
                        <button onclick="location.reload()" class="btn btn-secondary">
                            <i class="icon-refresh"></i> Refresh Check
                        </button>
                        <?php if (!$overallHealth): ?>
                        <a href="../sql/setup_database.php" class="btn btn-warning" target="_blank">
                            <i class="icon-database"></i> Run Database Setup
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
