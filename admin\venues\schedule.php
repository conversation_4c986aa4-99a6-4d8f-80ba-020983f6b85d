<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Venues Management - Venue Schedule
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication
requireAuth();

$venueId = (int)($_GET['venue_id'] ?? 0);

if (!$venueId) {
    header('Location: index.php?message=Invalid venue ID&type=error');
    exit;
}

// Get venue details
$venue = fetchOne("SELECT * FROM venues WHERE venue_id = ?", [$venueId]);

if (!$venue) {
    header('Location: index.php?message=Venue not found&type=error');
    exit;
}

// Get date filter
$selectedDate = $_GET['date'] ?? date('Y-m-d');
$startDate = date('Y-m-d', strtotime($selectedDate . ' -7 days'));
$endDate = date('Y-m-d', strtotime($selectedDate . ' +7 days'));

// Get matches for the venue within date range
$matches = fetchAll("
    SELECT m.*, s.name as sport_name, s.category as sport_category,
           e.name as event_name,
           (SELECT COUNT(*) FROM match_participants WHERE match_id = m.match_id) as participant_count
    FROM matches m
    JOIN sports s ON m.sport_id = s.sport_id
    JOIN events e ON m.event_id = e.event_id
    WHERE m.venue_id = ? AND m.match_date BETWEEN ? AND ?
    ORDER BY m.match_date ASC, m.match_time ASC
", [$venueId, $startDate, $endDate]);

// Group matches by date
$matchesByDate = [];
foreach ($matches as $match) {
    $date = $match['match_date'];
    if (!isset($matchesByDate[$date])) {
        $matchesByDate[$date] = [];
    }
    $matchesByDate[$date][] = $match;
}

// Generate date range for calendar
$dateRange = [];
$currentDate = $startDate;
while ($currentDate <= $endDate) {
    $dateRange[] = $currentDate;
    $currentDate = date('Y-m-d', strtotime($currentDate . ' +1 day'));
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($venue['name']); ?> Schedule - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>
    
    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1><?php echo htmlspecialchars($venue['name']); ?> Schedule</h1>
                <nav class="breadcrumb">
                    <a href="../dashboard.php">Dashboard</a>
                    <span>/</span>
                    <a href="index.php">Venues</a>
                    <span>/</span>
                    <a href="view.php?id=<?php echo $venue['venue_id']; ?>"><?php echo htmlspecialchars($venue['name']); ?></a>
                    <span>/</span>
                    <span>Schedule</span>
                </nav>
            </div>
            <div class="page-actions">
                <input type="date" id="dateFilter" value="<?php echo $selectedDate; ?>" class="form-control">
                <a href="view.php?id=<?php echo $venue['venue_id']; ?>" class="btn btn-outline">
                    <i class="icon-arrow-left"></i>
                    Back to Details
                </a>
            </div>
        </div>
        
        <!-- Venue Info -->
        <div class="venue-info-bar">
            <div class="venue-details">
                <h3><?php echo htmlspecialchars($venue['name']); ?></h3>
                <?php if ($venue['location']): ?>
                    <p><i class="icon-location"></i> <?php echo htmlspecialchars($venue['location']); ?></p>
                <?php endif; ?>
                <p><i class="icon-users"></i> Capacity: <?php echo number_format($venue['capacity']); ?></p>
            </div>
            <div class="venue-status">
                <span class="status-badge status-<?php echo $venue['status']; ?>">
                    <?php echo ucfirst($venue['status']); ?>
                </span>
            </div>
        </div>
        
        <!-- Schedule Calendar -->
        <div class="schedule-container">
            <div class="schedule-header">
                <h2>Weekly Schedule</h2>
                <div class="schedule-navigation">
                    <a href="?venue_id=<?php echo $venueId; ?>&date=<?php echo date('Y-m-d', strtotime($selectedDate . ' -7 days')); ?>" 
                       class="btn btn-sm btn-outline">
                        <i class="icon-chevron-left"></i> Previous Week
                    </a>
                    <span class="current-week">
                        <?php echo date('M j', strtotime($startDate)); ?> - <?php echo date('M j, Y', strtotime($endDate)); ?>
                    </span>
                    <a href="?venue_id=<?php echo $venueId; ?>&date=<?php echo date('Y-m-d', strtotime($selectedDate . ' +7 days')); ?>" 
                       class="btn btn-sm btn-outline">
                        Next Week <i class="icon-chevron-right"></i>
                    </a>
                </div>
            </div>
            
            <div class="schedule-grid">
                <?php foreach ($dateRange as $date): ?>
                    <div class="schedule-day <?php echo $date === date('Y-m-d') ? 'today' : ''; ?>">
                        <div class="day-header">
                            <div class="day-name"><?php echo date('D', strtotime($date)); ?></div>
                            <div class="day-date"><?php echo date('M j', strtotime($date)); ?></div>
                        </div>
                        
                        <div class="day-matches">
                            <?php if (isset($matchesByDate[$date])): ?>
                                <?php foreach ($matchesByDate[$date] as $match): ?>
                                    <div class="match-block status-<?php echo $match['status']; ?>">
                                        <div class="match-time">
                                            <?php echo formatTime($match['match_time']); ?>
                                        </div>
                                        <div class="match-sport">
                                            <?php echo htmlspecialchars($match['sport_name']); ?>
                                        </div>
                                        <div class="match-event">
                                            <?php echo htmlspecialchars($match['event_name']); ?>
                                        </div>
                                        <div class="match-participants">
                                            <?php echo $match['participant_count']; ?> participants
                                        </div>
                                        <div class="match-status">
                                            <span class="status-indicator status-<?php echo $match['status']; ?>">
                                                <?php echo ucfirst($match['status']); ?>
                                            </span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="no-matches">
                                    <span class="text-muted">No matches scheduled</span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        
        <!-- Match List View -->
        <div class="matches-list-container">
            <div class="matches-header">
                <h2>Detailed Schedule</h2>
                <span class="matches-count"><?php echo count($matches); ?> matches in this period</span>
            </div>
            
            <?php if (empty($matches)): ?>
                <div class="no-data">
                    <h3>No matches scheduled</h3>
                    <p>This venue has no matches scheduled for the selected period.</p>
                </div>
            <?php else: ?>
                <div class="matches-table">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Date & Time</th>
                                <th>Sport</th>
                                <th>Event</th>
                                <th>Participants</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($matches as $match): ?>
                                <tr>
                                    <td>
                                        <div class="datetime-info">
                                            <strong><?php echo formatDate($match['match_date']); ?></strong>
                                            <br><small><?php echo formatTime($match['match_time']); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="sport-name"><?php echo htmlspecialchars($match['sport_name']); ?></span>
                                        <br><small class="category-badge category-<?php echo $match['sport_category']; ?>">
                                            <?php echo ucfirst(str_replace('_', ' ', $match['sport_category'])); ?>
                                        </small>
                                    </td>
                                    <td><?php echo htmlspecialchars($match['event_name']); ?></td>
                                    <td class="text-center"><?php echo $match['participant_count']; ?></td>
                                    <td>
                                        <span class="status-badge status-<?php echo $match['status']; ?>">
                                            <?php echo ucfirst($match['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="../matches/view.php?id=<?php echo $match['match_id']; ?>" 
                                               class="btn btn-xs btn-outline" title="View Match">
                                                <i class="icon-eye"></i>
                                            </a>
                                            <?php if ($match['status'] === 'scheduled'): ?>
                                                <a href="../scores/record.php?match_id=<?php echo $match['match_id']; ?>" 
                                                   class="btn btn-xs btn-success" title="Record Score">
                                                    <i class="icon-edit"></i>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </main>
    
    <script src="../../assets/js/admin.js"></script>
    <script>
        // Date filter functionality
        document.getElementById('dateFilter').addEventListener('change', function() {
            const selectedDate = this.value;
            const venueId = <?php echo $venueId; ?>;
            window.location.href = `?venue_id=${venueId}&date=${selectedDate}`;
        });
        
        // Auto-refresh every 30 seconds for live updates
        setInterval(function() {
            if (!document.hidden) {
                location.reload();
            }
        }, 30000);
    </script>
</body>
</html>
