<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Create Department
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication
requireAuth();

$errors = [];
$formData = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('Invalid security token');
        }
        
        // Sanitize and validate input
        $formData = [
            'name' => sanitizeInput($_POST['name'] ?? ''),
            'abbreviation' => sanitizeInput($_POST['abbreviation'] ?? ''),
            'color_code' => sanitizeInput($_POST['color_code'] ?? '#000000'),
            'contact_person' => sanitizeInput($_POST['contact_person'] ?? ''),
            'email' => sanitizeInput($_POST['email'] ?? ''),
            'phone' => sanitizeInput($_POST['phone'] ?? ''),
            'status' => sanitizeInput($_POST['status'] ?? 'active')
        ];
        
        // Validation
        if (empty($formData['name'])) {
            $errors[] = 'Department name is required';
        }
        
        if (empty($formData['abbreviation'])) {
            $errors[] = 'Department abbreviation is required';
        } elseif (strlen($formData['abbreviation']) > 10) {
            $errors[] = 'Abbreviation must be 10 characters or less';
        }
        
        if (!empty($formData['email']) && !isValidEmail($formData['email'])) {
            $errors[] = 'Please enter a valid email address';
        }
        
        if (!empty($formData['phone']) && !isValidPhone($formData['phone'])) {
            $errors[] = 'Please enter a valid phone number';
        }
        
        // Check for duplicate abbreviation
        if (empty($errors)) {
            $existing = fetchOne("SELECT dept_id FROM departments WHERE abbreviation = ?", [$formData['abbreviation']]);
            if ($existing) {
                $errors[] = 'Department abbreviation already exists';
            }
        }
        
        // Validate color code
        if (!preg_match('/^#[0-9A-Fa-f]{6}$/', $formData['color_code'])) {
            $formData['color_code'] = '#000000';
        }
        
        if (empty($errors)) {
            // Insert department
            $deptId = insertRecord('departments', $formData);
            
            logActivity('department_created', "Department created: {$formData['name']} (ID: {$deptId})");
            redirect('view.php?id=' . $deptId, 'Department created successfully!', 'success');
        }
        
    } catch (Exception $e) {
        $errors[] = $e->getMessage();
    }
}

$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Department - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>
    
    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1>Create Department</h1>
                <nav class="breadcrumb">
                    <a href="../dashboard.php">Dashboard</a>
                    <span>/</span>
                    <a href="index.php">Departments</a>
                    <span>/</span>
                    <span>Create</span>
                </nav>
            </div>
        </div>
        
        <?php if (!empty($errors)): ?>
            <div class="alert alert-error">
                <ul>
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <div class="form-container">
            <form method="POST" class="admin-form" data-validate>
                <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                
                <div class="form-section">
                    <h2>Basic Information</h2>
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="name">Department Name *</label>
                            <input type="text" id="name" name="name" 
                                   value="<?php echo htmlspecialchars($formData['name'] ?? ''); ?>" 
                                   required class="form-control"
                                   placeholder="e.g., College of Engineering">
                            <small class="form-help">Full official name of the department</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="abbreviation">Abbreviation *</label>
                            <input type="text" id="abbreviation" name="abbreviation" 
                                   value="<?php echo htmlspecialchars($formData['abbreviation'] ?? ''); ?>" 
                                   required class="form-control" maxlength="10"
                                   placeholder="e.g., COE" style="text-transform: uppercase;">
                            <small class="form-help">Short abbreviation (max 10 characters)</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="color_code">Department Color</label>
                            <div class="color-input-group">
                                <input type="color" id="color_code" name="color_code" 
                                       value="<?php echo htmlspecialchars($formData['color_code'] ?? '#2563eb'); ?>" 
                                       class="form-control color-picker">
                                <input type="text" id="color_text" 
                                       value="<?php echo htmlspecialchars($formData['color_code'] ?? '#2563eb'); ?>" 
                                       class="form-control color-text" pattern="^#[0-9A-Fa-f]{6}$">
                            </div>
                            <small class="form-help">Color used for department identification</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select id="status" name="status" class="form-control">
                                <option value="active" <?php echo ($formData['status'] ?? 'active') === 'active' ? 'selected' : ''; ?>>Active</option>
                                <option value="inactive" <?php echo ($formData['status'] ?? '') === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="form-section">
                    <h2>Contact Information</h2>
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="contact_person">Contact Person</label>
                            <input type="text" id="contact_person" name="contact_person" 
                                   value="<?php echo htmlspecialchars($formData['contact_person'] ?? ''); ?>" 
                                   class="form-control"
                                   placeholder="e.g., Dr. John Smith">
                        </div>
                        
                        <div class="form-group">
                            <label for="email">Email Address</label>
                            <input type="email" id="email" name="email" 
                                   value="<?php echo htmlspecialchars($formData['email'] ?? ''); ?>" 
                                   class="form-control"
                                   placeholder="e.g., <EMAIL>">
                        </div>
                        
                        <div class="form-group">
                            <label for="phone">Phone Number</label>
                            <input type="tel" id="phone" name="phone" 
                                   value="<?php echo htmlspecialchars($formData['phone'] ?? ''); ?>" 
                                   class="form-control"
                                   placeholder="e.g., 09123456789">
                            <small class="form-help">Philippine mobile number format</small>
                        </div>
                    </div>
                </div>
                
                <!-- Preview Section -->
                <div class="form-section">
                    <h2>Preview</h2>
                    <div class="department-preview">
                        <div class="preview-card">
                            <div class="dept-header">
                                <div class="dept-color" id="preview-color" style="background-color: <?php echo htmlspecialchars($formData['color_code'] ?? '#2563eb'); ?>"></div>
                                <div class="dept-names">
                                    <h4 id="preview-abbreviation"><?php echo htmlspecialchars($formData['abbreviation'] ?? 'DEPT'); ?></h4>
                                    <p id="preview-name"><?php echo htmlspecialchars($formData['name'] ?? 'Department Name'); ?></p>
                                </div>
                            </div>
                            <div class="dept-contact" id="preview-contact">
                                <?php if (!empty($formData['contact_person'])): ?>
                                    <div><strong><?php echo htmlspecialchars($formData['contact_person']); ?></strong></div>
                                <?php endif; ?>
                                <?php if (!empty($formData['email'])): ?>
                                    <div><?php echo htmlspecialchars($formData['email']); ?></div>
                                <?php endif; ?>
                                <?php if (!empty($formData['phone'])): ?>
                                    <div><?php echo htmlspecialchars($formData['phone']); ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="form-actions">
                    <a href="index.php" class="btn btn-secondary">Cancel</a>
                    <button type="submit" class="btn btn-primary">
                        <i class="icon-save"></i>
                        Create Department
                    </button>
                </div>
            </form>
        </div>
    </main>
    
    <script src="../../assets/js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Color picker synchronization
            const colorPicker = document.getElementById('color_code');
            const colorText = document.getElementById('color_text');
            const previewColor = document.getElementById('preview-color');
            
            function updateColor(color) {
                colorPicker.value = color;
                colorText.value = color;
                previewColor.style.backgroundColor = color;
            }
            
            colorPicker.addEventListener('change', function() {
                updateColor(this.value);
            });
            
            colorText.addEventListener('input', function() {
                if (/^#[0-9A-Fa-f]{6}$/.test(this.value)) {
                    updateColor(this.value);
                }
            });
            
            // Live preview updates
            const nameInput = document.getElementById('name');
            const abbreviationInput = document.getElementById('abbreviation');
            const contactInput = document.getElementById('contact_person');
            const emailInput = document.getElementById('email');
            const phoneInput = document.getElementById('phone');
            
            const previewName = document.getElementById('preview-name');
            const previewAbbreviation = document.getElementById('preview-abbreviation');
            const previewContact = document.getElementById('preview-contact');
            
            function updatePreview() {
                previewName.textContent = nameInput.value || 'Department Name';
                previewAbbreviation.textContent = abbreviationInput.value || 'DEPT';
                
                let contactHtml = '';
                if (contactInput.value) {
                    contactHtml += '<div><strong>' + escapeHtml(contactInput.value) + '</strong></div>';
                }
                if (emailInput.value) {
                    contactHtml += '<div>' + escapeHtml(emailInput.value) + '</div>';
                }
                if (phoneInput.value) {
                    contactHtml += '<div>' + escapeHtml(phoneInput.value) + '</div>';
                }
                previewContact.innerHTML = contactHtml;
            }
            
            function escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }
            
            // Add event listeners for live preview
            [nameInput, abbreviationInput, contactInput, emailInput, phoneInput].forEach(input => {
                input.addEventListener('input', updatePreview);
            });
            
            // Auto-uppercase abbreviation
            abbreviationInput.addEventListener('input', function() {
                this.value = this.value.toUpperCase();
                updatePreview();
            });
            
            // Form validation
            const form = document.querySelector('.admin-form');
            form.addEventListener('submit', function(e) {
                const name = nameInput.value.trim();
                const abbreviation = abbreviationInput.value.trim();
                
                if (!name) {
                    e.preventDefault();
                    alert('Department name is required');
                    nameInput.focus();
                    return;
                }
                
                if (!abbreviation) {
                    e.preventDefault();
                    alert('Department abbreviation is required');
                    abbreviationInput.focus();
                    return;
                }
                
                if (abbreviation.length > 10) {
                    e.preventDefault();
                    alert('Abbreviation must be 10 characters or less');
                    abbreviationInput.focus();
                    return;
                }
                
                // Validate email if provided
                const email = emailInput.value.trim();
                if (email && !isValidEmail(email)) {
                    e.preventDefault();
                    alert('Please enter a valid email address');
                    emailInput.focus();
                    return;
                }
                
                // Validate phone if provided
                const phone = phoneInput.value.trim();
                if (phone && !isValidPhone(phone)) {
                    e.preventDefault();
                    alert('Please enter a valid Philippine phone number');
                    phoneInput.focus();
                    return;
                }
            });
            
            function isValidEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }
            
            function isValidPhone(phone) {
                const phoneRegex = /^(\+63|0)?[0-9]{10}$/;
                return phoneRegex.test(phone.replace(/\s/g, ''));
            }
        });
    </script>
</body>
</html>
