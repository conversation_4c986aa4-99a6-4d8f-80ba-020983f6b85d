<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Score Management - Main Page
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication
requireAuth();

// Get ongoing and scheduled matches for score entry
$ongoingMatches = fetchAll("
    SELECT m.match_id, m.match_date, m.match_time, m.status, m.round_type,
           s.name as sport_name, s.category as sport_category, s.scoring_type,
           v.name as venue_name,
           e.name as event_name,
           (SELECT COUNT(*) FROM match_participants WHERE match_id = m.match_id) as participant_count,
           (SELECT COUNT(*) FROM scores WHERE match_id = m.match_id AND is_final = 1) as final_score_count
    FROM matches m
    JOIN sports s ON m.sport_id = s.sport_id
    LEFT JOIN venues v ON m.venue_id = v.venue_id
    JOIN events e ON m.event_id = e.event_id
    WHERE m.status IN ('scheduled', 'ongoing')
    ORDER BY m.match_date ASC, m.match_time ASC
    LIMIT 20
");

// Get recent score entries
$recentScores = fetchAll("
    SELECT sc.score_id, sc.points, sc.position, sc.time_score, sc.distance_score, sc.timestamp,
           m.match_date, m.match_time, m.round_type,
           s.name as sport_name, s.scoring_type,
           mp.participant_name,
           d.name as dept_name, d.abbreviation as dept_abbr,
           au.full_name as recorded_by_name
    FROM scores sc
    JOIN matches m ON sc.match_id = m.match_id
    JOIN sports s ON m.sport_id = s.sport_id
    JOIN match_participants mp ON sc.participant_id = mp.participant_id
    JOIN departments d ON mp.dept_id = d.dept_id
    LEFT JOIN admin_users au ON sc.recorded_by = au.admin_id
    WHERE sc.is_final = 1
    ORDER BY sc.timestamp DESC
    LIMIT 15
");

$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Score Management - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>
    
    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1>Score Management</h1>
                <nav class="breadcrumb">
                    <a href="../dashboard.php">Dashboard</a>
                    <span>/</span>
                    <span>Scores</span>
                </nav>
            </div>
            <div class="page-actions">
                <a href="live-scoring.php" class="btn btn-primary">
                    <i class="icon-play"></i>
                    Live Scoring
                </a>
                <a href="bulk-entry.php" class="btn btn-secondary">
                    <i class="icon-edit"></i>
                    Bulk Entry
                </a>
            </div>
        </div>
        
        <div class="dashboard-grid">
            <!-- Pending Matches -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h2>Matches Pending Score Entry</h2>
                    <span class="badge"><?php echo count($ongoingMatches); ?></span>
                </div>
                <div class="card-content">
                    <?php if (empty($ongoingMatches)): ?>
                        <p class="no-data">No matches pending score entry.</p>
                    <?php else: ?>
                        <div class="matches-list">
                            <?php foreach ($ongoingMatches as $match): ?>
                                <div class="match-item">
                                    <div class="match-info">
                                        <h4><?php echo htmlspecialchars($match['sport_name']); ?></h4>
                                        <p class="match-details">
                                            <?php echo htmlspecialchars($match['event_name']); ?>
                                            <br><small><?php echo ucfirst($match['round_type']); ?></small>
                                        </p>
                                        <div class="match-meta">
                                            <span class="match-date">
                                                <?php echo formatDate($match['match_date']); ?> at 
                                                <?php echo formatTime($match['match_time']); ?>
                                            </span>
                                            <?php if ($match['venue_name']): ?>
                                                <br><small class="venue-name"><?php echo htmlspecialchars($match['venue_name']); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="match-status">
                                        <span class="status-badge status-<?php echo $match['status']; ?>">
                                            <?php echo ucfirst($match['status']); ?>
                                        </span>
                                        <div class="match-stats">
                                            <small><?php echo $match['participant_count']; ?> participants</small>
                                            <?php if ($match['final_score_count'] > 0): ?>
                                                <br><small class="text-success">✓ Scored</small>
                                            <?php else: ?>
                                                <br><small class="text-warning">⏳ Pending</small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="match-actions">
                                        <a href="record.php?match_id=<?php echo $match['match_id']; ?>" 
                                           class="btn btn-sm btn-primary">
                                            <i class="icon-edit"></i>
                                            Record Score
                                        </a>
                                        <?php if ($match['final_score_count'] > 0): ?>
                                            <a href="view.php?match_id=<?php echo $match['match_id']; ?>" 
                                               class="btn btn-sm btn-outline">
                                                <i class="icon-eye"></i>
                                                View Scores
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <?php if (count($ongoingMatches) >= 20): ?>
                            <div class="card-footer">
                                <a href="../matches/?status=ongoing" class="btn btn-outline">View All Matches</a>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Recent Score Entries -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h2>Recent Score Entries</h2>
                    <a href="history.php" class="btn btn-sm btn-outline">View All</a>
                </div>
                <div class="card-content">
                    <?php if (empty($recentScores)): ?>
                        <p class="no-data">No recent score entries.</p>
                    <?php else: ?>
                        <div class="scores-list">
                            <?php foreach ($recentScores as $score): ?>
                                <div class="score-item">
                                    <div class="score-info">
                                        <h4><?php echo htmlspecialchars($score['sport_name']); ?></h4>
                                        <p class="participant-info">
                                            <strong><?php echo htmlspecialchars($score['participant_name']); ?></strong>
                                            <span class="dept-badge" style="background-color: var(--dept-color, #ccc);">
                                                <?php echo htmlspecialchars($score['dept_abbr']); ?>
                                            </span>
                                        </p>
                                        <small class="score-meta">
                                            <?php echo formatDate($score['match_date']); ?> - <?php echo ucfirst($score['round_type']); ?>
                                        </small>
                                    </div>
                                    <div class="score-value">
                                        <?php if ($score['scoring_type'] === 'points'): ?>
                                            <span class="points"><?php echo number_format($score['points'], 0); ?> pts</span>
                                            <?php if ($score['position']): ?>
                                                <br><small class="position"><?php echo ordinal($score['position']); ?> place</small>
                                            <?php endif; ?>
                                        <?php elseif ($score['scoring_type'] === 'time' && $score['time_score']): ?>
                                            <span class="time"><?php echo $score['time_score']; ?></span>
                                            <?php if ($score['position']): ?>
                                                <br><small class="position"><?php echo ordinal($score['position']); ?> place</small>
                                            <?php endif; ?>
                                        <?php elseif ($score['scoring_type'] === 'distance' && $score['distance_score']): ?>
                                            <span class="distance"><?php echo number_format($score['distance_score'], 2); ?>m</span>
                                            <?php if ($score['position']): ?>
                                                <br><small class="position"><?php echo ordinal($score['position']); ?> place</small>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                    <div class="score-timestamp">
                                        <small class="text-muted">
                                            <?php echo timeAgo($score['timestamp']); ?>
                                            <?php if ($score['recorded_by_name']): ?>
                                                <br>by <?php echo htmlspecialchars($score['recorded_by_name']); ?>
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="quick-actions-section">
            <h2>Quick Actions</h2>
            <div class="quick-actions-grid">
                <a href="record.php" class="quick-action-card">
                    <div class="action-icon">
                        <i class="icon-edit"></i>
                    </div>
                    <div class="action-content">
                        <h3>Record Scores</h3>
                        <p>Enter scores for completed matches</p>
                    </div>
                </a>
                
                <a href="live-scoring.php" class="quick-action-card">
                    <div class="action-icon">
                        <i class="icon-play"></i>
                    </div>
                    <div class="action-content">
                        <h3>Live Scoring</h3>
                        <p>Real-time score updates during matches</p>
                    </div>
                </a>
                
                <a href="bulk-entry.php" class="quick-action-card">
                    <div class="action-icon">
                        <i class="icon-upload"></i>
                    </div>
                    <div class="action-content">
                        <h3>Bulk Entry</h3>
                        <p>Import scores from spreadsheet</p>
                    </div>
                </a>
                
                <a href="history.php" class="quick-action-card">
                    <div class="action-icon">
                        <i class="icon-history"></i>
                    </div>
                    <div class="action-content">
                        <h3>Score History</h3>
                        <p>View all recorded scores</p>
                    </div>
                </a>
            </div>
        </div>
    </main>
    
    <script src="../../assets/js/admin.js"></script>
    <script>
        // Auto-refresh every 30 seconds for live updates
        setInterval(function() {
            if (!document.hidden) {
                location.reload();
            }
        }, 30000);
    </script>
</body>
</html>
