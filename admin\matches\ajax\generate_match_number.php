<?php
/**
 * Generate Match Number AJAX Endpoint
 */

define('SCIMS_ACCESS', true);
require_once '../../../includes/config.php';
require_once '../../../includes/functions.php';
require_once '../../../includes/auth.php';

// Require authentication
requireAuth();

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

$eventId = (int)($input['event_id'] ?? 0);
$sportId = (int)($input['sport_id'] ?? 0);

if (!$eventId || !$sportId) {
    echo json_encode(['error' => 'Missing required parameters']);
    exit;
}

try {
    // Get sport abbreviation or use first 3 letters of name
    $sport = fetchOne("SELECT name FROM sports WHERE sport_id = ?", [$sportId]);
    if (!$sport) {
        echo json_encode(['error' => 'Sport not found']);
        exit;
    }
    
    // Create sport code (first 3 letters, uppercase)
    $sportCode = strtoupper(substr(preg_replace('/[^a-zA-Z]/', '', $sport['name']), 0, 3));
    
    // Get next match number for this event and sport
    $lastMatch = fetchOne("
        SELECT match_number 
        FROM matches 
        WHERE event_id = ? AND sport_id = ? AND match_number LIKE ?
        ORDER BY match_id DESC 
        LIMIT 1
    ", [$eventId, $sportId, $sportCode . '%']);
    
    $nextNumber = 1;
    if ($lastMatch && $lastMatch['match_number']) {
        // Extract number from last match number
        preg_match('/(\d+)$/', $lastMatch['match_number'], $matches);
        if (!empty($matches[1])) {
            $nextNumber = (int)$matches[1] + 1;
        }
    }
    
    $matchNumber = $sportCode . sprintf('%03d', $nextNumber);
    
    echo json_encode([
        'match_number' => $matchNumber,
        'sport_code' => $sportCode,
        'sequence' => $nextNumber
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Database error']);
}
?>
