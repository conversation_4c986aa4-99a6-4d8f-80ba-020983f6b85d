<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * System Information
 *
 * @version 1.0
 * <AUTHOR> Development Team
 */

declare(strict_types=1);

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication
requireAuth();

// Only super admin can access this
if (getCurrentUser()['role'] !== 'super_admin') {
    header('Location: ../dashboard.php?message=Access denied&type=error');
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Information - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
    <style>
        /* CRITICAL OVERRIDE: Force system information content to be fully visible */
        html, body {
            height: auto !important;
            overflow-y: auto !important;
            overflow-x: hidden !important;
        }

        .admin-page {
            height: auto !important;
            min-height: 100vh !important;
            grid-template-rows: var(--header-height) auto !important;
        }

        .main-content {
            height: auto !important;
            min-height: calc(100vh - var(--header-height)) !important;
            overflow: visible !important;
            padding-bottom: 6rem !important;
        }

        .settings-container {
            height: auto !important;
            min-height: fit-content !important;
            overflow: visible !important;
            margin-bottom: 4rem !important;
        }

        .settings-content {
            height: auto !important;
            min-height: fit-content !important;
            overflow: visible !important;
        }

        .settings-panel {
            height: auto !important;
            min-height: fit-content !important;
            overflow: visible !important;
            padding-bottom: 3rem !important;
        }

        .system-info-grid {
            height: auto !important;
            min-height: fit-content !important;
            overflow: visible !important;
            margin-bottom: 3rem !important;
        }

        .info-card {
            height: auto !important;
            min-height: fit-content !important;
            overflow: visible !important;
            margin-bottom: 1.5rem !important;
        }

        .action-buttons {
            margin-bottom: 2rem !important;
        }

        /* Ensure last card (Quick Actions) is fully visible */
        .info-card:last-child {
            margin-bottom: 3rem !important;
        }
    </style>
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>

    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1>System Information</h1>
                <nav class="breadcrumb">
                    <a href="../dashboard.php">Dashboard</a>
                    <span>/</span>
                    <span>Settings</span>
                </nav>
            </div>
        </div>

        <!-- Settings Tab Navigation -->
        <div class="profile-tabs-container">
            <nav class="profile-tabs">
                <a href="index.php" class="profile-tab">
                    <i class="icon-settings"></i>
                    General
                </a>
                <a href="security.php" class="profile-tab">
                    <i class="icon-shield"></i>
                    Security
                </a>
                <a href="events.php" class="profile-tab">
                    <i class="icon-trophy"></i>
                    Events
                </a>
                <a href="notifications.php" class="profile-tab">
                    <i class="icon-mail"></i>
                    Notifications
                </a>
                <a href="system.php" class="profile-tab active">
                    <i class="icon-info"></i>
                    System Info
                </a>
            </nav>
        </div>

        <div class="settings-container">
            <!-- System Info Panel -->
            <div class="settings-content">
                <div class="settings-panel active" id="system-panel">
                    <div class="panel-header">
                        <h2>System Information</h2>
                        <p>View current system status and configuration details</p>
                    </div>

                    <div class="system-info-grid">
                        <div class="info-card">
                            <h3>Application</h3>
                            <div class="info-item">
                                <label>Name:</label>
                                <span><?php echo APP_NAME; ?></span>
                            </div>
                            <div class="info-item">
                                <label>Version:</label>
                                <span><?php echo APP_VERSION; ?></span>
                            </div>
                            <div class="info-item">
                                <label>Environment:</label>
                                <span><?php echo DEVELOPMENT_MODE ? 'Development' : 'Production'; ?></span>
                            </div>
                        </div>

                        <div class="info-card">
                            <h3>Server</h3>
                            <div class="info-item">
                                <label>PHP Version:</label>
                                <span><?php echo PHP_VERSION; ?></span>
                            </div>
                            <div class="info-item">
                                <label>Server Software:</label>
                                <span><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></span>
                            </div>
                            <div class="info-item">
                                <label>Timezone:</label>
                                <span><?php echo date_default_timezone_get(); ?></span>
                            </div>
                        </div>

                        <div class="info-card">
                            <h3>Database</h3>
                            <div class="info-item">
                                <label>Host:</label>
                                <span><?php echo DB_HOST; ?></span>
                            </div>
                            <div class="info-item">
                                <label>Database:</label>
                                <span><?php echo DB_NAME; ?></span>
                            </div>
                            <div class="info-item">
                                <label>Charset:</label>
                                <span><?php echo DB_CHARSET; ?></span>
                            </div>
                        </div>

                        <div class="info-card">
                            <h3>Quick Actions</h3>
                            <div class="action-buttons">
                                <a href="../system_health.php" class="btn btn-secondary">
                                    <i class="icon-heart"></i>
                                    System Health
                                </a>
                                <a href="../backup/" class="btn btn-secondary">
                                    <i class="icon-database"></i>
                                    Backup System
                                </a>
                                <button type="button" class="btn btn-secondary" onclick="clearCache()">
                                    <i class="icon-refresh"></i>
                                    Clear Cache
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="../../assets/js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // CRITICAL FIX: Force content visibility
            function forceContentVisibility() {
                // Force body and html to allow scrolling
                document.documentElement.style.height = 'auto';
                document.documentElement.style.overflow = 'visible';
                document.body.style.height = 'auto';
                document.body.style.overflow = 'visible';

                // Force admin page layout
                const adminPage = document.querySelector('.admin-page');
                if (adminPage) {
                    adminPage.style.height = 'auto';
                    adminPage.style.minHeight = '100vh';
                    adminPage.style.gridTemplateRows = 'var(--header-height) auto';
                }

                // Force main content
                const mainContent = document.querySelector('.main-content');
                if (mainContent) {
                    mainContent.style.height = 'auto';
                    mainContent.style.minHeight = 'calc(100vh - var(--header-height))';
                    mainContent.style.overflow = 'visible';
                    mainContent.style.paddingBottom = '5rem';
                }

                // Force settings container
                const settingsContainer = document.querySelector('.settings-container');
                if (settingsContainer) {
                    settingsContainer.style.height = 'auto';
                    settingsContainer.style.minHeight = 'fit-content';
                    settingsContainer.style.overflow = 'visible';
                    settingsContainer.style.marginBottom = '4rem';
                }

                // Force settings content
                const settingsContent = document.querySelector('.settings-content');
                if (settingsContent) {
                    settingsContent.style.height = 'auto';
                    settingsContent.style.minHeight = 'fit-content';
                    settingsContent.style.overflow = 'visible';
                }

                // Force system panel
                const systemPanel = document.querySelector('#system-panel');
                if (systemPanel) {
                    systemPanel.style.height = 'auto';
                    systemPanel.style.minHeight = 'fit-content';
                    systemPanel.style.overflow = 'visible';
                    systemPanel.style.paddingBottom = '3rem';
                }

                // Force system info grid
                const systemGrid = document.querySelector('.system-info-grid');
                if (systemGrid) {
                    systemGrid.style.height = 'auto';
                    systemGrid.style.minHeight = 'fit-content';
                    systemGrid.style.overflow = 'visible';
                    systemGrid.style.marginBottom = '3rem';
                }

                // Force all info cards
                const infoCards = document.querySelectorAll('.info-card');
                infoCards.forEach(card => {
                    card.style.height = 'auto';
                    card.style.minHeight = 'fit-content';
                    card.style.overflow = 'visible';
                    card.style.marginBottom = '1.5rem';
                });

                // Force action buttons
                const actionButtons = document.querySelector('.action-buttons');
                if (actionButtons) {
                    actionButtons.style.marginBottom = '2rem';
                }
            }

            // Apply fixes immediately and after a delay
            forceContentVisibility();
            setTimeout(forceContentVisibility, 100);
            setTimeout(forceContentVisibility, 500);

            // Settings tab navigation enhancement
            const profileTabs = document.querySelectorAll('.profile-tab');
            profileTabs.forEach(tab => {
                tab.addEventListener('click', function(e) {
                    // Add loading state for better UX
                    if (!this.classList.contains('active')) {
                        this.style.opacity = '0.7';
                        this.innerHTML += ' <i class="icon-spinner" style="animation: spin 1s linear infinite;"></i>';
                    }
                });
            });

            // Add CSS for spinner animation and force visibility
            const style = document.createElement('style');
            style.textContent = `
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }

                /* EMERGENCY OVERRIDE - Force all content visible */
                html, body {
                    height: auto !important;
                    overflow: visible !important;
                }

                .admin-page {
                    height: auto !important;
                    grid-template-rows: var(--header-height) auto !important;
                }

                .main-content {
                    height: auto !important;
                    overflow: visible !important;
                    padding-bottom: 5rem !important;
                }

                .settings-container,
                .settings-content,
                .settings-panel,
                .system-info-grid,
                .info-card {
                    height: auto !important;
                    overflow: visible !important;
                }
            `;
            document.head.appendChild(style);
        });

        // Global functions for button actions
        function clearCache() {
            fetch('clear_cache.php', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Cache cleared successfully!');
                } else {
                    alert('Failed to clear cache');
                }
            })
            .catch(error => {
                alert('Error clearing cache');
            });
        }
    </script>
</body>
</html>
