<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Backup Management - Coming Soon
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication
requireAuth();

// Only super admin can access this
if (getCurrentUser()['role'] !== 'super_admin') {
    header('Location: ../dashboard.php?message=Access denied&type=error');
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backup Management - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>
    
    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1>Backup Management</h1>
                <nav class="breadcrumb">
                    <a href="../dashboard.php">Dashboard</a>
                    <span>/</span>
                    <span>Backup</span>
                </nav>
            </div>
        </div>
        
        <div class="coming-soon">
            <div class="coming-soon-content">
                <h2>💾 Backup Management</h2>
                <p>This module is currently under development and will be available in a future update.</p>
                
                <div class="planned-features">
                    <h3>Planned Features:</h3>
                    <ul>
                        <li>✅ Automated database backups</li>
                        <li>✅ File system backups</li>
                        <li>✅ Scheduled backup jobs</li>
                        <li>✅ Backup restoration</li>
                        <li>✅ Cloud storage integration</li>
                        <li>✅ Backup verification and testing</li>
                    </ul>
                </div>
                
                <div class="backup-info">
                    <h3>Important Backup Information:</h3>
                    <div class="info-cards">
                        <div class="info-card warning">
                            <h4>⚠️ Manual Backup Recommended</h4>
                            <p>Until this module is complete, we recommend manually backing up your database regularly using phpMyAdmin or similar tools.</p>
                        </div>
                        <div class="info-card info">
                            <h4>📁 Database Location</h4>
                            <p>Database: <code><?php echo DB_NAME; ?></code></p>
                            <p>Host: <code><?php echo DB_HOST; ?></code></p>
                        </div>
                        <div class="info-card success">
                            <h4>✅ What to Backup</h4>
                            <ul>
                                <li>Database tables and data</li>
                                <li>Uploaded files and images</li>
                                <li>Configuration files</li>
                                <li>Custom modifications</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="alternative-actions">
                    <h3>Available Actions:</h3>
                    <div class="action-cards">
                        <a href="../system_health.php" class="action-card">
                            <i class="icon-heart"></i>
                            <h4>System Health</h4>
                            <p>Check system status and integrity</p>
                        </a>
                        <a href="../settings/" class="action-card">
                            <i class="icon-settings"></i>
                            <h4>System Settings</h4>
                            <p>Configure system preferences</p>
                        </a>
                        <a href="../logs/" class="action-card">
                            <i class="icon-file"></i>
                            <h4>Activity Logs</h4>
                            <p>View system activity and errors</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <script src="../../assets/js/admin.js"></script>
</body>
</html>
