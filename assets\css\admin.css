/**
 * Samar College Intramurals Management System (SCIMS)
 * Admin Panel Styles
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

/* CSS Custom Properties (Variables) */
:root {
    /* Primary Colors */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    
    /* Secondary Colors */
    --secondary-color: #64748b;
    --secondary-dark: #475569;
    --secondary-light: #94a3b8;
    
    /* Status Colors */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #06b6d4;
    
    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* Layout */
    --sidebar-width: 280px;
    --header-height: 70px;
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    
    /* Typography */
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    
    /* Transitions */
    --transition: all 0.2s ease-in-out;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-700);
    background-color: var(--gray-50);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Login Page Styles */
.login-page {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    position: relative;
    overflow-x: hidden;
}

.login-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.login-container {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 1fr;
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 1;
}

.login-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow:
        0 20px 25px -5px rgba(0, 0, 0, 0.1),
        0 10px 10px -5px rgba(0, 0, 0, 0.04),
        0 0 0 1px rgba(255, 255, 255, 0.05);
    overflow: hidden;
    transform: translateY(0);
    transition: all 0.3s ease;
    animation: slideInLeft 0.6s ease-out;
}

.login-card:hover {
    transform: translateY(-2px);
    box-shadow:
        0 25px 50px -12px rgba(0, 0, 0, 0.15),
        0 20px 25px -5px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.05);
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.login-header {
    text-align: center;
    padding: 2.5rem 2rem 1.5rem;
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
    border-bottom: 1px solid var(--gray-200);
    position: relative;
}

.login-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: 2px;
}

.login-header .logo {
    width: 70px;
    height: 70px;
    margin-bottom: 1rem;
    border-radius: 50%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.login-header .logo:hover {
    transform: scale(1.05);
}

.login-header h1 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.login-header p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    font-weight: 500;
    letter-spacing: 0.025em;
}

.login-form-container {
    padding: 2.5rem;
    background: var(--white);
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: 1.75rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.form-group label {
    font-weight: 600;
    color: var(--gray-700);
    font-size: var(--font-size-sm);
    letter-spacing: 0.025em;
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group i {
    position: absolute;
    left: 1rem;
    color: var(--gray-400);
    z-index: 1;
    font-size: 1.1rem;
    transition: color 0.3s ease;
}

.input-group input {
    width: 100%;
    padding: 1rem 1rem 1rem 2.75rem;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    transition: all 0.3s ease;
    background: var(--white);
    font-weight: 500;
}

.input-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow:
        0 0 0 3px rgb(37 99 235 / 0.1),
        0 1px 3px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.input-group input:focus + .password-toggle,
.input-group:focus-within i {
    color: var(--primary-color);
}

.input-group input.error {
    border-color: var(--error-color);
    box-shadow: 0 0 0 3px rgb(239 68 68 / 0.1);
}

.field-error {
    color: var(--error-color);
    font-size: var(--font-size-xs);
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.password-toggle {
    position: absolute;
    right: 1rem;
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: 0.25rem;
    z-index: 1;
}

.checkbox-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-700);
    padding: 0.5rem 0;
    transition: color 0.3s ease;
    position: relative;
}

.checkbox-container:hover {
    color: var(--primary-color);
}

.checkbox-container input[type="checkbox"] {
    width: 1.125rem;
    height: 1.125rem;
    accent-color: var(--primary-color);
    cursor: pointer;
    border-radius: 3px;
    transition: all 0.3s ease;
}

.checkbox-container input[type="checkbox"]:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.checkbox-container input[type="checkbox"]:checked {
    transform: scale(1.05);
}

/* Custom checkbox styling for better cross-browser support */
.custom-checkbox {
    position: relative;
    display: inline-block;
    width: 1.125rem;
    height: 1.125rem;
    margin-right: 0.75rem;
}

.custom-checkbox input[type="checkbox"] {
    opacity: 0;
    position: absolute;
    width: 100%;
    height: 100%;
    margin: 0;
    cursor: pointer;
}

.custom-checkbox .checkmark {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--white);
    border: 2px solid var(--gray-300);
    border-radius: 3px;
    transition: all 0.3s ease;
}

.custom-checkbox:hover .checkmark {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.custom-checkbox input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.custom-checkbox input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 3px;
    top: 0px;
    width: 5px;
    height: 9px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.custom-checkbox input[type="checkbox"]:focus + .checkmark {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.btn-secondary {
    background: var(--gray-100);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.btn-secondary:hover {
    background: var(--gray-200);
    color: var(--gray-800);
    border-color: var(--gray-400);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.btn-login {
    width: 100%;
    padding: 1.25rem;
    font-size: var(--font-size-lg);
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border: none;
    border-radius: var(--border-radius);
    color: var(--white);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-login::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-login:hover::before {
    left: 100%;
}

.btn-login:hover {
    transform: translateY(-2px);
    box-shadow:
        0 10px 25px rgba(37, 99, 235, 0.3),
        0 5px 10px rgba(0, 0, 0, 0.1);
}

.btn-login:active {
    transform: translateY(0);
}

.btn-login:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.login-footer {
    text-align: center;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--gray-200);
}

.forgot-password-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-color);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: 600;
    transition: all 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
}

.forgot-password-link:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.login-info {
    color: var(--white);
    padding: 2rem;
    animation: slideInRight 0.6s ease-out 0.3s both;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.login-info h2 {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--white), rgba(255, 255, 255, 0.8));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.login-info > p {
    font-size: var(--font-size-lg);
    margin-bottom: 3rem;
    opacity: 0.9;
    line-height: 1.6;
}

.features {
    display: flex;
    flex-direction: column;
    gap: 2.5rem;
}

.feature {
    display: flex;
    align-items: flex-start;
    gap: 1.25rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.feature:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.feature i {
    font-size: 2.5rem;
    opacity: 0.9;
    margin-top: 0.25rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.75rem;
    border-radius: var(--border-radius);
    backdrop-filter: blur(5px);
}

.feature h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin-bottom: 0.75rem;
    color: var(--white);
}

.feature p {
    opacity: 0.85;
    line-height: 1.5;
    font-size: var(--font-size-sm);
}

.login-footer-bottom {
    text-align: center;
    padding: 1rem;
    color: var(--white);
    opacity: 0.7;
    font-size: var(--font-size-sm);
}

/* Alert Styles */
.alert {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.alert-success {
    background: rgb(16 185 129 / 0.1);
    color: var(--success-color);
    border: 1px solid rgb(16 185 129 / 0.2);
}

.alert-error {
    background: rgb(239 68 68 / 0.1);
    color: var(--error-color);
    border: 1px solid rgb(239 68 68 / 0.2);
}

.alert-warning {
    background: rgb(245 158 11 / 0.1);
    color: var(--warning-color);
    border: 1px solid rgb(245 158 11 / 0.2);
}

.alert-info {
    background: rgb(6 182 212 / 0.1);
    color: var(--info-color);
    border: 1px solid rgb(6 182 212 / 0.2);
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
    .login-container {
        max-width: 1000px;
        gap: 3rem;
    }

    .login-info h2 {
        font-size: var(--font-size-3xl);
    }

    .feature {
        padding: 1.25rem;
    }
}

@media (max-width: 1024px) {
    .login-container {
        max-width: 900px;
        gap: 2.5rem;
        padding: 1.5rem;
    }

    .login-card {
        max-width: 450px;
    }

    .login-info {
        padding: 1.5rem;
    }

    .features {
        gap: 2rem;
    }

    .feature {
        padding: 1rem;
    }
}

@media (max-width: 768px) {
    .login-container {
        grid-template-columns: 1fr;
        gap: 2rem;
        padding: 1rem;
        max-width: 500px;
    }

    .login-info {
        order: -1;
        text-align: center;
        padding: 1.5rem 1rem;
        animation: slideInUp 0.6s ease-out;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .login-info h2 {
        font-size: var(--font-size-2xl);
        margin-bottom: 0.75rem;
    }

    .login-info > p {
        font-size: var(--font-size-base);
        margin-bottom: 2rem;
    }

    .features {
        display: grid;
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .feature {
        padding: 1rem;
        text-align: left;
    }

    .feature i {
        font-size: 2rem;
    }

    .login-card {
        margin: 0;
        animation: slideInUp 0.6s ease-out 0.2s both;
    }

    .login-header {
        padding: 2rem 1.5rem 1.25rem;
    }

    .login-form-container {
        padding: 2rem 1.5rem;
    }
}

@media (max-width: 480px) {
    .login-container {
        padding: 0.75rem;
        gap: 1.5rem;
    }

    .login-header {
        padding: 1.5rem 1rem 1rem;
    }

    .login-header .logo {
        width: 60px;
        height: 60px;
    }

    .login-header h1 {
        font-size: var(--font-size-2xl);
    }

    .login-form-container {
        padding: 1.5rem 1rem;
    }

    .form-group {
        gap: 0.5rem;
    }

    .input-group input {
        padding: 0.875rem 1rem 0.875rem 2.5rem;
        font-size: var(--font-size-sm);
    }

    .btn-login {
        padding: 1rem;
        font-size: var(--font-size-base);
    }

    .login-info {
        padding: 1rem;
    }

    .login-info h2 {
        font-size: var(--font-size-xl);
    }

    .login-info > p {
        font-size: var(--font-size-sm);
        margin-bottom: 1.5rem;
    }

    .features {
        gap: 1rem;
    }

    .feature {
        padding: 0.75rem;
        gap: 1rem;
    }

    .feature i {
        font-size: 1.75rem;
        padding: 0.5rem;
    }

    .feature h3 {
        font-size: var(--font-size-lg);
        margin-bottom: 0.5rem;
    }

    .feature p {
        font-size: var(--font-size-xs);
    }
}

/* Touch device optimizations */
@media (pointer: coarse) {
    .btn, .btn-login {
        min-height: 48px;
        padding: 1rem 1.5rem;
        font-size: 1rem;
    }

    .input-group input {
        min-height: 48px;
        padding: 1rem 1rem 1rem 2.75rem;
        font-size: 1rem;
    }

    .password-toggle {
        min-width: 48px;
        min-height: 48px;
        padding: 0.75rem;
    }

    .checkbox-container {
        min-height: 48px;
        padding: 1rem 0;
    }

    .custom-checkbox {
        width: 1.5rem;
        height: 1.5rem;
        margin-right: 1rem;
    }

    .custom-checkbox .checkmark {
        width: 1.5rem;
        height: 1.5rem;
    }

    .forgot-password-link {
        min-height: 48px;
        padding: 0.75rem 1rem;
        font-size: 1rem;
    }

    /* Larger tap targets for mobile */
    .login-card {
        padding: 2rem 1.5rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }
}

/* Accessibility improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .btn {
        border: 2px solid;
    }

    .input-group input {
        border: 2px solid var(--gray-400);
    }

    .custom-checkbox .checkmark {
        border: 2px solid var(--gray-600);
    }

    .alert {
        border: 2px solid;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .login-card {
        animation: none;
    }

    .slideInLeft,
    .slideInRight,
    .slideInUp {
        animation: none;
        opacity: 1;
        transform: none;
    }
}

/* Enhanced focus management */
.btn:focus-visible,
input:focus-visible,
.checkbox-container:focus-within,
.forgot-password-link:focus-visible {
    outline: 3px solid var(--primary-color);
    outline-offset: 2px;
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.2);
}

/* Skip link for screen readers */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
    font-weight: 600;
}

.skip-link:focus {
    top: 6px;
}

/* Enhanced loading states */
.btn.loading {
    position: relative;
    color: transparent;
    pointer-events: none;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced error and success states */
.field-error {
    color: var(--error-color);
    font-size: var(--font-size-sm);
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    animation: slideInUp 0.3s ease;
}

.field-error::before {
    content: '⚠';
    font-size: 0.875rem;
}

input.error {
    border-color: var(--error-color);
    box-shadow: 0 0 0 3px rgb(239 68 68 / 0.1);
}

input.success {
    border-color: var(--success-color);
    box-shadow: 0 0 0 3px rgb(34 197 94 / 0.1);
}

.field-success {
    color: var(--success-color);
    font-size: var(--font-size-sm);
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    animation: slideInUp 0.3s ease;
}

.field-success::before {
    content: '✓';
    font-size: 0.875rem;
}

/* Icon Styles (using CSS for basic icons) */
.icon-user::before { content: "👤"; }
.icon-lock::before { content: "🔒"; }
.icon-eye::before { content: "👁"; }
.icon-eye-off::before { content: "🙈"; }
.icon-login::before { content: "🚪"; }
.icon-trophy::before { content: "🏆"; }
.icon-chart::before { content: "📊"; }
.icon-users::before { content: "👥"; }
.icon-error::before { content: "❌"; }
.icon-success::before { content: "✅"; }
.icon-warning::before { content: "⚠️"; }
.icon-info::before { content: "ℹ️"; }
.icon-spinner::before { content: "⏳"; }
.icon-calendar::before { content: "📅"; }
.icon-check::before { content: "✓"; }

/* Admin Layout Styles */
.admin-page {
    display: grid;
    grid-template-areas:
        "sidebar header"
        "sidebar main";
    grid-template-columns: var(--sidebar-width) 1fr;
    grid-template-rows: var(--header-height) auto;
    min-height: 100vh;
    height: auto;
}

/* Header Styles */
.admin-header {
    grid-area: header;
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 2rem;
    box-shadow: var(--shadow-sm);
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--gray-600);
}

.header-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-menu {
    position: relative;
}

.user-menu-toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.user-menu-toggle:hover {
    background: var(--gray-100);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--primary-color);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: var(--font-size-sm);
}

.user-menu-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    min-width: 200px;
    z-index: 1000;
    display: none;
}

.user-menu-dropdown.show {
    display: block;
}

.user-menu-item {
    display: block;
    padding: 0.75rem 1rem;
    color: var(--gray-700);
    text-decoration: none;
    border-bottom: 1px solid var(--gray-100);
    transition: var(--transition);
}

.user-menu-item:hover {
    background: var(--gray-50);
}

.user-menu-item:last-child {
    border-bottom: none;
}

/* Sidebar Styles */
.admin-sidebar {
    grid-area: sidebar;
    background: var(--gray-900);
    color: var(--white);
    overflow-y: auto;
    z-index: 200;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-700);
}

.sidebar-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    color: var(--white);
}

.sidebar-logo img {
    width: 32px;
    height: 32px;
}

.logo-placeholder {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.sidebar-logo h1 {
    font-size: var(--font-size-lg);
    font-weight: 700;
}

.sidebar-nav {
    padding: 1rem 0;
}

.nav-section {
    margin-bottom: 2rem;
}

.nav-section-title {
    padding: 0 1.5rem 0.5rem;
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--gray-400);
}

.nav-item {
    display: block;
    padding: 0.75rem 1.5rem;
    color: var(--gray-300);
    text-decoration: none;
    transition: var(--transition);
    border-left: 3px solid transparent;
}

.nav-item:hover {
    background: var(--gray-800);
    color: var(--white);
    border-left-color: var(--primary-color);
}

.nav-item.active {
    background: var(--gray-800);
    color: var(--white);
    border-left-color: var(--primary-color);
}

.nav-item i {
    margin-right: 0.75rem;
    width: 1.25rem;
    text-align: center;
}

/* Main Content Styles */
.main-content {
    grid-area: main;
    padding: 2rem;
    background: var(--gray-50);
    min-height: calc(100vh - var(--header-height));
    height: auto;
    overflow: visible;
}

.page-header {
    margin-bottom: 2rem;
}

.page-header h1 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.page-header p {
    color: var(--gray-600);
    font-size: var(--font-size-lg);
}

/* Dashboard Styles */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius);
    background: var(--primary-color);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.stat-content h3 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.25rem;
}

.stat-content p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    margin-bottom: 0.5rem;
}

.stat-change {
    font-size: var(--font-size-xs);
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    background: var(--gray-100);
    color: var(--gray-600);
}

.stat-change.positive {
    background: rgb(16 185 129 / 0.1);
    color: var(--success-color);
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
}

.dashboard-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h2 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
}

.card-content {
    padding: 1.5rem;
}

.no-data {
    text-align: center;
    color: var(--gray-500);
    font-style: italic;
    padding: 2rem;
}

/* Button Styles */
.btn-sm {
    padding: 0.5rem 1rem;
    font-size: var(--font-size-sm);
}

.btn-xs {
    padding: 0.25rem 0.75rem;
    font-size: var(--font-size-xs);
}

.btn-outline {
    background: transparent;
    border: 1px solid var(--gray-300);
    color: var(--gray-700);
}

.btn-outline:hover {
    background: var(--gray-50);
    border-color: var(--gray-400);
}

/* Match List Styles */
.matches-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.match-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.match-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.match-info h4 {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 0.25rem;
}

.match-info p {
    color: var(--gray-600);
    margin-bottom: 0.25rem;
}

.match-info small {
    color: var(--gray-500);
    font-size: var(--font-size-xs);
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.status-scheduled {
    background: rgb(6 182 212 / 0.1);
    color: var(--info-color);
}

.status-ongoing {
    background: rgb(245 158 11 / 0.1);
    color: var(--warning-color);
}

.status-completed {
    background: rgb(16 185 129 / 0.1);
    color: var(--success-color);
}

.status-cancelled {
    background: rgb(239 68 68 / 0.1);
    color: var(--error-color);
}

/* Event Creation Wizard Styles */
.wizard-container {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.wizard-progress {
    background: var(--gray-50);
    padding: 2rem;
    border-bottom: 1px solid var(--gray-200);
}

.progress-bar {
    background: var(--gray-200);
    height: 8px;
    border-radius: 4px;
    margin-bottom: 2rem;
    overflow: hidden;
}

.progress-fill {
    background: var(--primary-color);
    height: 100%;
    transition: width 0.3s ease;
}

.wizard-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.wizard-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
    position: relative;
}

.wizard-step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 20px;
    left: 60%;
    right: -40%;
    height: 2px;
    background: var(--gray-300);
    z-index: 1;
}

.wizard-step.completed:not(:last-child)::after {
    background: var(--primary-color);
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--gray-300);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    position: relative;
    z-index: 2;
}

.wizard-step.completed .step-number {
    background: var(--primary-color);
}

.wizard-step.active .step-number {
    background: var(--primary-color);
    box-shadow: 0 0 0 4px rgb(37 99 235 / 0.2);
}

.step-title {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--gray-600);
    text-align: center;
}

.wizard-step.active .step-title {
    color: var(--primary-color);
}

.wizard-content {
    padding: 2rem;
}

.step-content h2 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.step-content > p {
    color: var(--gray-600);
    margin-bottom: 2rem;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.form-grid .full-width {
    grid-column: 1 / -1;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 600;
    color: var(--gray-700);
    font-size: var(--font-size-sm);
}

.form-control {
    padding: 0.75rem;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    transition: var(--transition);
    background: var(--white);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.form-help {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

.wizard-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.nav-spacer {
    flex: 1;
}

/* Sports Selection Styles */
.sports-selection {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.sport-category h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.sports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
}

.sport-card {
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
}

.sport-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow);
}

.sport-checkbox {
    display: block;
    cursor: pointer;
}

.sport-checkbox input[type="checkbox"] {
    display: none;
}

.sport-checkbox input[type="checkbox"]:checked + .sport-info {
    background: rgb(37 99 235 / 0.05);
    border-color: var(--primary-color);
}

.sport-info {
    padding: 1.5rem;
    border: 2px solid transparent;
    transition: var(--transition);
}

.sport-info h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.sport-info p {
    color: var(--gray-600);
    margin-bottom: 1rem;
}

.sport-meta {
    display: flex;
    gap: 1rem;
    font-size: var(--font-size-sm);
}

.sport-type,
.sport-participants {
    padding: 0.25rem 0.5rem;
    background: var(--gray-100);
    border-radius: var(--border-radius);
    color: var(--gray-700);
}

.sport-config {
    padding: 1rem 1.5rem;
    background: var(--gray-50);
    border-top: 1px solid var(--gray-200);
}

/* Department Selection Styles */
.departments-selection {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.select-all-container {
    padding: 1rem;
    background: var(--gray-50);
    border-radius: var(--border-radius);
}

.departments-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1rem;
}

.department-card {
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
}

.department-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow);
}

.department-checkbox {
    display: block;
    cursor: pointer;
}

.department-checkbox input[type="checkbox"] {
    display: none;
}

.department-checkbox input[type="checkbox"]:checked + .department-info {
    background: rgb(37 99 235 / 0.05);
    border-color: var(--primary-color);
}

.department-info {
    padding: 1.5rem;
    border: 2px solid transparent;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.dept-color {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    flex-shrink: 0;
}

.dept-details h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 0.25rem;
}

.dept-details p {
    color: var(--gray-600);
    margin-bottom: 0.25rem;
}

.dept-details small {
    color: var(--gray-500);
    font-size: var(--font-size-xs);
}

/* Point System Configuration */
.point-system-config {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.preset-systems h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.preset-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.point-inputs {
    background: var(--gray-50);
    padding: 2rem;
    border-radius: var(--border-radius);
}

/* Confirmation Summary */
.confirmation-summary {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.summary-section {
    padding: 1.5rem;
    background: var(--gray-50);
    border-radius: var(--border-radius);
}

.summary-section h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.summary-item {
    margin-bottom: 0.75rem;
}

.summary-item strong {
    color: var(--gray-900);
    margin-right: 0.5rem;
}

.summary-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.summary-tag {
    padding: 0.25rem 0.75rem;
    background: var(--primary-color);
    color: var(--white);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.dept-tag {
    background: transparent;
    color: var(--gray-700);
    border: 2px solid;
}

.point-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.point-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--white);
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-200);
}

.position {
    font-weight: 600;
    color: var(--gray-700);
}

.points {
    font-weight: 700;
    color: var(--primary-color);
}

/* Filters Container */
.filters-container {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.filters-form {
    display: flex;
    gap: 1rem;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 200px;
}

.filter-group label {
    font-weight: 600;
    color: var(--gray-700);
    font-size: var(--font-size-sm);
}

.filter-actions {
    display: flex;
    gap: 0.5rem;
}

/* Bulk Actions */
.bulk-actions {
    background: var(--primary-color);
    color: var(--white);
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
}

.bulk-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.selected-count {
    font-weight: 600;
}

.bulk-controls .form-control {
    background: var(--white);
    border: none;
    min-width: 150px;
}

/* Table Styles */
.table-container {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid var(--gray-200);
}

.data-table th {
    background: var(--gray-50);
    font-weight: 600;
    color: var(--gray-900);
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.data-table tr:hover {
    background: var(--gray-50);
}

.data-table .text-center {
    text-align: center;
}

/* Department Specific Styles */
.department-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.dept-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.dept-names h4 {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 0.25rem;
}

.dept-names p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    margin: 0;
}

.contact-info div {
    margin-bottom: 0.25rem;
}

.contact-info a {
    color: var(--primary-color);
    text-decoration: none;
}

.contact-info a:hover {
    text-decoration: underline;
}

.dept-stats {
    display: flex;
    gap: 1rem;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.stat-value {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--gray-900);
}

.stat-label {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.btn-xs {
    padding: 0.375rem 0.75rem;
    font-size: var(--font-size-xs);
}

/* Dropdown Styles */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    background: none;
    border: 1px solid var(--gray-300);
    padding: 0.375rem 0.75rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    min-width: 150px;
    z-index: 1000;
    display: none;
}

.dropdown.show .dropdown-menu {
    display: block;
}

.dropdown-item {
    display: block;
    padding: 0.5rem 1rem;
    color: var(--gray-700);
    text-decoration: none;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    transition: var(--transition);
}

.dropdown-item:hover {
    background: var(--gray-50);
}

/* Sports Grid Styles */
.sports-container {
    margin-bottom: 2rem;
}

.sports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.sport-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
}

.sport-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.sport-header {
    padding: 1rem;
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sport-category {
    display: flex;
    align-items: center;
}

.category-badge {
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.category-team {
    background: rgb(16 185 129 / 0.1);
    color: var(--success-color);
}

.category-individual {
    background: rgb(6 182 212 / 0.1);
    color: var(--info-color);
}

.category-performing_arts {
    background: rgb(168 85 247 / 0.1);
    color: #8b5cf6;
}

.category-academic {
    background: rgb(245 158 11 / 0.1);
    color: var(--warning-color);
}

.sport-status {
    display: flex;
    align-items: center;
}

.sport-content {
    padding: 1.5rem;
}

.sport-content h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.sport-description {
    color: var(--gray-600);
    margin-bottom: 1rem;
    line-height: 1.5;
}

.sport-details {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.detail-label {
    font-weight: 600;
    color: var(--gray-700);
    font-size: var(--font-size-sm);
}

.detail-value {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
}

.sport-stats {
    display: flex;
    justify-content: space-around;
    padding: 1rem;
    background: var(--gray-50);
    border-top: 1px solid var(--gray-200);
    margin-bottom: 1rem;
}

.sport-actions {
    padding: 1rem;
    background: var(--gray-50);
    border-top: 1px solid var(--gray-200);
    display: flex;
    justify-content: center;
    gap: 0.5rem;
}

.bulk-selection-controls {
    text-align: center;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: var(--border-radius);
    margin-top: 1rem;
}

/* Venues Styles */
.venues-container {
    margin-bottom: 2rem;
}

.venues-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.venue-card {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
}

.venue-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.venue-header {
    padding: 1rem;
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.venue-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.live-indicator {
    font-size: var(--font-size-xs);
    font-weight: 600;
    color: var(--error-color);
}

.venue-actions-quick .status-select {
    padding: 0.25rem 0.5rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: var(--font-size-xs);
    background: var(--white);
}

.venue-content {
    padding: 1.5rem;
}

.venue-content h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.venue-location,
.venue-capacity {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    color: var(--gray-600);
    font-size: var(--font-size-sm);
}

.venue-location i,
.venue-capacity i {
    color: var(--gray-400);
}

.venue-facilities {
    margin-bottom: 1rem;
}

.venue-facilities strong {
    color: var(--gray-900);
    font-size: var(--font-size-sm);
}

.venue-facilities p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    margin: 0.25rem 0 0 0;
}

.venue-stats {
    margin-bottom: 1rem;
}

.stat-row {
    display: flex;
    justify-content: space-around;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: var(--border-radius);
}

.venue-contact {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.venue-contact strong {
    color: var(--gray-900);
}

.venue-actions {
    padding: 1rem;
    background: var(--gray-50);
    border-top: 1px solid var(--gray-200);
    display: flex;
    justify-content: center;
    gap: 0.5rem;
}

.venues-summary {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    padding: 2rem;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.summary-card {
    text-align: center;
    padding: 1.5rem;
    background: var(--gray-50);
    border-radius: var(--border-radius);
}

.summary-card h4 {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--gray-700);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
}

.summary-count {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--primary-color);
}

/* Form Styles */
.form-container {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    overflow: hidden;
}

.admin-form {
    padding: 2rem;
}

.form-section {
    margin-bottom: 3rem;
}

.form-section h2 {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--gray-200);
}

.color-input-group {
    display: flex;
    gap: 0.5rem;
}

.color-picker {
    width: 60px;
    height: 45px;
    padding: 0;
    border: 2px solid var(--gray-200);
    cursor: pointer;
}

.color-text {
    flex: 1;
    font-family: monospace;
    text-transform: uppercase;
}

.department-preview {
    background: var(--gray-50);
    padding: 2rem;
    border-radius: var(--border-radius);
}

.preview-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    max-width: 400px;
}

.preview-card .dept-header {
    padding: 1.5rem;
}

.preview-card .dept-contact {
    padding: 0 1.5rem 1.5rem;
    color: var(--gray-600);
    font-size: var(--font-size-sm);
}

.preview-card .dept-contact div {
    margin-bottom: 0.25rem;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 2rem;
    background: var(--gray-50);
    border-top: 1px solid var(--gray-200);
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: none;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--gray-400);
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: var(--gray-600);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--gray-200);
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

.text-warning {
    color: var(--warning-color);
    font-size: var(--font-size-sm);
    margin-top: 0.5rem;
}

/* Checkbox Styles */
.checkbox-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: var(--font-size-sm);
}

.checkbox-container input[type="checkbox"] {
    width: 1rem;
    height: 1rem;
    accent-color: var(--primary-color);
}

.checkmark {
    width: 1rem;
    height: 1rem;
    border: 2px solid var(--gray-300);
    border-radius: 3px;
    position: relative;
    transition: var(--transition);
}

.checkbox-container input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-container input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 1px;
    color: var(--white);
    font-size: 0.75rem;
    font-weight: bold;
}

/* Responsive Design for Admin */
@media (max-width: 1024px) {
    .admin-page {
        grid-template-columns: 1fr;
        grid-template-areas:
            "header"
            "main";
    }

    .admin-sidebar {
        position: fixed;
        left: -280px;
        top: 0;
        height: 100vh;
        z-index: 300;
        transition: left 0.3s ease;
    }

    .admin-sidebar.mobile-open {
        left: 0;
    }

    .menu-toggle {
        display: block;
    }

    .main-content {
        padding: 1rem;
    }

    .page-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .filters-form {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        min-width: auto;
    }

    .sports-grid,
    .venues-grid {
        grid-template-columns: 1fr;
    }

    .departments-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .wizard-steps {
        flex-direction: column;
        gap: 1rem;
    }

    .wizard-step:not(:last-child)::after {
        display: none;
    }
}

@media (max-width: 768px) {
    .data-table {
        font-size: var(--font-size-sm);
    }

    .data-table th,
    .data-table td {
        padding: 0.5rem;
    }

    .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }

    .bulk-controls {
        flex-direction: column;
        gap: 0.5rem;
        align-items: stretch;
    }

    .summary-cards {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Pagination Styles */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: var(--gray-50);
    border-top: 1px solid var(--gray-200);
}

.pagination-info {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
}

.pagination-nav {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.pagination-btn {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--gray-300);
    background: var(--white);
    color: var(--gray-700);
    text-decoration: none;
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    transition: var(--transition);
}

.pagination-btn:hover {
    background: var(--gray-50);
    border-color: var(--gray-400);
}

.pagination-btn.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.pagination-ellipsis {
    padding: 0.5rem;
    color: var(--gray-400);
    font-size: var(--font-size-sm);
}

@media (max-width: 768px) {
    .pagination-container {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .pagination-nav {
        flex-wrap: wrap;
        justify-content: center;
    }
}

/* ===== SETTINGS INTERFACE ===== */
.settings-container {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 2rem;
    margin-top: 1rem;
}

.settings-nav {
    background: var(--white);
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-200);
    padding: 1rem 0;
    height: fit-content;
    position: sticky;
    top: 2rem;
}

.settings-tab {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    width: 100%;
    padding: 0.875rem 1.5rem;
    border: none;
    background: none;
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-align: left;
    cursor: pointer;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.settings-tab:hover {
    background: var(--gray-50);
    color: var(--gray-800);
}

.settings-tab.active {
    background: var(--primary-50);
    color: var(--primary-600);
    border-left-color: var(--primary-500);
}

.settings-tab i {
    font-size: 1.125rem;
    width: 20px;
    text-align: center;
}

.settings-content {
    background: var(--white);
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-200);
    overflow: visible;
    min-height: fit-content;
}

.settings-panel {
    display: none;
    padding: 2rem;
    min-height: fit-content;
    height: auto;
}

.settings-panel.active {
    display: block;
}

.panel-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--gray-200);
}

.panel-header h2 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.panel-header p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    margin: 0;
}

.settings-form {
    max-width: 800px;
}

.form-grid {
    display: grid;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 500;
    color: var(--gray-700);
    font-size: var(--font-size-sm);
}

.form-group input,
.form-group textarea,
.form-group select {
    padding: 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px var(--primary-100);
}

.form-group input.error,
.form-group textarea.error,
.form-group select.error {
    border-color: var(--danger-500);
    box-shadow: 0 0 0 3px var(--danger-100);
}

.form-group small {
    color: var(--gray-500);
    font-size: var(--font-size-xs);
    margin-top: 0.25rem;
}

.field-error {
    color: var(--danger-600);
    font-size: var(--font-size-xs);
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.field-error::before {
    content: "⚠";
    font-size: 0.875rem;
}

.checkbox-label {
    display: flex !important;
    flex-direction: row !important;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    font-weight: 500;
    color: var(--gray-700);
}

.checkbox-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
    margin: 0;
}

.checkmark {
    position: relative;
    display: inline-block;
    width: 18px;
    height: 18px;
    border: 2px solid var(--gray-300);
    border-radius: 3px;
    transition: all 0.2s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-500);
    border-color: var(--primary-500);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: "✓";
    position: absolute;
    top: -2px;
    left: 2px;
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.form-actions {
    display: flex;
    gap: 1rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--gray-200);
}

.password-toggle {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 3px;
    transition: color 0.2s ease;
}

.password-toggle:hover {
    color: var(--gray-700);
}

/* System Info Styles */
.system-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    width: 100%;
    min-height: fit-content;
}

.info-card {
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    height: fit-content;
    min-height: auto;
}

.info-card h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--gray-300);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--gray-200);
}

.info-item:last-child {
    border-bottom: none;
}

.info-item label {
    font-weight: 500;
    color: var(--gray-600);
    font-size: var(--font-size-sm);
}

.info-item span {
    color: var(--gray-900);
    font-size: var(--font-size-sm);
    font-family: var(--font-mono);
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    width: 100%;
    min-height: fit-content;
}

.action-buttons .btn {
    justify-content: flex-start;
    gap: 0.5rem;
    width: 100%;
    text-align: left;
    display: flex;
    align-items: center;
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 2rem;
    right: 2rem;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    z-index: 1000;
    min-width: 300px;
    animation: slideInRight 0.3s ease;
}

.notification-success {
    border-left: 4px solid var(--success-500);
}

.notification-error {
    border-left: 4px solid var(--danger-500);
}

.notification-info {
    border-left: 4px solid var(--primary-500);
}

.notification-close {
    background: none;
    border: none;
    font-size: 1.25rem;
    color: var(--gray-500);
    cursor: pointer;
    margin-left: auto;
    padding: 0;
    line-height: 1;
}

.notification-close:hover {
    color: var(--gray-700);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Settings Responsive Design */
@media (max-width: 1024px) {
    .settings-container {
        grid-template-columns: 200px 1fr;
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .settings-container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .settings-nav {
        position: static;
        display: flex;
        overflow-x: auto;
        padding: 0.5rem;
        gap: 0.5rem;
    }

    .settings-tab {
        white-space: nowrap;
        min-width: auto;
        padding: 0.75rem 1rem;
        border-left: none;
        border-bottom: 3px solid transparent;
        border-radius: var(--border-radius);
    }

    .settings-tab.active {
        border-left: none;
        border-bottom-color: var(--primary-500);
    }

    .settings-panel {
        padding: 1.5rem;
    }

    .form-grid {
        gap: 1rem;
    }

    .form-actions {
        flex-direction: column;
    }

    .system-info-grid {
        grid-template-columns: 1fr;
    }
}

/* Ensure system info content is fully visible */
#system-panel {
    min-height: fit-content !important;
    height: auto !important;
    overflow: visible !important;
    padding-bottom: 3rem !important;
}

#system-panel .system-info-grid {
    min-height: fit-content !important;
    height: auto !important;
    margin-bottom: 2rem !important;
}

#system-panel .info-card {
    min-height: fit-content !important;
    height: auto !important;
    overflow: visible !important;
}

/* System Information Page Specific Fixes */
.settings-container {
    display: block !important;
    width: 100% !important;
    min-height: fit-content !important;
    height: auto !important;
    margin-bottom: 3rem !important;
}

/* Ensure settings content expands properly */
.settings-content {
    width: 100% !important;
    min-height: fit-content !important;
    height: auto !important;
    overflow: visible !important;
    margin-bottom: 2rem !important;
}

/* Add bottom padding to main content for system pages */
body.admin-page .main-content {
    padding-bottom: 4rem !important;
}

/* Ensure system info grid has proper spacing */
.system-info-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
    gap: 1.5rem !important;
    width: 100% !important;
    min-height: fit-content !important;
    margin-bottom: 3rem !important;
}

/* Force visibility of all info cards */
.info-card {
    background: var(--gray-50) !important;
    border: 1px solid var(--gray-200) !important;
    border-radius: var(--border-radius) !important;
    padding: 1.5rem !important;
    height: auto !important;
    min-height: fit-content !important;
    overflow: visible !important;
    margin-bottom: 1rem !important;
}

/* Ensure action buttons are fully visible */
.action-buttons {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.75rem !important;
    width: 100% !important;
    min-height: fit-content !important;
    margin-top: 1rem !important;
}

.action-buttons .btn {
    justify-content: flex-start !important;
    gap: 0.5rem !important;
    width: 100% !important;
    text-align: left !important;
    display: flex !important;
    align-items: center !important;
    padding: 0.75rem 1rem !important;
    margin-bottom: 0.5rem !important;
}

/* Ensure Quick Actions card is fully visible */
.info-card:last-child {
    margin-bottom: 2rem !important;
}

/* Force body and html to allow content expansion */
html, body {
    height: auto !important;
    min-height: 100vh !important;
    overflow-x: hidden !important;
    overflow-y: auto !important;
}

/* System Information Page - Override all height constraints */
body.admin-page {
    height: auto !important;
    min-height: 100vh !important;
    overflow: visible !important;
}

/* Specific fix for system.php page */
body.admin-page .admin-page {
    height: auto !important;
    min-height: 100vh !important;
    grid-template-rows: var(--header-height) auto !important;
}

/* Ensure main content can expand beyond viewport */
body.admin-page .main-content {
    height: auto !important;
    min-height: calc(100vh - var(--header-height)) !important;
    max-height: none !important;
    overflow: visible !important;
    padding-bottom: 5rem !important;
}

/* CRITICAL FIX: System Information Page Layout Override */
/* This ensures the system.php page displays all content properly */

/* Alternative approach - target pages with settings-container */
.settings-container {
    display: block !important;
    width: 100% !important;
    height: auto !important;
    min-height: fit-content !important;
    margin-bottom: 4rem !important;
    overflow: visible !important;
}

/* Force system info content to be visible */
.settings-container .settings-content {
    width: 100% !important;
    height: auto !important;
    min-height: fit-content !important;
    overflow: visible !important;
    margin-bottom: 3rem !important;
}

.settings-container .settings-panel {
    height: auto !important;
    min-height: fit-content !important;
    overflow: visible !important;
    padding-bottom: 3rem !important;
}

.settings-container .system-info-grid {
    height: auto !important;
    min-height: fit-content !important;
    margin-bottom: 4rem !important;
    overflow: visible !important;
}

.settings-container .info-card {
    height: auto !important;
    min-height: fit-content !important;
    overflow: visible !important;
    margin-bottom: 1.5rem !important;
}

.settings-container .action-buttons {
    margin-bottom: 2rem !important;
}

/* ===== PROFILE & PASSWORD PAGES ===== */

/* Profile Tab Navigation */
.profile-tabs-container {
    margin: 1.5rem 0;
    border-bottom: 1px solid var(--gray-200);
}

.profile-tabs {
    display: flex;
    gap: 0;
    max-width: 1200px;
    margin: 0 auto;
}

.profile-tab {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    text-decoration: none;
    color: var(--gray-600);
    font-weight: 500;
    font-size: var(--font-size-sm);
    border-bottom: 3px solid transparent;
    transition: var(--transition);
    position: relative;
}

.profile-tab:hover {
    color: var(--primary-color);
    background: var(--gray-50);
}

.profile-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: var(--white);
}

.profile-tab i {
    font-size: 1rem;
}

.profile-container {
    max-width: 1200px;
    margin: 0 auto;
}

.profile-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-top: 1rem;
}

.profile-card {
    background: var(--white);
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.card-header {
    background: var(--gray-50);
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
}

.card-header h2 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-header p {
    margin: 0.5rem 0 0 0;
    color: var(--gray-600);
    font-size: var(--font-size-sm);
}

.profile-form,
.password-form {
    padding: 1.5rem;
}

.change-password-container {
    max-width: 600px;
    margin: 2rem auto;
    display: grid;
    gap: 2rem;
}

.password-card {
    background: var(--white);
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.password-input-wrapper {
    position: relative;
}

.password-input-wrapper input {
    padding-right: 3rem;
}

.password-toggle {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 3px;
    transition: color 0.2s ease;
}

.password-toggle:hover {
    color: var(--gray-700);
}

.password-requirements {
    margin-top: 1rem;
    padding: 1rem;
    background: var(--gray-50);
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-200);
}

.password-requirements h4 {
    margin: 0 0 0.75rem 0;
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--gray-700);
}

.password-requirements ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.requirement {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0;
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    transition: color 0.2s ease;
}

.requirement.met {
    color: var(--success-600);
}

.requirement i {
    width: 16px;
    height: 16px;
    font-size: 12px;
}

.password-match-indicator {
    margin-top: 0.5rem;
    font-size: var(--font-size-sm);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.password-match-indicator.match {
    color: var(--success-600);
}

.password-match-indicator.no-match {
    color: var(--danger-600);
}

.security-tips {
    background: var(--blue-50);
    border: 1px solid var(--blue-200);
    border-radius: var(--border-radius);
    padding: 1.5rem;
}

.security-tips h3 {
    margin: 0 0 1rem 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--blue-800);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.security-tips ul {
    margin: 0;
    padding-left: 1.5rem;
}

.security-tips li {
    margin-bottom: 0.5rem;
    color: var(--blue-700);
    font-size: var(--font-size-sm);
}

.status-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: var(--font-size-xs);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-active {
    background: var(--success-100);
    color: var(--success-800);
}

.status-inactive {
    background: var(--gray-100);
    color: var(--gray-800);
}

.status-suspended {
    background: var(--danger-100);
    color: var(--danger-800);
}

/* Profile Responsive Design */
@media (max-width: 768px) {
    .profile-tabs {
        flex-direction: column;
        margin: 0 1rem;
    }

    .profile-tab {
        padding: 0.875rem 1rem;
        border-bottom: none;
        border-left: 3px solid transparent;
        border-radius: var(--border-radius);
        margin-bottom: 0.25rem;
    }

    .profile-tab.active {
        border-bottom: none;
        border-left-color: var(--primary-color);
        background: var(--primary-50);
    }

    .profile-tabs-container {
        margin: 1rem 0;
        border-bottom: none;
    }

    .profile-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .profile-card:nth-child(3) {
        grid-column: 1;
    }

    .change-password-container {
        margin: 1rem;
    }

    .card-header,
    .profile-form,
    .password-form {
        padding: 1rem;
    }
}

/* ===== ENHANCED EVENTS MANAGEMENT STYLES ===== */

/* Improved Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    flex-wrap: wrap;
}

.action-buttons .btn {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 6px;
    text-decoration: none;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    white-space: nowrap;
    min-width: auto;
}

.action-buttons .btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;
}

.action-buttons .btn-xs {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

.action-buttons .btn span {
    display: inline;
}

/* Button Variants */
.btn-outline {
    background: var(--white);
    color: var(--gray-700);
    border-color: var(--gray-300);
}

.btn-outline:hover {
    background: var(--gray-50);
    border-color: var(--gray-400);
    color: var(--gray-900);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-primary {
    background: var(--primary-600);
    color: var(--white);
    border-color: var(--primary-600);
}

.btn-primary:hover {
    background: var(--primary-700);
    border-color: var(--primary-700);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.btn-secondary {
    background: var(--gray-100);
    color: var(--gray-700);
    border-color: var(--gray-300);
}

.btn-secondary:hover {
    background: var(--gray-200);
    border-color: var(--gray-400);
    color: var(--gray-900);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-danger {
    background: var(--red-600);
    color: var(--white);
    border-color: var(--red-600);
}

.btn-danger:hover {
    background: var(--red-700);
    border-color: var(--red-700);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

/* Enhanced Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal.show {
    display: flex;
    opacity: 1;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.modal-content {
    background: var(--white);
    border-radius: 12px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.95);
    transition: transform 0.3s ease;
}

.modal.show .modal-content {
    transform: scale(1);
}

.modal-lg {
    max-width: 800px;
}

.modal-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--gray-50);
}

.modal-header h3 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--gray-400);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s ease;
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    background: var(--gray-200);
    color: var(--gray-600);
}

.modal-body {
    padding: 2rem;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 1.5rem 2rem;
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

/* Enhanced Dropdown Styles */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    background: var(--gray-100);
    border: 1px solid var(--gray-300);
    color: var(--gray-700);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.dropdown-toggle:hover {
    background: var(--gray-200);
    border-color: var(--gray-400);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: 8px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    min-width: 160px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
    margin-top: 0.25rem;
}

.dropdown.show .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    width: 100%;
    padding: 0.75rem 1rem;
    background: none;
    border: none;
    text-align: left;
    color: var(--gray-700);
    font-size: 0.875rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.dropdown-item:hover {
    background: var(--gray-50);
    color: var(--gray-900);
}

.dropdown-item:first-child {
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.dropdown-item:last-child {
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
}

/* Status Badge Enhancements */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-upcoming {
    background: var(--blue-100);
    color: var(--blue-800);
}

.status-ongoing {
    background: var(--green-100);
    color: var(--green-800);
}

.status-completed {
    background: var(--gray-100);
    color: var(--gray-800);
}

.status-scheduled {
    background: var(--yellow-100);
    color: var(--yellow-800);
}

.status-cancelled {
    background: var(--red-100);
    color: var(--red-800);
}

/* Loading Animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.icon-spinner {
    animation: spin 1s linear infinite;
}

.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 2rem;
    color: var(--gray-600);
    font-size: 0.875rem;
}

/* Responsive Improvements */
@media (max-width: 768px) {
    .action-buttons {
        flex-direction: column;
        align-items: stretch;
        gap: 0.375rem;
    }

    .action-buttons .btn {
        justify-content: center;
        width: 100%;
    }

    .modal-overlay {
        padding: 0.5rem;
    }

    .modal-content {
        max-height: 95vh;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .dropdown-menu {
        right: auto;
        left: 0;
        min-width: 200px;
    }
}
