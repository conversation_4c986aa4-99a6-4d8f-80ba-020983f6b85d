<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Admin Login Page
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// Set security headers
setSecurityHeaders();

// Clean up expired tokens periodically (1% chance)
if (rand(1, 100) === 1) {
    cleanupExpiredRememberTokens();
}

// Validate session security
validateSessionSecurity();

// Check for auto-login with remember token
if (!isLoggedIn() && isset($_COOKIE['remember_token'])) {
    if (autoLoginWithRememberToken()) {
        redirect('dashboard.php', 'Welcome back!', 'success');
    }
}

// Redirect if already logged in
if (isLoggedIn()) {
    redirect('dashboard.php');
}

$error = '';
$success = '';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Additional security checks
        if (!isset($_POST['csrf_token'])) {
            logSecurityEvent('missing_csrf_token', 'Login attempt without CSRF token', 'high');
            throw new Exception('Security token missing');
        }

        // Verify CSRF token
        if (!verifyCSRFToken($_POST['csrf_token'])) {
            logSecurityEvent('invalid_csrf_token', 'Login attempt with invalid CSRF token', 'high');
            throw new Exception('Invalid security token');
        }

        // Check for suspicious patterns
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        if (empty($userAgent) || strlen($userAgent) < 10) {
            logSecurityEvent('suspicious_user_agent', 'Login attempt with suspicious user agent', 'medium');
        }

        // Validate and sanitize input
        $username = sanitizeInput($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        $rememberMe = isset($_POST['remember_me']) && $_POST['remember_me'] === 'on';

        // Enhanced input validation
        if (empty($username) || empty($password)) {
            throw new Exception('Please enter both username and password');
        }

        if (strlen($username) < 3 || strlen($username) > 50) {
            throw new Exception('Username must be between 3 and 50 characters');
        }

        if (strlen($password) < 6 || strlen($password) > 255) {
            throw new Exception('Password must be between 6 and 255 characters');
        }

        // Check for SQL injection patterns
        $suspiciousPatterns = ['union', 'select', 'insert', 'update', 'delete', 'drop', '--', ';'];
        foreach ($suspiciousPatterns as $pattern) {
            if (stripos($username, $pattern) !== false) {
                logSecurityEvent('sql_injection_attempt', "Potential SQL injection in username: {$username}", 'critical');
                throw new Exception('Invalid characters in username');
            }
        }

        // Attempt authentication
        if (authenticateUser($username, $password, $rememberMe)) {
            redirect('dashboard.php', 'Welcome back!', 'success');
        }

    } catch (Exception $e) {
        $error = $e->getMessage();

        // Log failed attempts with additional context
        $context = [
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'referer' => $_SERVER['HTTP_REFERER'] ?? 'none',
            'username_attempted' => $username ?? 'none'
        ];
        logSecurityEvent('login_form_error', $e->getMessage() . ' | Context: ' . json_encode($context), 'medium');
    }
}

// Generate CSRF token
$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body class="login-page">
    <!-- Skip link for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <div class="login-container" id="main-content" role="main">
        <div class="login-card">
            <div class="login-header">
                <img src="../assets/images/logo.png" alt="SCIMS Logo" class="logo" onerror="this.style.display='none'">
                <h1>SCIMS Admin</h1>
                <p>Samar College Intramurals Management System</p>
            </div>
            
            <div class="login-form-container">
                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <i class="icon-error"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="icon-success"></i>
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" action="" class="login-form" id="loginForm" role="form" aria-label="Admin Login Form">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">

                    <div class="form-group">
                        <label for="username">Username</label>
                        <div class="input-group">
                            <i class="icon-user" aria-hidden="true"></i>
                            <input
                                type="text"
                                id="username"
                                name="username"
                                value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                                required
                                autocomplete="username"
                                placeholder="Enter your username"
                                aria-describedby="username-help"
                                aria-invalid="false"
                            >
                        </div>
                        <div id="username-help" class="sr-only">Enter your admin username to log in</div>
                    </div>

                    <div class="form-group">
                        <label for="password">Password</label>
                        <div class="input-group">
                            <i class="icon-lock" aria-hidden="true"></i>
                            <input
                                type="password"
                                id="password"
                                name="password"
                                required
                                autocomplete="current-password"
                                placeholder="Enter your password"
                                aria-describedby="password-help"
                                aria-invalid="false"
                            >
                            <button
                                type="button"
                                class="password-toggle"
                                onclick="togglePassword()"
                                aria-label="Toggle password visibility"
                                aria-pressed="false"
                            >
                                <i class="icon-eye" id="passwordToggleIcon" aria-hidden="true"></i>
                            </button>
                        </div>
                        <div id="password-help" class="sr-only">Enter your admin password</div>
                    </div>

                    <div class="form-group">
                        <label class="checkbox-container" for="remember_me">
                            <div class="custom-checkbox">
                                <input
                                    type="checkbox"
                                    name="remember_me"
                                    id="remember_me"
                                    aria-describedby="remember-help"
                                >
                                <span class="checkmark" aria-hidden="true"></span>
                            </div>
                            Remember me for 30 days
                        </label>
                        <div id="remember-help" class="sr-only">Keep me logged in for 30 days on this device</div>
                    </div>

                    <button type="submit" class="btn btn-primary btn-login" aria-describedby="login-help">
                        <i class="icon-login" aria-hidden="true"></i>
                        Sign In
                    </button>
                    <div id="login-help" class="sr-only">Submit the form to log in to the admin dashboard</div>
                </form>
                
                <div class="login-footer">
                    <a href="forgot-password.php" class="forgot-password-link">
                        Forgot your password?
                    </a>
                </div>
            </div>
        </div>
        
        <div class="login-info">
            <h2>Welcome to SCIMS</h2>
            <p>The comprehensive sports event management system for Samar College intramural competitions.</p>
            
            <div class="features">
                <div class="feature">
                    <i class="icon-trophy"></i>
                    <h3>Event Management</h3>
                    <p>Organize and manage multiple sports events efficiently</p>
                </div>
                
                <div class="feature">
                    <i class="icon-chart"></i>
                    <h3>Real-time Scoring</h3>
                    <p>Live score updates and automatic standings calculation</p>
                </div>
                
                <div class="feature">
                    <i class="icon-users"></i>
                    <h3>Department Tracking</h3>
                    <p>Monitor department performance and rankings</p>
                </div>
            </div>
        </div>
    </div>
    
    <footer class="login-footer-bottom">
        <p>&copy; <?php echo date('Y'); ?> Samar College. All rights reserved. | Version <?php echo APP_VERSION; ?></p>
    </footer>
    
    <script src="../assets/js/admin.js"></script>
    <script>
        // Enhanced login page functionality
        document.addEventListener('DOMContentLoaded', function() {
            initializeLoginPage();
        });

        function initializeLoginPage() {
            // Auto-focus username field
            const usernameField = document.getElementById('username');
            if (usernameField) {
                usernameField.focus();
            }

            // Enhanced form validation
            const loginForm = document.getElementById('loginForm');
            if (loginForm) {
                loginForm.addEventListener('submit', handleFormSubmit);

                // Real-time validation
                const inputs = loginForm.querySelectorAll('input[required]');
                inputs.forEach(input => {
                    input.addEventListener('blur', validateField);
                    input.addEventListener('input', clearFieldError);
                });
            }

            // Auto-dismiss alerts
            setTimeout(() => {
                const alerts = document.querySelectorAll('.alert');
                alerts.forEach(alert => {
                    if (!alert.classList.contains('alert-error')) {
                        alert.style.opacity = '0';
                        setTimeout(() => alert.remove(), 300);
                    }
                });
            }, 8000);

            // Keyboard shortcuts
            document.addEventListener('keydown', handleKeyboardShortcuts);
        }

        function handleFormSubmit(e) {
            const form = e.target;
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;

            // Clear previous errors
            clearAllFieldErrors();

            let isValid = true;

            // Validate username
            if (!username) {
                showFieldError(document.getElementById('username'), 'Username is required');
                isValid = false;
            } else if (username.length < 3) {
                showFieldError(document.getElementById('username'), 'Username must be at least 3 characters');
                isValid = false;
            }

            // Validate password
            if (!password) {
                showFieldError(document.getElementById('password'), 'Password is required');
                isValid = false;
            } else if (password.length < 6) {
                showFieldError(document.getElementById('password'), 'Password must be at least 6 characters');
                isValid = false;
            }

            if (!isValid) {
                e.preventDefault();
                return false;
            }

            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="icon-spinner"></i> Signing In...';
                submitBtn.disabled = true;

                // Re-enable after 10 seconds as fallback
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 10000);
            }

            return true;
        }

        function validateField(e) {
            const input = e.target;
            const value = input.value.trim();

            clearFieldError(e);

            if (input.hasAttribute('required') && !value) {
                const fieldName = input.name.charAt(0).toUpperCase() + input.name.slice(1);
                showFieldError(input, `${fieldName} is required`);
                return false;
            }

            if (input.name === 'username' && value && value.length < 3) {
                showFieldError(input, 'Username must be at least 3 characters');
                return false;
            }

            if (input.name === 'password' && value && value.length < 6) {
                showFieldError(input, 'Password must be at least 6 characters');
                return false;
            }

            return true;
        }

        function showFieldError(input, message) {
            const inputGroup = input.closest('.input-group') || input.closest('.form-group');

            // Remove existing error
            const existingError = inputGroup.querySelector('.field-error');
            if (existingError) {
                existingError.remove();
            }

            // Add error class and ARIA attributes
            input.classList.add('error');
            input.setAttribute('aria-invalid', 'true');

            // Create error message with unique ID
            const errorId = input.id + '-error';
            const errorDiv = document.createElement('div');
            errorDiv.className = 'field-error';
            errorDiv.id = errorId;
            errorDiv.textContent = message;
            errorDiv.setAttribute('role', 'alert');
            errorDiv.setAttribute('aria-live', 'polite');

            // Link input to error message
            input.setAttribute('aria-describedby', (input.getAttribute('aria-describedby') || '') + ' ' + errorId);

            inputGroup.appendChild(errorDiv);
        }

        function clearFieldError(e) {
            const input = e.target;
            const inputGroup = input.closest('.input-group') || input.closest('.form-group');

            // Remove error class and ARIA attributes
            input.classList.remove('error');
            input.setAttribute('aria-invalid', 'false');

            // Remove error message
            const errorDiv = inputGroup.querySelector('.field-error');
            if (errorDiv) {
                // Remove error ID from aria-describedby
                const describedBy = input.getAttribute('aria-describedby');
                if (describedBy) {
                    const newDescribedBy = describedBy.replace(errorDiv.id, '').trim();
                    if (newDescribedBy) {
                        input.setAttribute('aria-describedby', newDescribedBy);
                    } else {
                        input.removeAttribute('aria-describedby');
                    }
                }
                errorDiv.remove();
            }
        }

        function clearAllFieldErrors() {
            const inputs = document.querySelectorAll('input.error');
            inputs.forEach(input => {
                clearFieldError({ target: input });
            });
        }

        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('passwordToggleIcon');
            const toggleButton = document.querySelector('.password-toggle');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'icon-eye-off';
                toggleButton.setAttribute('aria-pressed', 'true');
                toggleButton.setAttribute('aria-label', 'Hide password');
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'icon-eye';
                toggleButton.setAttribute('aria-pressed', 'false');
                toggleButton.setAttribute('aria-label', 'Show password');
            }
        }

        function handleKeyboardShortcuts(e) {
            // Enter key on username field focuses password
            if (e.key === 'Enter' && e.target.id === 'username') {
                e.preventDefault();
                document.getElementById('password').focus();
            }

            // Ctrl+L to focus username field
            if (e.ctrlKey && e.key === 'l') {
                e.preventDefault();
                document.getElementById('username').focus();
                document.getElementById('username').select();
            }
        }

        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.innerHTML = `<i class="icon-${type}"></i> ${message}`;
            alertDiv.style.opacity = '0';
            alertDiv.style.transform = 'translateY(-10px)';

            const form = document.querySelector('.login-form');
            form.insertBefore(alertDiv, form.firstChild);

            // Animate in
            setTimeout(() => {
                alertDiv.style.transition = 'all 0.3s ease';
                alertDiv.style.opacity = '1';
                alertDiv.style.transform = 'translateY(0)';
            }, 10);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                alertDiv.style.opacity = '0';
                alertDiv.style.transform = 'translateY(-10px)';
                setTimeout(() => alertDiv.remove(), 300);
            }, 5000);
        }
    </script>
</body>
</html>
