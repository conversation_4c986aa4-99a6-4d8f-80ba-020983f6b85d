<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Admin Login Page
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

// Redirect if already logged in
if (isLoggedIn()) {
    redirect('dashboard.php');
}

$error = '';
$success = '';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Verify CSRF token
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('Invalid security token');
        }
        
        $username = sanitizeInput($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        
        // Validate input
        if (empty($username) || empty($password)) {
            throw new Exception('Please enter both username and password');
        }
        
        // Attempt authentication
        if (authenticateUser($username, $password)) {
            redirect('dashboard.php', 'Welcome back!', 'success');
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Generate CSRF token
$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <img src="../assets/images/logo.png" alt="SCIMS Logo" class="logo" onerror="this.style.display='none'">
                <h1>SCIMS Admin</h1>
                <p>Samar College Intramurals Management System</p>
            </div>
            
            <div class="login-form-container">
                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <i class="icon-error"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="icon-success"></i>
                        <?php echo htmlspecialchars($success); ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" action="" class="login-form" id="loginForm">
                    <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                    
                    <div class="form-group">
                        <label for="username">Username</label>
                        <div class="input-group">
                            <i class="icon-user"></i>
                            <input 
                                type="text" 
                                id="username" 
                                name="username" 
                                value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                                required 
                                autocomplete="username"
                                placeholder="Enter your username"
                            >
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">Password</label>
                        <div class="input-group">
                            <i class="icon-lock"></i>
                            <input 
                                type="password" 
                                id="password" 
                                name="password" 
                                required 
                                autocomplete="current-password"
                                placeholder="Enter your password"
                            >
                            <button type="button" class="password-toggle" onclick="togglePassword()">
                                <i class="icon-eye" id="passwordToggleIcon"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="checkbox-container">
                            <input type="checkbox" name="remember_me">
                            <span class="checkmark"></span>
                            Remember me
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-login">
                        <i class="icon-login"></i>
                        Sign In
                    </button>
                </form>
                
                <div class="login-footer">
                    <a href="forgot-password.php" class="forgot-password-link">
                        Forgot your password?
                    </a>
                </div>
            </div>
        </div>
        
        <div class="login-info">
            <h2>Welcome to SCIMS</h2>
            <p>The comprehensive sports event management system for Samar College intramural competitions.</p>
            
            <div class="features">
                <div class="feature">
                    <i class="icon-trophy"></i>
                    <h3>Event Management</h3>
                    <p>Organize and manage multiple sports events efficiently</p>
                </div>
                
                <div class="feature">
                    <i class="icon-chart"></i>
                    <h3>Real-time Scoring</h3>
                    <p>Live score updates and automatic standings calculation</p>
                </div>
                
                <div class="feature">
                    <i class="icon-users"></i>
                    <h3>Department Tracking</h3>
                    <p>Monitor department performance and rankings</p>
                </div>
            </div>
        </div>
    </div>
    
    <footer class="login-footer-bottom">
        <p>&copy; <?php echo date('Y'); ?> Samar College. All rights reserved. | Version <?php echo APP_VERSION; ?></p>
    </footer>
    
    <script src="../assets/js/admin.js"></script>
    <script>
        // Password toggle functionality
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('passwordToggleIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'icon-eye-off';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'icon-eye';
            }
        }
        
        // Form validation
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                e.preventDefault();
                showAlert('Please enter both username and password', 'error');
                return false;
            }
            
            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="icon-spinner"></i> Signing In...';
            submitBtn.disabled = true;
        });
        
        // Auto-focus username field
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('username').focus();
        });
        
        // Show alert function
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.innerHTML = `<i class="icon-${type}"></i> ${message}`;
            
            const form = document.querySelector('.login-form');
            form.insertBefore(alertDiv, form.firstChild);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }
    </script>
</body>
</html>
