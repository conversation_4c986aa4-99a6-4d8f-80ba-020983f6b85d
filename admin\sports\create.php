<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Sports Management - Create New Sport
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication
requireAuth();

$message = '';
$messageType = 'info';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('Invalid security token');
        }
        
        $name = sanitizeInput($_POST['name'] ?? '');
        $category = sanitizeInput($_POST['category'] ?? '');
        $scoring_type = sanitizeInput($_POST['scoring_type'] ?? '');
        $description = sanitizeInput($_POST['description'] ?? '');
        $rules = sanitizeInput($_POST['rules'] ?? '');
        $status = sanitizeInput($_POST['status'] ?? 'active');
        
        // Validation
        if (empty($name)) {
            throw new Exception('Sport name is required');
        }
        
        if (!in_array($category, ['individual', 'team', 'mixed'])) {
            throw new Exception('Invalid category selected');
        }
        
        if (!in_array($scoring_type, ['points', 'time', 'distance', 'subjective'])) {
            throw new Exception('Invalid scoring type selected');
        }
        
        // Check if sport name already exists
        $existing = fetchOne("SELECT sport_id FROM sports WHERE name = ?", [$name]);
        if ($existing) {
            throw new Exception('A sport with this name already exists');
        }
        
        // Insert sport
        $sportData = [
            'name' => $name,
            'category' => $category,
            'scoring_type' => $scoring_type,
            'description' => $description,
            'rules' => $rules,
            'status' => $status
        ];
        
        $sportId = insertRecord('sports', $sportData);
        
        logActivity('sport_created', "Sport '{$name}' created with ID {$sportId}");
        
        // Redirect to sports list
        header('Location: index.php?message=Sport created successfully&type=success');
        exit;
        
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Sport - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>
    
    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1>Create New Sport</h1>
                <nav class="breadcrumb">
                    <a href="../dashboard.php">Dashboard</a>
                    <span>/</span>
                    <a href="index.php">Sports</a>
                    <span>/</span>
                    <span>Create</span>
                </nav>
            </div>
            <div class="page-actions">
                <a href="index.php" class="btn btn-outline">
                    <i class="icon-arrow-left"></i>
                    Back to Sports
                </a>
            </div>
        </div>
        
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <div class="form-container">
            <form method="POST" class="form-card">
                <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                
                <div class="form-section">
                    <h3>Basic Information</h3>
                    
                    <div class="form-group">
                        <label for="name" class="form-label required">Sport Name</label>
                        <input type="text" id="name" name="name" class="form-control" 
                               value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>" required>
                        <small class="form-help">Enter the name of the sport</small>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="category" class="form-label required">Category</label>
                            <select id="category" name="category" class="form-control" required>
                                <option value="">Select Category</option>
                                <option value="individual" <?php echo ($_POST['category'] ?? '') === 'individual' ? 'selected' : ''; ?>>Individual</option>
                                <option value="team" <?php echo ($_POST['category'] ?? '') === 'team' ? 'selected' : ''; ?>>Team</option>
                                <option value="mixed" <?php echo ($_POST['category'] ?? '') === 'mixed' ? 'selected' : ''; ?>>Mixed</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="scoring_type" class="form-label required">Scoring Type</label>
                            <select id="scoring_type" name="scoring_type" class="form-control" required>
                                <option value="">Select Scoring Type</option>
                                <option value="points" <?php echo ($_POST['scoring_type'] ?? '') === 'points' ? 'selected' : ''; ?>>Points</option>
                                <option value="time" <?php echo ($_POST['scoring_type'] ?? '') === 'time' ? 'selected' : ''; ?>>Time</option>
                                <option value="distance" <?php echo ($_POST['scoring_type'] ?? '') === 'distance' ? 'selected' : ''; ?>>Distance</option>
                                <option value="subjective" <?php echo ($_POST['scoring_type'] ?? '') === 'subjective' ? 'selected' : ''; ?>>Subjective</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="status" class="form-label">Status</label>
                        <select id="status" name="status" class="form-control">
                            <option value="active" <?php echo ($_POST['status'] ?? 'active') === 'active' ? 'selected' : ''; ?>>Active</option>
                            <option value="inactive" <?php echo ($_POST['status'] ?? '') === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-section">
                    <h3>Description & Rules</h3>
                    
                    <div class="form-group">
                        <label for="description" class="form-label">Description</label>
                        <textarea id="description" name="description" class="form-control" rows="4" 
                                  placeholder="Brief description of the sport"><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="rules" class="form-label">Rules & Regulations</label>
                        <textarea id="rules" name="rules" class="form-control" rows="6" 
                                  placeholder="Detailed rules and regulations for this sport"><?php echo htmlspecialchars($_POST['rules'] ?? ''); ?></textarea>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="icon-check"></i>
                        Create Sport
                    </button>
                    <a href="index.php" class="btn btn-outline">Cancel</a>
                </div>
            </form>
        </div>
    </main>
    
    <script src="../../assets/js/admin.js"></script>
    <script>
        // Form validation
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const nameInput = document.getElementById('name');
            const categorySelect = document.getElementById('category');
            const scoringSelect = document.getElementById('scoring_type');
            
            form.addEventListener('submit', function(e) {
                let isValid = true;
                
                // Validate name
                if (!nameInput.value.trim()) {
                    showFieldError(nameInput, 'Sport name is required');
                    isValid = false;
                } else {
                    clearFieldError(nameInput);
                }
                
                // Validate category
                if (!categorySelect.value) {
                    showFieldError(categorySelect, 'Please select a category');
                    isValid = false;
                } else {
                    clearFieldError(categorySelect);
                }
                
                // Validate scoring type
                if (!scoringSelect.value) {
                    showFieldError(scoringSelect, 'Please select a scoring type');
                    isValid = false;
                } else {
                    clearFieldError(scoringSelect);
                }
                
                if (!isValid) {
                    e.preventDefault();
                }
            });
            
            function showFieldError(field, message) {
                clearFieldError(field);
                field.classList.add('error');
                const errorDiv = document.createElement('div');
                errorDiv.className = 'field-error';
                errorDiv.textContent = message;
                field.parentNode.appendChild(errorDiv);
            }
            
            function clearFieldError(field) {
                field.classList.remove('error');
                const existingError = field.parentNode.querySelector('.field-error');
                if (existingError) {
                    existingError.remove();
                }
            }
        });
    </script>
</body>
</html>
