/**
 * Samar College Intramurals Management System (SCIMS)
 * Public Interface JavaScript
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

'use strict';

// Global SCIMS object
const SCIMS = {
    // Configuration
    config: {
        updateInterval: 30000, // 30 seconds
        animationDuration: 300,
        cacheExpiry: 300000, // 5 minutes
        maxRetries: 3
    },
    
    // State management
    state: {
        isOnline: navigator.onLine,
        cache: new Map(),
        lastUpdate: null,
        retryCount: 0
    },
    
    // Initialize the application
    init() {
        this.setupEventListeners();
        this.initializeComponents();
        this.startLiveUpdates();
        this.setupServiceWorker();
        console.log('SCIMS Public Interface initialized');
    },
    
    // Setup global event listeners
    setupEventListeners() {
        // Mobile menu toggle
        const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
        const navMenu = document.querySelector('.nav-menu');
        
        if (mobileMenuToggle && navMenu) {
            mobileMenuToggle.addEventListener('click', () => {
                navMenu.classList.toggle('mobile-open');
                mobileMenuToggle.classList.toggle('active');
            });
        }
        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(anchor.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // Search functionality
        this.setupSearch();
        
        // Filter functionality
        this.setupFilters();
        
        // Online/offline status
        window.addEventListener('online', () => this.handleOnlineStatus(true));
        window.addEventListener('offline', () => this.handleOnlineStatus(false));
        
        // Visibility change for live updates
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && this.state.isOnline) {
                this.updateLiveData();
            }
        });
    },
    
    // Initialize components
    initializeComponents() {
        // Initialize live ticker
        this.initializeLiveTicker();
        
        // Initialize countdown timers
        this.initializeCountdowns();
        
        // Initialize lazy loading
        this.initializeLazyLoading();
        
        // Initialize animations
        this.initializeAnimations();
        
        // Initialize PWA features
        this.initializePWA();
    },
    
    // Setup search functionality
    setupSearch() {
        const searchInput = document.querySelector('.search-input');
        const searchResults = document.querySelector('.search-results');
        
        if (searchInput) {
            let searchTimeout;
            
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                const query = e.target.value.trim();
                
                if (query.length >= 2) {
                    searchTimeout = setTimeout(() => {
                        this.performSearch(query, searchResults);
                    }, 300);
                } else if (searchResults) {
                    searchResults.innerHTML = '';
                    searchResults.style.display = 'none';
                }
            });
            
            // Close search results when clicking outside
            document.addEventListener('click', (e) => {
                if (!searchInput.contains(e.target) && searchResults) {
                    searchResults.style.display = 'none';
                }
            });
        }
    },
    
    // Perform search
    async performSearch(query, resultsContainer) {
        if (!resultsContainer) return;
        
        try {
            resultsContainer.innerHTML = '<div class="search-loading">Searching...</div>';
            resultsContainer.style.display = 'block';
            
            const response = await fetch(`/api/search.php?q=${encodeURIComponent(query)}`);
            const data = await response.json();
            
            if (data.success && data.results.length > 0) {
                resultsContainer.innerHTML = data.results.map(result => `
                    <div class="search-result-item">
                        <h4><a href="${result.url}">${result.title}</a></h4>
                        <p>${result.description}</p>
                        <small>${result.type}</small>
                    </div>
                `).join('');
            } else {
                resultsContainer.innerHTML = '<div class="search-no-results">No results found</div>';
            }
        } catch (error) {
            console.error('Search error:', error);
            resultsContainer.innerHTML = '<div class="search-error">Search unavailable</div>';
        }
    },
    
    // Setup filters
    setupFilters() {
        const filterButtons = document.querySelectorAll('.filter-btn');
        const filterableItems = document.querySelectorAll('.filterable-item');
        
        filterButtons.forEach(button => {
            button.addEventListener('click', () => {
                const filter = button.getAttribute('data-filter');
                
                // Update active filter button
                filterButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
                
                // Filter items
                this.filterItems(filterableItems, filter);
            });
        });
    },
    
    // Filter items
    filterItems(items, filter) {
        items.forEach(item => {
            const itemCategories = item.getAttribute('data-categories');
            const shouldShow = filter === 'all' || 
                             (itemCategories && itemCategories.includes(filter));
            
            if (shouldShow) {
                item.style.display = '';
                item.classList.add('fade-in');
            } else {
                item.style.display = 'none';
                item.classList.remove('fade-in');
            }
        });
    },
    
    // Initialize live ticker
    initializeLiveTicker() {
        const ticker = document.querySelector('.live-ticker');
        if (!ticker) return;
        
        // Pause animation on hover
        ticker.addEventListener('mouseenter', () => {
            ticker.style.animationPlayState = 'paused';
        });
        
        ticker.addEventListener('mouseleave', () => {
            ticker.style.animationPlayState = 'running';
        });
        
        // Update ticker content
        this.updateTicker();
    },
    
    // Update ticker content
    async updateTicker() {
        const tickerContent = document.querySelector('.ticker-content');
        if (!tickerContent) return;
        
        try {
            const response = await fetch('/api/live-scores.php');
            const data = await response.json();
            
            if (data.success && data.scores.length > 0) {
                tickerContent.innerHTML = data.scores.map(score => `
                    <span class="ticker-item">
                        ${score.sport}: ${score.team1} ${score.score1} - ${score.score2} ${score.team2}
                        ${score.status === 'ongoing' ? '(LIVE)' : '(FINAL)'}
                    </span>
                `).join('');
            }
        } catch (error) {
            console.error('Ticker update error:', error);
        }
    },
    
    // Initialize countdown timers
    initializeCountdowns() {
        const countdowns = document.querySelectorAll('.countdown');
        
        countdowns.forEach(countdown => {
            const targetDate = new Date(countdown.getAttribute('data-target'));
            this.startCountdown(countdown, targetDate);
        });
    },
    
    // Start countdown
    startCountdown(element, targetDate) {
        const updateCountdown = () => {
            const now = new Date().getTime();
            const distance = targetDate.getTime() - now;
            
            if (distance < 0) {
                element.innerHTML = 'Event Started!';
                return;
            }
            
            const days = Math.floor(distance / (1000 * 60 * 60 * 24));
            const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((distance % (1000 * 60)) / 1000);
            
            element.innerHTML = `
                <div class="countdown-item">
                    <span class="countdown-number">${days}</span>
                    <span class="countdown-label">Days</span>
                </div>
                <div class="countdown-item">
                    <span class="countdown-number">${hours}</span>
                    <span class="countdown-label">Hours</span>
                </div>
                <div class="countdown-item">
                    <span class="countdown-number">${minutes}</span>
                    <span class="countdown-label">Minutes</span>
                </div>
                <div class="countdown-item">
                    <span class="countdown-number">${seconds}</span>
                    <span class="countdown-label">Seconds</span>
                </div>
            `;
        };
        
        updateCountdown();
        setInterval(updateCountdown, 1000);
    },
    
    // Initialize lazy loading
    initializeLazyLoading() {
        const images = document.querySelectorAll('img[data-src]');
        
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            images.forEach(img => imageObserver.observe(img));
        } else {
            // Fallback for older browsers
            images.forEach(img => {
                img.src = img.dataset.src;
                img.classList.remove('lazy');
            });
        }
    },
    
    // Initialize animations
    initializeAnimations() {
        const animatedElements = document.querySelectorAll('.animate-on-scroll');
        
        if ('IntersectionObserver' in window) {
            const animationObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animated');
                    }
                });
            }, { threshold: 0.1 });
            
            animatedElements.forEach(el => animationObserver.observe(el));
        }
    },
    
    // Initialize PWA features
    initializePWA() {
        // Add to home screen prompt
        let deferredPrompt;
        
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            this.showInstallPrompt();
        });
        
        // Handle app installation
        window.addEventListener('appinstalled', () => {
            console.log('SCIMS PWA installed');
            this.hideInstallPrompt();
        });
    },
    
    // Show install prompt
    showInstallPrompt() {
        const installBanner = document.createElement('div');
        installBanner.className = 'install-banner';
        installBanner.innerHTML = `
            <div class="install-content">
                <p>Install SCIMS for quick access to live scores and schedules!</p>
                <button class="btn btn-primary install-btn">Install</button>
                <button class="btn btn-secondary dismiss-btn">Dismiss</button>
            </div>
        `;
        
        document.body.appendChild(installBanner);
        
        // Handle install button click
        installBanner.querySelector('.install-btn').addEventListener('click', async () => {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                const { outcome } = await deferredPrompt.userChoice;
                console.log(`User response to install prompt: ${outcome}`);
                deferredPrompt = null;
            }
            this.hideInstallPrompt();
        });
        
        // Handle dismiss button click
        installBanner.querySelector('.dismiss-btn').addEventListener('click', () => {
            this.hideInstallPrompt();
        });
    },
    
    // Hide install prompt
    hideInstallPrompt() {
        const installBanner = document.querySelector('.install-banner');
        if (installBanner) {
            installBanner.remove();
        }
    },
    
    // Setup service worker
    setupServiceWorker() {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                    console.log('Service Worker registered:', registration);
                })
                .catch(error => {
                    console.log('Service Worker registration failed:', error);
                });
        }
    },
    
    // Start live updates
    startLiveUpdates() {
        setInterval(() => {
            if (this.state.isOnline && !document.hidden) {
                this.updateLiveData();
            }
        }, this.config.updateInterval);
    },
    
    // Update live data
    async updateLiveData() {
        try {
            // Update live scores
            await this.updateTicker();
            
            // Update standings if on standings page
            if (document.querySelector('.standings-table')) {
                await this.updateStandings();
            }
            
            // Update match status if on schedule page
            if (document.querySelector('.schedule-table')) {
                await this.updateSchedule();
            }
            
            this.state.lastUpdate = Date.now();
            this.state.retryCount = 0;
            
        } catch (error) {
            console.error('Live update error:', error);
            this.handleUpdateError();
        }
    },
    
    // Update standings
    async updateStandings() {
        try {
            const response = await fetch('/api/standings.php');
            const data = await response.json();
            
            if (data.success) {
                const standingsTable = document.querySelector('.standings-table tbody');
                if (standingsTable) {
                    standingsTable.innerHTML = data.html;
                }
            }
        } catch (error) {
            console.error('Standings update error:', error);
        }
    },
    
    // Update schedule
    async updateSchedule() {
        try {
            const response = await fetch('/api/schedule.php');
            const data = await response.json();
            
            if (data.success) {
                const scheduleTable = document.querySelector('.schedule-table tbody');
                if (scheduleTable) {
                    scheduleTable.innerHTML = data.html;
                }
            }
        } catch (error) {
            console.error('Schedule update error:', error);
        }
    },
    
    // Handle update errors
    handleUpdateError() {
        this.state.retryCount++;
        
        if (this.state.retryCount >= this.config.maxRetries) {
            console.warn('Max retries reached for live updates');
            this.showOfflineMessage();
        }
    },
    
    // Show offline message
    showOfflineMessage() {
        const message = document.createElement('div');
        message.className = 'offline-message';
        message.innerHTML = `
            <p>Live updates are currently unavailable. Data may not be current.</p>
            <button onclick="location.reload()">Refresh Page</button>
        `;
        
        document.body.appendChild(message);
        
        setTimeout(() => {
            if (message.parentElement) {
                message.remove();
            }
        }, 10000);
    },
    
    // Handle online/offline status
    handleOnlineStatus(isOnline) {
        this.state.isOnline = isOnline;
        
        const statusIndicator = document.querySelector('.connection-status');
        if (statusIndicator) {
            statusIndicator.textContent = isOnline ? 'Online' : 'Offline';
            statusIndicator.className = `connection-status ${isOnline ? 'online' : 'offline'}`;
        }
        
        if (isOnline) {
            this.state.retryCount = 0;
            this.updateLiveData();
        }
    },
    
    // Utility functions
    formatDate(date) {
        return new Date(date).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    },
    
    formatTime(time) {
        return new Date(`2000-01-01 ${time}`).toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });
    },
    
    // Cache management
    setCache(key, data, expiry = this.config.cacheExpiry) {
        this.state.cache.set(key, {
            data,
            expiry: Date.now() + expiry
        });
    },
    
    getCache(key) {
        const cached = this.state.cache.get(key);
        if (cached && cached.expiry > Date.now()) {
            return cached.data;
        }
        this.state.cache.delete(key);
        return null;
    },
    
    clearCache() {
        this.state.cache.clear();
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    SCIMS.init();
});

// Export for global access
window.SCIMS = SCIMS;
