<?php
/**
 * Quick Database Setup for SCIMS
 * This script will create the database and import the schema
 */

// Database Configuration
$host = 'localhost';
$dbname = 'IMS_db';
$username = 'root';
$password = '';

echo "<h1>SCIMS Quick Database Setup</h1>";

try {
    // Step 1: Connect to MySQL server (without database)
    echo "<p>Step 1: Connecting to MySQL server...</p>";
    $dsn = "mysql:host=$host;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ Connected to MySQL server successfully!</p>";
    
    // Step 2: Create database
    echo "<p>Step 2: Creating database '$dbname'...</p>";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p style='color: green;'>✅ Database '$dbname' created successfully!</p>";
    
    // Step 3: Connect to the new database
    echo "<p>Step 3: Connecting to database '$dbname'...</p>";
    $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ Connected to database '$dbname' successfully!</p>";
    
    // Step 4: Import SQL schema
    echo "<p>Step 4: Importing database schema...</p>";
    $sqlFile = 'sql/database_schema.sql';
    
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL file not found: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    
    // Remove CREATE DATABASE and USE statements since we already created the database
    $sql = preg_replace('/CREATE DATABASE.*?;/i', '', $sql);
    $sql = preg_replace('/USE.*?;/i', '', $sql);
    
    // Split into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    $successCount = 0;
    foreach ($statements as $statement) {
        if (!empty($statement) && !preg_match('/^--/', $statement)) {
            try {
                $pdo->exec($statement);
                $successCount++;
            } catch (PDOException $e) {
                echo "<p style='color: orange;'>⚠️ Warning: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
        }
    }
    
    echo "<p style='color: green;'>✅ Executed $successCount SQL statements successfully!</p>";
    
    // Step 5: Verify tables
    echo "<p>Step 5: Verifying tables...</p>";
    $tables = [
        'admin_users',
        'events', 
        'departments',
        'sports',
        'venues',
        'matches',
        'match_participants',
        'scores',
        'department_standings',
        'event_sports',
        'announcements',
        'referees',
        'match_officials',
        'system_settings'
    ];
    
    $tableCount = 0;
    foreach ($tables as $table) {
        $result = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($result->rowCount() > 0) {
            $count = $pdo->query("SELECT COUNT(*) FROM $table")->fetchColumn();
            echo "<p style='color: green;'>✅ Table '$table' exists with $count records</p>";
            $tableCount++;
        } else {
            echo "<p style='color: red;'>❌ Table '$table' not found</p>";
        }
    }
    
    echo "<h2 style='color: green;'>🎉 Setup Complete!</h2>";
    echo "<p><strong>Summary:</strong></p>";
    echo "<ul>";
    echo "<li>Database '$dbname' created</li>";
    echo "<li>$tableCount tables created</li>";
    echo "<li>Sample data inserted</li>";
    echo "</ul>";
    
    echo "<p><strong>Default Admin Credentials:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Username:</strong> admin</li>";
    echo "<li><strong>Password:</strong> Admin123!</li>";
    echo "</ul>";
    
    echo "<p><strong>Next Steps:</strong></p>";
    echo "<ol>";
    echo "<li><a href='test-db.php' target='_blank'>Test Database Connection</a></li>";
    echo "<li><a href='admin/' target='_blank'>Login to Admin Panel</a></li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>Troubleshooting:</strong></p>";
    echo "<ul>";
    echo "<li>Make sure XAMPP/WAMP is running</li>";
    echo "<li>Check that MySQL service is started</li>";
    echo "<li>Verify database credentials are correct</li>";
    echo "<li>Ensure the sql/database_schema.sql file exists</li>";
    echo "</ul>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: #f5f5f5;
}
h1, h2 {
    color: #333;
}
p {
    margin: 10px 0;
}
ul, ol {
    margin: 10px 0;
    padding-left: 30px;
}
a {
    color: #007cba;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
</style>
