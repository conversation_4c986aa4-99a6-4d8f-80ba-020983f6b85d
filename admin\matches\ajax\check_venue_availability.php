<?php
/**
 * Check Venue Availability AJAX Endpoint
 */

define('SCIMS_ACCESS', true);
require_once '../../../includes/config.php';
require_once '../../../includes/functions.php';
require_once '../../../includes/auth.php';

// Require authentication
requireAuth();

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);

$venueId = (int)($input['venue_id'] ?? 0);
$date = sanitizeInput($input['date'] ?? '');
$time = sanitizeInput($input['time'] ?? '');

if (!$venueId || !$date || !$time) {
    echo json_encode(['error' => 'Missing required parameters']);
    exit;
}

try {
    // Check for conflicts
    $conflict = fetchOne("
        SELECT COUNT(*) as count 
        FROM matches 
        WHERE venue_id = ? AND match_date = ? AND match_time = ? AND status != 'cancelled'
    ", [$venueId, $date, $time]);
    
    $available = $conflict['count'] == 0;
    
    echo json_encode([
        'available' => $available,
        'message' => $available ? 'Venue is available' : 'Venue is already booked at this time'
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Database error']);
}
?>
