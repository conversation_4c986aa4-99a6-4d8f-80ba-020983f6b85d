<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Events Management - Edit Event AJAX Handler
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication and permission
requireAuth();
requirePermission('manage_events');

$eventId = (int)($_GET['id'] ?? $_POST['event_id'] ?? 0);

if (!$eventId) {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'Invalid event ID']);
        exit;
    } else {
        echo '<div class="alert alert-error">Invalid event ID</div>';
        exit;
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    try {
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('Invalid security token');
        }
        
        $name = sanitizeInput($_POST['name'] ?? '');
        $description = sanitizeInput($_POST['description'] ?? '');
        $startDate = sanitizeInput($_POST['start_date'] ?? '');
        $endDate = sanitizeInput($_POST['end_date'] ?? '');
        $status = sanitizeInput($_POST['status'] ?? '');
        
        // Validation
        if (empty($name)) {
            throw new Exception('Event name is required');
        }
        
        if (empty($startDate)) {
            throw new Exception('Start date is required');
        }
        
        if (empty($endDate)) {
            throw new Exception('End date is required');
        }
        
        if (!in_array($status, ['upcoming', 'ongoing', 'completed'])) {
            throw new Exception('Invalid status');
        }
        
        if (strtotime($endDate) < strtotime($startDate)) {
            throw new Exception('End date must be after start date');
        }
        
        // Update event
        $updateData = [
            'name' => $name,
            'description' => $description,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'status' => $status,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        updateRecord('events', $updateData, 'event_id = :event_id', ['event_id' => $eventId]);
        
        logActivity('event_updated', "Event ID {$eventId} updated");
        
        echo json_encode(['success' => true, 'message' => 'Event updated successfully']);
        exit;
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        exit;
    }
}

// Get event details for editing
$event = fetchOne("SELECT * FROM events WHERE event_id = ?", [$eventId]);

if (!$event) {
    echo '<div class="alert alert-error">Event not found</div>';
    exit;
}

$csrfToken = generateCSRFToken();
?>

<div id="editFormErrors"></div>

<form id="editEventForm" class="edit-event-form">
    <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
    <input type="hidden" name="event_id" value="<?php echo $eventId; ?>">
    
    <div class="form-grid">
        <div class="form-group">
            <label for="edit_name">Event Name <span class="required">*</span></label>
            <input type="text" 
                   id="edit_name" 
                   name="name" 
                   value="<?php echo htmlspecialchars($event['name']); ?>" 
                   required 
                   class="form-control"
                   placeholder="Enter event name">
        </div>
        
        <div class="form-group">
            <label for="edit_status">Status <span class="required">*</span></label>
            <select id="edit_status" name="status" required class="form-control">
                <option value="upcoming" <?php echo $event['status'] === 'upcoming' ? 'selected' : ''; ?>>Upcoming</option>
                <option value="ongoing" <?php echo $event['status'] === 'ongoing' ? 'selected' : ''; ?>>Ongoing</option>
                <option value="completed" <?php echo $event['status'] === 'completed' ? 'selected' : ''; ?>>Completed</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="edit_start_date">Start Date <span class="required">*</span></label>
            <input type="date" 
                   id="edit_start_date" 
                   name="start_date" 
                   value="<?php echo $event['start_date']; ?>" 
                   required 
                   class="form-control">
        </div>
        
        <div class="form-group">
            <label for="edit_end_date">End Date <span class="required">*</span></label>
            <input type="date" 
                   id="edit_end_date" 
                   name="end_date" 
                   value="<?php echo $event['end_date']; ?>" 
                   required 
                   class="form-control">
        </div>
        
        <div class="form-group form-group-full">
            <label for="edit_description">Description</label>
            <textarea id="edit_description" 
                      name="description" 
                      rows="4" 
                      class="form-control"
                      placeholder="Enter event description (optional)"><?php echo htmlspecialchars($event['description']); ?></textarea>
        </div>
    </div>
    
    <div class="form-actions">
        <button type="button" class="btn btn-secondary" onclick="closeModal('editModal')">
            <i class="icon-cancel"></i>
            Cancel
        </button>
        <button type="submit" class="btn btn-primary">
            <i class="icon-save"></i>
            Save Changes
        </button>
    </div>
</form>

<style>
/* CSS Variables for compatibility */
:root {
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-900: #111827;
    --white: #ffffff;
    --red-50: #fef2f2;
    --red-200: #fecaca;
    --red-300: #fca5a5;
    --red-500: #ef4444;
    --red-700: #b91c1c;
    --green-50: #f0fdf4;
    --green-200: #bbf7d0;
    --green-300: #86efac;
    --green-700: #15803d;
}

.edit-event-form {
    padding: 1rem 0;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.form-group-full {
    grid-column: 1 / -1;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.required {
    color: var(--red-500);
}

.form-control {
    padding: 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: 6px;
    font-size: 0.875rem;
    transition: border-color 0.2s, box-shadow 0.2s;
    background: var(--white);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-control:invalid {
    border-color: var(--red-300);
}

.form-control:invalid:focus {
    border-color: var(--red-500);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

textarea.form-control {
    resize: vertical;
    min-height: 100px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--gray-200);
    margin-top: 1rem;
    background: white;
    position: relative;
    z-index: 10;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-primary {
    background: var(--primary-600, #2563eb);
    color: var(--white, #ffffff);
    border: none;
}

.btn-primary:hover {
    background: var(--primary-700, #1d4ed8);
}

.btn-primary:disabled {
    background: var(--gray-400, #9ca3af);
    cursor: not-allowed;
}

.btn-secondary {
    background: var(--gray-100, #f3f4f6);
    color: var(--gray-700, #374151);
    border: 1px solid var(--gray-300, #d1d5db);
}

.btn-secondary:hover {
    background: var(--gray-200, #e5e7eb);
}

.alert {
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.alert-error {
    background: var(--red-50);
    color: var(--red-700);
    border: 1px solid var(--red-200);
}

.alert-success {
    background: var(--green-50);
    color: var(--green-700);
    border: 1px solid var(--green-200);
}

/* Responsive design */
@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .btn {
        justify-content: center;
    }
}

/* Form validation styles */
.form-control:valid {
    border-color: var(--green-300);
}

.form-control:required:invalid {
    border-color: var(--red-300);
}

/* Loading state */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Focus styles for accessibility */
.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.form-control:focus {
    outline: none;
}

/* Smooth transitions */
* {
    transition: border-color 0.2s ease, box-shadow 0.2s ease, background-color 0.2s ease;
}
</style>

<script>
// Add client-side validation
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('editEventForm');
    const startDateInput = document.getElementById('edit_start_date');
    const endDateInput = document.getElementById('edit_end_date');
    
    // Date validation
    function validateDates() {
        const startDate = new Date(startDateInput.value);
        const endDate = new Date(endDateInput.value);
        
        if (startDate && endDate && endDate < startDate) {
            endDateInput.setCustomValidity('End date must be after start date');
        } else {
            endDateInput.setCustomValidity('');
        }
    }
    
    startDateInput.addEventListener('change', validateDates);
    endDateInput.addEventListener('change', validateDates);
    
    // Real-time validation feedback
    const inputs = form.querySelectorAll('.form-control');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.checkValidity()) {
                this.classList.remove('invalid');
                this.classList.add('valid');
            } else {
                this.classList.remove('valid');
                this.classList.add('invalid');
            }
        });
    });
});
</script>
