<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Installation Setup Script
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

// Prevent running if already installed
if (file_exists('includes/config.php') && !isset($_GET['force'])) {
    die('SCIMS is already installed. Add ?force=1 to reinstall.');
}

$step = $_GET['step'] ?? 1;
$errors = [];
$success = [];

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($step) {
        case 1:
            // Database configuration
            $dbHost = $_POST['db_host'] ?? 'localhost';
            $dbName = $_POST['db_name'] ?? 'IMS_db';
            $dbUser = $_POST['db_user'] ?? 'root';
            $dbPass = $_POST['db_pass'] ?? '';
            
            // Test database connection
            try {
                $dsn = "mysql:host={$dbHost};charset=utf8mb4";
                $pdo = new PDO($dsn, $dbUser, $dbPass);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // Create database if it doesn't exist
                $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$dbName}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                $pdo->exec("USE `{$dbName}`");
                
                // Store database config in session
                session_start();
                $_SESSION['db_config'] = [
                    'host' => $dbHost,
                    'name' => $dbName,
                    'user' => $dbUser,
                    'pass' => $dbPass
                ];
                
                $success[] = "Database connection successful!";
                $step = 2;
                
            } catch (PDOException $e) {
                $errors[] = "Database connection failed: " . $e->getMessage();
            }
            break;
            
        case 2:
            // Import database schema
            session_start();
            $dbConfig = $_SESSION['db_config'];
            
            try {
                $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['name']};charset=utf8mb4";
                $pdo = new PDO($dsn, $dbConfig['user'], $dbConfig['pass']);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // Read and execute SQL file
                $sqlFile = 'sql/database_schema.sql';
                if (file_exists($sqlFile)) {
                    $sql = file_get_contents($sqlFile);
                    
                    // Split SQL into individual statements
                    $statements = array_filter(array_map('trim', explode(';', $sql)));
                    
                    foreach ($statements as $statement) {
                        if (!empty($statement) && !preg_match('/^(CREATE DATABASE|USE)/i', $statement)) {
                            $pdo->exec($statement);
                        }
                    }
                    
                    $success[] = "Database schema imported successfully!";
                    $step = 3;
                } else {
                    $errors[] = "Database schema file not found!";
                }
                
            } catch (PDOException $e) {
                $errors[] = "Database import failed: " . $e->getMessage();
            }
            break;
            
        case 3:
            // Create admin user
            session_start();
            $dbConfig = $_SESSION['db_config'];
            
            $username = $_POST['admin_username'] ?? '';
            $password = $_POST['admin_password'] ?? '';
            $email = $_POST['admin_email'] ?? '';
            $fullName = $_POST['admin_name'] ?? '';
            
            // Validate input
            if (empty($username) || empty($password) || empty($email) || empty($fullName)) {
                $errors[] = "All fields are required!";
            } elseif (strlen($password) < 8) {
                $errors[] = "Password must be at least 8 characters long!";
            } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $errors[] = "Invalid email address!";
            } else {
                try {
                    $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['name']};charset=utf8mb4";
                    $pdo = new PDO($dsn, $dbConfig['user'], $dbConfig['pass']);
                    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                    
                    // Hash password
                    $passwordHash = password_hash($password, PASSWORD_DEFAULT);
                    
                    // Insert admin user
                    $stmt = $pdo->prepare("
                        INSERT INTO admin_users (username, password_hash, email, full_name, role, status) 
                        VALUES (?, ?, ?, ?, 'super_admin', 'active')
                    ");
                    $stmt->execute([$username, $passwordHash, $email, $fullName]);
                    
                    $success[] = "Admin user created successfully!";
                    $step = 4;
                    
                } catch (PDOException $e) {
                    $errors[] = "Failed to create admin user: " . $e->getMessage();
                }
            }
            break;
            
        case 4:
            // Create configuration file
            session_start();
            $dbConfig = $_SESSION['db_config'];
            
            $appUrl = $_POST['app_url'] ?? 'http://localhost/IMS';
            
            $configContent = "<?php\n";
            $configContent .= "/**\n";
            $configContent .= " * Samar College Intramurals Management System (SCIMS)\n";
            $configContent .= " * Configuration File - Generated by Setup\n";
            $configContent .= " */\n\n";
            $configContent .= "// Prevent direct access\n";
            $configContent .= "if (!defined('SCIMS_ACCESS')) {\n";
            $configContent .= "    die('Direct access not permitted');\n";
            $configContent .= "}\n\n";
            $configContent .= "// Database Configuration\n";
            $configContent .= "define('DB_HOST', '" . addslashes($dbConfig['host']) . "');\n";
            $configContent .= "define('DB_NAME', '" . addslashes($dbConfig['name']) . "');\n";
            $configContent .= "define('DB_USER', '" . addslashes($dbConfig['user']) . "');\n";
            $configContent .= "define('DB_PASS', '" . addslashes($dbConfig['pass']) . "');\n";
            $configContent .= "define('DB_CHARSET', 'utf8mb4');\n\n";
            $configContent .= "// Application Configuration\n";
            $configContent .= "define('APP_NAME', 'Samar College Intramurals Management System');\n";
            $configContent .= "define('APP_VERSION', '1.0.0');\n";
            $configContent .= "define('APP_URL', '" . addslashes($appUrl) . "');\n";
            $configContent .= "define('ADMIN_URL', APP_URL . '/admin');\n";
            $configContent .= "define('PUBLIC_URL', APP_URL . '/public');\n\n";
            $configContent .= "// Include the rest of the configuration\n";
            $configContent .= "require_once __DIR__ . '/config_base.php';\n";
            $configContent .= "?>";
            
            // Create includes directory if it doesn't exist
            if (!is_dir('includes')) {
                mkdir('includes', 0755, true);
            }
            
            // Write configuration file
            if (file_put_contents('includes/config.php', $configContent)) {
                // Create directories
                $directories = [
                    'assets/images/uploads',
                    'logs',
                    'backups'
                ];
                
                foreach ($directories as $dir) {
                    if (!is_dir($dir)) {
                        mkdir($dir, 0755, true);
                    }
                }
                
                // Create .htaccess for security
                $htaccessContent = "# SCIMS Security Rules\n";
                $htaccessContent .= "RewriteEngine On\n\n";
                $htaccessContent .= "# Deny access to sensitive files\n";
                $htaccessContent .= "<Files ~ \"\\.(sql|log|bak)$\">\n";
                $htaccessContent .= "    Order allow,deny\n";
                $htaccessContent .= "    Deny from all\n";
                $htaccessContent .= "</Files>\n\n";
                $htaccessContent .= "# Deny access to includes directory\n";
                $htaccessContent .= "<Directory \"includes\">\n";
                $htaccessContent .= "    Order allow,deny\n";
                $htaccessContent .= "    Deny from all\n";
                $htaccessContent .= "</Directory>\n";
                
                file_put_contents('.htaccess', $htaccessContent);
                
                $success[] = "Configuration file created successfully!";
                $success[] = "SCIMS installation completed!";
                
                // Clear session
                session_destroy();
                $step = 5;
                
            } else {
                $errors[] = "Failed to create configuration file. Please check file permissions.";
            }
            break;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SCIMS Setup - Step <?php echo $step; ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }
        
        .setup-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 100%;
            overflow: hidden;
        }
        
        .setup-header {
            background: #f8fafc;
            padding: 2rem;
            text-align: center;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .setup-header h1 {
            color: #1e293b;
            margin-bottom: 0.5rem;
        }
        
        .setup-header p {
            color: #64748b;
        }
        
        .setup-content {
            padding: 2rem;
        }
        
        .progress-bar {
            background: #e2e8f0;
            height: 8px;
            border-radius: 4px;
            margin-bottom: 2rem;
            overflow: hidden;
        }
        
        .progress-fill {
            background: #2563eb;
            height: 100%;
            transition: width 0.3s ease;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #374151;
        }
        
        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.2s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #2563eb;
        }
        
        .btn {
            background: #2563eb;
            color: white;
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .btn:hover {
            background: #1d4ed8;
        }
        
        .btn-secondary {
            background: #6b7280;
            margin-right: 1rem;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .alert-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        
        .step-info {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
        }
        
        .step-info h3 {
            color: #0369a1;
            margin-bottom: 0.5rem;
        }
        
        .step-info p {
            color: #0284c7;
            margin: 0;
        }
        
        .requirements {
            background: #fffbeb;
            border: 1px solid #fed7aa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
        }
        
        .requirements h4 {
            color: #92400e;
            margin-bottom: 0.5rem;
        }
        
        .requirements ul {
            color: #b45309;
            margin-left: 1.5rem;
        }
        
        .success-message {
            text-align: center;
            padding: 2rem;
        }
        
        .success-message h2 {
            color: #059669;
            margin-bottom: 1rem;
        }
        
        .success-message p {
            color: #6b7280;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <h1>SCIMS Setup</h1>
            <p>Samar College Intramurals Management System Installation</p>
        </div>
        
        <div class="setup-content">
            <!-- Progress Bar -->
            <div class="progress-bar">
                <div class="progress-fill" style="width: <?php echo ($step / 5) * 100; ?>%"></div>
            </div>
            
            <!-- Display Messages -->
            <?php foreach ($errors as $error): ?>
                <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
            <?php endforeach; ?>
            
            <?php foreach ($success as $message): ?>
                <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
            <?php endforeach; ?>
            
            <?php if ($step == 1): ?>
                <!-- Step 1: Database Configuration -->
                <div class="step-info">
                    <h3>Step 1: Database Configuration</h3>
                    <p>Configure your MySQL database connection settings.</p>
                </div>
                
                <div class="requirements">
                    <h4>Requirements:</h4>
                    <ul>
                        <li>MySQL 8.0+ or MariaDB 10.3+</li>
                        <li>PHP 8.0+ with PDO MySQL extension</li>
                        <li>Database user with CREATE, INSERT, UPDATE, DELETE privileges</li>
                    </ul>
                </div>
                
                <form method="POST">
                    <div class="form-group">
                        <label for="db_host">Database Host:</label>
                        <input type="text" id="db_host" name="db_host" value="localhost" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="db_name">Database Name:</label>
                        <input type="text" id="db_name" name="db_name" value="IMS_db" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="db_user">Database Username:</label>
                        <input type="text" id="db_user" name="db_user" value="root" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="db_pass">Database Password:</label>
                        <input type="password" id="db_pass" name="db_pass">
                    </div>
                    
                    <button type="submit" class="btn">Test Connection & Continue</button>
                </form>
                
            <?php elseif ($step == 2): ?>
                <!-- Step 2: Database Import -->
                <div class="step-info">
                    <h3>Step 2: Database Schema</h3>
                    <p>Import the database schema and sample data.</p>
                </div>
                
                <form method="POST">
                    <p>Click the button below to import the database schema and sample data.</p>
                    <button type="submit" class="btn">Import Database Schema</button>
                </form>
                
            <?php elseif ($step == 3): ?>
                <!-- Step 3: Admin User -->
                <div class="step-info">
                    <h3>Step 3: Create Admin User</h3>
                    <p>Create the initial administrator account.</p>
                </div>
                
                <form method="POST">
                    <div class="form-group">
                        <label for="admin_username">Admin Username:</label>
                        <input type="text" id="admin_username" name="admin_username" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="admin_password">Admin Password:</label>
                        <input type="password" id="admin_password" name="admin_password" required>
                        <small>Minimum 8 characters with uppercase, lowercase, number, and special character</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="admin_email">Admin Email:</label>
                        <input type="email" id="admin_email" name="admin_email" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="admin_name">Full Name:</label>
                        <input type="text" id="admin_name" name="admin_name" required>
                    </div>
                    
                    <button type="submit" class="btn">Create Admin User</button>
                </form>
                
            <?php elseif ($step == 4): ?>
                <!-- Step 4: Application Configuration -->
                <div class="step-info">
                    <h3>Step 4: Application Settings</h3>
                    <p>Configure basic application settings.</p>
                </div>
                
                <form method="POST">
                    <div class="form-group">
                        <label for="app_url">Application URL:</label>
                        <input type="url" id="app_url" name="app_url" 
                               value="http://<?php echo $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']); ?>" required>
                        <small>The base URL where SCIMS will be accessible</small>
                    </div>
                    
                    <button type="submit" class="btn">Complete Installation</button>
                </form>
                
            <?php elseif ($step == 5): ?>
                <!-- Step 5: Installation Complete -->
                <div class="success-message">
                    <h2>🎉 Installation Complete!</h2>
                    <p>SCIMS has been successfully installed and configured.</p>
                    
                    <div style="text-align: left; margin: 2rem 0;">
                        <h4>Next Steps:</h4>
                        <ol>
                            <li>Delete this setup.php file for security</li>
                            <li>Access the admin panel to configure events and sports</li>
                            <li>Add departments and set up your first intramural event</li>
                            <li>Configure venues and schedule matches</li>
                        </ol>
                    </div>
                    
                    <a href="admin/" class="btn">Go to Admin Panel</a>
                    <a href="public/" class="btn btn-secondary">View Public Site</a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
