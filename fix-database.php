<?php
/**
 * Fix Database - Create Tables Manually
 * This script will create all required tables step by step
 */

// Define access constant to bypass security check
define('SCIMS_ACCESS', true);

// Database Configuration
$host = 'localhost';
$dbname = 'IMS_db';
$username = 'root';
$password = '';

echo "<h1>SCIMS Database Fix Tool</h1>";

try {
    // Connect to database
    echo "<p>Connecting to database...</p>";
    $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ Connected to database successfully!</p>";
    
    // Create tables one by one
    echo "<h2>Creating Tables...</h2>";
    
    // 1. Admin Users Table
    echo "<p>Creating admin_users table...</p>";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS admin_users (
            admin_id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            password_hash VARCHAR(255) NOT NULL,
            email VARCHAR(100) NOT NULL UNIQUE,
            full_name VARCHAR(100) NOT NULL,
            role ENUM('super_admin', 'admin', 'organizer') DEFAULT 'admin',
            status ENUM('active', 'inactive') DEFAULT 'active',
            last_login TIMESTAMP NULL,
            failed_login_attempts INT DEFAULT 0,
            locked_until TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "<p style='color: green;'>✅ admin_users table created</p>";
    
    // 2. Events Table
    echo "<p>Creating events table...</p>";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS events (
            event_id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            status ENUM('upcoming', 'ongoing', 'completed') DEFAULT 'upcoming',
            description TEXT,
            logo_path VARCHAR(255),
            point_system TEXT,
            created_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "<p style='color: green;'>✅ events table created</p>";
    
    // 3. Departments Table
    echo "<p>Creating departments table...</p>";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS departments (
            dept_id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(50) NOT NULL,
            abbreviation VARCHAR(10) NOT NULL UNIQUE,
            color_code VARCHAR(7) DEFAULT '#000000',
            contact_person VARCHAR(100),
            email VARCHAR(100),
            phone VARCHAR(20),
            logo_path VARCHAR(255),
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ");
    echo "<p style='color: green;'>✅ departments table created</p>";
    
    // 4. Sports Table
    echo "<p>Creating sports table...</p>";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS sports (
            sport_id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(50) NOT NULL,
            category ENUM('team', 'individual', 'performing_arts', 'academic') NOT NULL,
            scoring_type ENUM('points', 'time', 'distance', 'subjective') NOT NULL,
            max_participants INT DEFAULT 1,
            min_participants INT DEFAULT 1,
            description TEXT,
            rules TEXT,
            equipment_needed TEXT,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ");
    echo "<p style='color: green;'>✅ sports table created</p>";
    
    // 5. Venues Table
    echo "<p>Creating venues table...</p>";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS venues (
            venue_id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            location VARCHAR(200),
            capacity INT DEFAULT 0,
            facilities TEXT,
            status ENUM('available', 'maintenance', 'occupied') DEFAULT 'available',
            contact_person VARCHAR(100),
            booking_notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ");
    echo "<p style='color: green;'>✅ venues table created</p>";
    
    // 6. Matches Table
    echo "<p>Creating matches table...</p>";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS matches (
            match_id INT AUTO_INCREMENT PRIMARY KEY,
            sport_id INT NOT NULL,
            venue_id INT,
            event_id INT NOT NULL,
            match_date DATE NOT NULL,
            match_time TIME NOT NULL,
            status ENUM('scheduled', 'ongoing', 'completed', 'cancelled') DEFAULT 'scheduled',
            round_type ENUM('preliminary', 'quarterfinal', 'semifinal', 'final', 'round_robin') DEFAULT 'preliminary',
            match_number VARCHAR(20),
            notes TEXT,
            winner_id INT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "<p style='color: green;'>✅ matches table created</p>";
    
    // Insert default admin user
    echo "<h2>Creating Default Admin User...</h2>";
    $adminExists = $pdo->query("SELECT COUNT(*) FROM admin_users WHERE username = 'admin'")->fetchColumn();
    
    if ($adminExists == 0) {
        $passwordHash = password_hash('Admin123!', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO admin_users (username, password_hash, email, full_name, role) 
            VALUES (?, ?, ?, ?, 'super_admin')
        ");
        $stmt->execute(['admin', $passwordHash, '<EMAIL>', 'System Administrator']);
        echo "<p style='color: green;'>✅ Default admin user created</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ Admin user already exists</p>";
    }
    
    // Insert sample data
    echo "<h2>Inserting Sample Data...</h2>";
    
    // Departments
    $deptCount = $pdo->query("SELECT COUNT(*) FROM departments")->fetchColumn();
    if ($deptCount == 0) {
        $pdo->exec("
            INSERT INTO departments (name, abbreviation, color_code, contact_person, email, phone) VALUES
            ('College of Engineering', 'COE', '#FF6B35', 'Dr. Maria Santos', '<EMAIL>', '***********'),
            ('College of Business Administration', 'CBA', '#004E89', 'Prof. Juan Dela Cruz', '<EMAIL>', '***********'),
            ('College of Arts and Sciences', 'CAS', '#1A936F', 'Dr. Ana Rodriguez', '<EMAIL>', '***********'),
            ('College of Education', 'COED', '#C5282F', 'Prof. Pedro Martinez', '<EMAIL>', '***********'),
            ('College of Information Technology', 'CIT', '#7209B7', 'Dr. Lisa Garcia', '<EMAIL>', '***********')
        ");
        echo "<p style='color: green;'>✅ Sample departments inserted</p>";
    }
    
    // Sports
    $sportsCount = $pdo->query("SELECT COUNT(*) FROM sports")->fetchColumn();
    if ($sportsCount == 0) {
        $pdo->exec("
            INSERT INTO sports (name, category, scoring_type, max_participants, min_participants, description) VALUES
            ('Basketball', 'team', 'points', 12, 5, 'Standard basketball game with 5 players on court'),
            ('Volleyball', 'team', 'points', 12, 6, 'Indoor volleyball with 6 players per team'),
            ('Track and Field - 100m', 'individual', 'time', 1, 1, '100 meter sprint race'),
            ('Chess', 'individual', 'points', 1, 1, 'Individual chess matches'),
            ('Swimming - 50m Freestyle', 'individual', 'time', 1, 1, '50 meter freestyle swimming')
        ");
        echo "<p style='color: green;'>✅ Sample sports inserted</p>";
    }
    
    // Venues
    $venueCount = $pdo->query("SELECT COUNT(*) FROM venues")->fetchColumn();
    if ($venueCount == 0) {
        $pdo->exec("
            INSERT INTO venues (name, location, capacity, facilities, status) VALUES
            ('Main Gymnasium', 'Sports Complex Building A', 2000, 'Basketball court, sound system, scoreboard', 'available'),
            ('Volleyball Court', 'Sports Complex Building B', 500, 'Volleyball net, bleachers', 'available'),
            ('Swimming Pool', 'Aquatic Center', 300, 'Olympic-size pool, timing system', 'available')
        ");
        echo "<p style='color: green;'>✅ Sample venues inserted</p>";
    }
    
    // Events
    $eventCount = $pdo->query("SELECT COUNT(*) FROM events")->fetchColumn();
    if ($eventCount == 0) {
        $pdo->exec("
            INSERT INTO events (name, start_date, end_date, status, description, point_system) VALUES
            ('Samar College Intramurals 2024', '2024-02-15', '2024-02-25', 'upcoming', 'Annual intramural sports competition', '{\"1st\": 15, \"2nd\": 12, \"3rd\": 10, \"4th\": 8, \"5th\": 6, \"participation\": 3}')
        ");
        echo "<p style='color: green;'>✅ Sample event inserted</p>";
    }
    
    echo "<h2 style='color: green;'>🎉 Database Setup Complete!</h2>";
    echo "<p><strong>Summary:</strong></p>";
    echo "<ul>";
    echo "<li>✅ All required tables created</li>";
    echo "<li>✅ Default admin user created</li>";
    echo "<li>✅ Sample data inserted</li>";
    echo "</ul>";
    
    echo "<p><strong>Login Credentials:</strong></p>";
    echo "<ul>";
    echo "<li><strong>Username:</strong> admin</li>";
    echo "<li><strong>Password:</strong> Admin123!</li>";
    echo "</ul>";
    
    echo "<p><strong>Next Steps:</strong></p>";
    echo "<ol>";
    echo "<li><a href='debug-login.php'>🔍 Run Debug Tool Again</a></li>";
    echo "<li><a href='test-db.php'>🗄️ Test Database Connection</a></li>";
    echo "<li><a href='admin/'>🚪 Try Admin Login</a></li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: #f5f5f5;
}
h1, h2 {
    color: #333;
}
p {
    margin: 10px 0;
}
ul, ol {
    margin: 10px 0;
    padding-left: 30px;
}
a {
    color: #007cba;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
</style>
