<?php
/**
 * Database Setup Script for SCIMS
 * This script creates the database and all required tables
 */

// Define access constant
define('SCIMS_ACCESS', true);

// Include configuration
require_once '../includes/config.php';

// Set content type
header('Content-Type: text/html; charset=UTF-8');

echo "<!DOCTYPE html>
<html>
<head>
    <title>SCIMS Database Setup</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
        .step { margin: 10px 0; padding: 10px; border-left: 4px solid #007cba; background: #f9f9f9; }
    </style>
</head>
<body>
    <h1>SCIMS Database Setup</h1>
    <p class='info'>Setting up database and tables for Samar College Intramurals Management System...</p>";

try {
    echo "<div class='step'><strong>Step 1:</strong> Connecting to database...</div>";
    
    // Test database connection
    $db = getDB();
    echo "<p class='success'>✓ Database connection successful!</p>";
    
    echo "<div class='step'><strong>Step 2:</strong> Creating tables...</div>";
    
    // Create admin_users table
    $sql = "CREATE TABLE IF NOT EXISTS admin_users (
        admin_id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        full_name VARCHAR(100) NOT NULL,
        role ENUM('super_admin', 'admin', 'organizer') DEFAULT 'admin',
        status ENUM('active', 'inactive') DEFAULT 'active',
        last_login DATETIME NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($sql);
    echo "<p class='success'>✓ admin_users table created</p>";
    
    // Create events table
    $sql = "CREATE TABLE IF NOT EXISTS events (
        event_id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        start_date DATE NOT NULL,
        end_date DATE NOT NULL,
        status ENUM('upcoming', 'ongoing', 'completed') DEFAULT 'upcoming',
        created_by INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (created_by) REFERENCES admin_users(admin_id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($sql);
    echo "<p class='success'>✓ events table created</p>";
    
    // Create departments table
    $sql = "CREATE TABLE IF NOT EXISTS departments (
        dept_id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        abbreviation VARCHAR(10) NOT NULL,
        color_code VARCHAR(7) DEFAULT '#007cba',
        description TEXT,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($sql);
    echo "<p class='success'>✓ departments table created</p>";
    
    // Create sports table
    $sql = "CREATE TABLE IF NOT EXISTS sports (
        sport_id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        category ENUM('individual', 'team', 'mixed') NOT NULL,
        scoring_type ENUM('points', 'time', 'distance', 'subjective') DEFAULT 'points',
        description TEXT,
        rules TEXT,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($sql);
    echo "<p class='success'>✓ sports table created</p>";
    
    // Create venues table
    $sql = "CREATE TABLE IF NOT EXISTS venues (
        venue_id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        location VARCHAR(200),
        capacity INT DEFAULT 0,
        description TEXT,
        status ENUM('available', 'maintenance', 'unavailable') DEFAULT 'available',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($sql);
    echo "<p class='success'>✓ venues table created</p>";
    
    // Create matches table
    $sql = "CREATE TABLE IF NOT EXISTS matches (
        match_id INT AUTO_INCREMENT PRIMARY KEY,
        event_id INT NOT NULL,
        sport_id INT NOT NULL,
        venue_id INT NULL,
        match_date DATE NOT NULL,
        match_time TIME NOT NULL,
        round_type ENUM('preliminary', 'quarterfinal', 'semifinal', 'final', 'round_robin') DEFAULT 'preliminary',
        match_number VARCHAR(20),
        status ENUM('scheduled', 'ongoing', 'completed', 'cancelled') DEFAULT 'scheduled',
        winner_id INT NULL,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (event_id) REFERENCES events(event_id) ON DELETE CASCADE,
        FOREIGN KEY (sport_id) REFERENCES sports(sport_id) ON DELETE CASCADE,
        FOREIGN KEY (venue_id) REFERENCES venues(venue_id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($sql);
    echo "<p class='success'>✓ matches table created</p>";
    
    // Create match_participants table
    $sql = "CREATE TABLE IF NOT EXISTS match_participants (
        participant_id INT AUTO_INCREMENT PRIMARY KEY,
        match_id INT NOT NULL,
        dept_id INT NOT NULL,
        participant_name VARCHAR(100) NOT NULL,
        participant_type ENUM('individual', 'team') DEFAULT 'individual',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (match_id) REFERENCES matches(match_id) ON DELETE CASCADE,
        FOREIGN KEY (dept_id) REFERENCES departments(dept_id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($sql);
    echo "<p class='success'>✓ match_participants table created</p>";
    
    // Create scores table
    $sql = "CREATE TABLE IF NOT EXISTS scores (
        score_id INT AUTO_INCREMENT PRIMARY KEY,
        match_id INT NOT NULL,
        participant_id INT NOT NULL,
        points DECIMAL(10,2) DEFAULT 0,
        time_score TIME NULL,
        distance_score DECIMAL(10,2) NULL,
        position INT NULL,
        is_final BOOLEAN DEFAULT FALSE,
        recorded_by INT NULL,
        notes TEXT,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (match_id) REFERENCES matches(match_id) ON DELETE CASCADE,
        FOREIGN KEY (participant_id) REFERENCES match_participants(participant_id) ON DELETE CASCADE,
        FOREIGN KEY (recorded_by) REFERENCES admin_users(admin_id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($sql);
    echo "<p class='success'>✓ scores table created</p>";
    
    // Create department_standings table
    $sql = "CREATE TABLE IF NOT EXISTS department_standings (
        standing_id INT AUTO_INCREMENT PRIMARY KEY,
        event_id INT NOT NULL,
        dept_id INT NOT NULL,
        total_points DECIMAL(10,2) DEFAULT 0,
        medals_gold INT DEFAULT 0,
        medals_silver INT DEFAULT 0,
        medals_bronze INT DEFAULT 0,
        matches_played INT DEFAULT 0,
        matches_won INT DEFAULT 0,
        matches_lost INT DEFAULT 0,
        matches_drawn INT DEFAULT 0,
        rank_position INT DEFAULT 0,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (event_id) REFERENCES events(event_id) ON DELETE CASCADE,
        FOREIGN KEY (dept_id) REFERENCES departments(dept_id) ON DELETE CASCADE,
        UNIQUE KEY unique_event_dept (event_id, dept_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($sql);
    echo "<p class='success'>✓ department_standings table created</p>";

    // Create event_sports table (many-to-many relationship)
    $sql = "CREATE TABLE IF NOT EXISTS event_sports (
        event_sport_id INT AUTO_INCREMENT PRIMARY KEY,
        event_id INT NOT NULL,
        sport_id INT NOT NULL,
        max_teams_per_dept INT DEFAULT 1,
        registration_deadline DATE NULL,
        status ENUM('open', 'closed', 'completed') DEFAULT 'open',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (event_id) REFERENCES events(event_id) ON DELETE CASCADE,
        FOREIGN KEY (sport_id) REFERENCES sports(sport_id) ON DELETE CASCADE,
        UNIQUE KEY unique_event_sport (event_id, sport_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $db->query($sql);
    echo "<p class='success'>✓ event_sports table created</p>";

    // Create system_settings table
    $sql = "CREATE TABLE IF NOT EXISTS system_settings (
        setting_id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $db->query($sql);
    echo "<p class='success'>✓ system_settings table created</p>";

    echo "<div class='step'><strong>Step 3:</strong> Inserting sample data...</div>";
    
    // Insert default admin user
    $adminExists = fetchOne("SELECT admin_id FROM admin_users WHERE username = 'admin'");
    if (!$adminExists) {
        $adminData = [
            'username' => 'admin',
            'email' => '<EMAIL>',
            'password_hash' => password_hash('Admin123!', PASSWORD_DEFAULT),
            'full_name' => 'System Administrator',
            'role' => 'super_admin'
        ];
        insertRecord('admin_users', $adminData);
        echo "<p class='success'>✓ Default admin user created (username: admin, password: Admin123!)</p>";
    } else {
        echo "<p class='warning'>⚠ Admin user already exists</p>";
    }
    
    // Insert sample departments
    $deptExists = fetchOne("SELECT dept_id FROM departments LIMIT 1");
    if (!$deptExists) {
        $departments = [
            ['name' => 'College of Engineering', 'abbreviation' => 'COE', 'color_code' => '#FF6B6B'],
            ['name' => 'College of Education', 'abbreviation' => 'COED', 'color_code' => '#4ECDC4'],
            ['name' => 'College of Business Administration', 'abbreviation' => 'CBA', 'color_code' => '#45B7D1'],
            ['name' => 'College of Arts and Sciences', 'abbreviation' => 'CAS', 'color_code' => '#96CEB4'],
            ['name' => 'College of Computer Studies', 'abbreviation' => 'CCS', 'color_code' => '#FFEAA7']
        ];
        
        foreach ($departments as $dept) {
            insertRecord('departments', $dept);
        }
        echo "<p class='success'>✓ Sample departments created</p>";
    } else {
        echo "<p class='warning'>⚠ Departments already exist</p>";
    }
    
    // Insert sample sports
    $sportExists = fetchOne("SELECT sport_id FROM sports LIMIT 1");
    if (!$sportExists) {
        $sports = [
            ['name' => 'Basketball', 'category' => 'team', 'scoring_type' => 'points'],
            ['name' => 'Volleyball', 'category' => 'team', 'scoring_type' => 'points'],
            ['name' => '100m Sprint', 'category' => 'individual', 'scoring_type' => 'time'],
            ['name' => 'Chess', 'category' => 'individual', 'scoring_type' => 'points'],
            ['name' => 'Badminton', 'category' => 'individual', 'scoring_type' => 'points']
        ];
        
        foreach ($sports as $sport) {
            insertRecord('sports', $sport);
        }
        echo "<p class='success'>✓ Sample sports created</p>";
    } else {
        echo "<p class='warning'>⚠ Sports already exist</p>";
    }
    
    echo "<div class='step'><strong>Setup Complete!</strong></div>";
    echo "<p class='success'>✓ Database setup completed successfully!</p>";
    echo "<p class='info'>You can now access the admin panel at: <a href='../admin/'>Admin Panel</a></p>";
    echo "<p class='info'>Login credentials: <strong>Username:</strong> admin, <strong>Password:</strong> Admin123!</p>";
    
} catch (Exception $e) {
    echo "<p class='error'>✗ Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "</body></html>";
?>
