<?php
/**
 * Admin Header Include
 */
if (!defined('SCIMS_ACCESS')) {
    die('Direct access not permitted');
}

$currentUser = getCurrentUser();

// Determine the base path for navigation links based on directory depth
$basePath = '';
$currentPath = $_SERVER['PHP_SELF'];

// Calculate how many levels deep we are from the admin root
$adminPos = strpos($currentPath, '/admin/');
if ($adminPos !== false) {
    $pathAfterAdmin = substr($currentPath, $adminPos + 7); // 7 = length of '/admin/'
    $depth = substr_count($pathAfterAdmin, '/');

    // Generate the appropriate number of '../' based on depth
    if ($depth > 0) {
        $basePath = str_repeat('../', $depth);
    }
}
?>
<header class="admin-header">
    <div class="header-left">
        <button class="menu-toggle" id="mobileMenuToggle">
            ☰
        </button>
        <h1 class="header-title">
            <?php 
            $pageTitle = '';
            $currentPage = basename($_SERVER['PHP_SELF'], '.php');
            
            switch($currentPage) {
                case 'dashboard': $pageTitle = 'Dashboard'; break;
                case 'events': $pageTitle = 'Events'; break;
                case 'departments': $pageTitle = 'Departments'; break;
                case 'matches': $pageTitle = 'Matches'; break;
                case 'reports': $pageTitle = 'Reports'; break;
                default: $pageTitle = 'Admin Panel';
            }
            
            echo $pageTitle;
            ?>
        </h1>
    </div>
    
    <div class="header-right">
        <div class="connection-status online" id="connectionStatus">
            Online
        </div>
        
        <div class="user-menu">
            <button class="user-menu-toggle" id="userMenuToggle">
                <div class="user-avatar">
                    <?php echo strtoupper(substr($currentUser['full_name'], 0, 1)); ?>
                </div>
                <span class="user-name"><?php echo htmlspecialchars($currentUser['full_name']); ?></span>
                <i class="icon-chevron-down"></i>
            </button>
            
            <div class="user-menu-dropdown" id="userMenuDropdown">
                <a href="<?php echo $basePath; ?>profile.php" class="user-menu-item">
                    <i class="icon-user"></i>
                    Profile Settings
                </a>
                <a href="<?php echo $basePath; ?>change-password.php" class="user-menu-item">
                    <i class="icon-lock"></i>
                    Change Password
                </a>
                <div class="user-menu-divider"></div>
                <a href="<?php echo $basePath; ?>logout.php" class="user-menu-item">
                    <i class="icon-logout"></i>
                    Sign Out
                </a>
            </div>
        </div>
    </div>
</header>
