<?php
/**
 * Database Connection Test
 */

define('SCIMS_ACCESS', true);
require_once 'includes/config.php';

echo "<h1>SCIMS Database Connection Test</h1>";

try {
    $db = getDB();
    echo "<p style='color: green;'>✅ Database connection successful!</p>";
    
    // Test if database exists
    $dbName = DB_NAME;
    $result = $db->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '$dbName'");
    if ($result->rowCount() > 0) {
        echo "<p style='color: green;'>✅ Database '$dbName' exists!</p>";
        
        // Check for tables
        $tables = [
            'admin_users',
            'events', 
            'departments',
            'sports',
            'venues',
            'matches'
        ];
        
        echo "<h2>Table Status:</h2>";
        foreach ($tables as $table) {
            if (tableExists($table)) {
                $count = fetchOne("SELECT COUNT(*) as count FROM $table")['count'];
                echo "<p style='color: green;'>✅ Table '$table' exists with $count records</p>";
            } else {
                echo "<p style='color: red;'>❌ Table '$table' does not exist</p>";
            }
        }
        
    } else {
        echo "<p style='color: red;'>❌ Database '$dbName' does not exist!</p>";
        echo "<p>Please run the setup script first: <a href='setup.php'>setup.php</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration in includes/config.php</p>";
}

echo "<hr>";
echo "<h2>Configuration:</h2>";
echo "<p><strong>Host:</strong> " . DB_HOST . "</p>";
echo "<p><strong>Database:</strong> " . DB_NAME . "</p>";
echo "<p><strong>User:</strong> " . DB_USER . "</p>";
echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";

echo "<hr>";
echo "<p><a href='setup.php'>Run Setup Script</a> | <a href='admin/'>Go to Admin</a></p>";
?>
