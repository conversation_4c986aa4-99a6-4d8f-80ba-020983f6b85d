<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Venues Management - Edit Venue
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication
requireAuth();

$venueId = (int)($_GET['id'] ?? 0);
$message = '';
$messageType = 'info';

if (!$venueId) {
    header('Location: index.php?message=Invalid venue ID&type=error');
    exit;
}

// Get venue details
$venue = fetchOne("SELECT * FROM venues WHERE venue_id = ?", [$venueId]);

if (!$venue) {
    header('Location: index.php?message=Venue not found&type=error');
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('Invalid security token');
        }
        
        $name = sanitizeInput($_POST['name'] ?? '');
        $location = sanitizeInput($_POST['location'] ?? '');
        $capacity = (int)($_POST['capacity'] ?? 0);
        $description = sanitizeInput($_POST['description'] ?? '');
        $status = sanitizeInput($_POST['status'] ?? 'available');
        
        // Validation
        if (empty($name)) {
            throw new Exception('Venue name is required');
        }
        
        if (!in_array($status, ['available', 'maintenance', 'unavailable'])) {
            throw new Exception('Invalid status selected');
        }
        
        // Check if venue name already exists (excluding current venue)
        $existing = fetchOne("SELECT venue_id FROM venues WHERE name = ? AND venue_id != ?", [$name, $venueId]);
        if ($existing) {
            throw new Exception('A venue with this name already exists');
        }
        
        // Update venue
        $venueData = [
            'name' => $name,
            'location' => $location,
            'capacity' => $capacity,
            'description' => $description,
            'status' => $status
        ];
        
        updateRecord('venues', $venueData, 'venue_id = :venue_id', ['venue_id' => $venueId]);
        
        logActivity('venue_updated', "Venue '{$name}' updated (ID: {$venueId})");
        
        // Redirect to venue details
        header('Location: view.php?id=' . $venueId . '&message=Venue updated successfully&type=success');
        exit;
        
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit <?php echo htmlspecialchars($venue['name']); ?> - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>
    
    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1>Edit Venue</h1>
                <nav class="breadcrumb">
                    <a href="../dashboard.php">Dashboard</a>
                    <span>/</span>
                    <a href="index.php">Venues</a>
                    <span>/</span>
                    <a href="view.php?id=<?php echo $venue['venue_id']; ?>"><?php echo htmlspecialchars($venue['name']); ?></a>
                    <span>/</span>
                    <span>Edit</span>
                </nav>
            </div>
            <div class="page-actions">
                <a href="view.php?id=<?php echo $venue['venue_id']; ?>" class="btn btn-outline">
                    <i class="icon-arrow-left"></i>
                    Back to Details
                </a>
            </div>
        </div>
        
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <div class="form-container">
            <form method="POST" class="form-card">
                <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                
                <div class="form-section">
                    <h3>Venue Information</h3>
                    
                    <div class="form-group">
                        <label for="name" class="form-label required">Venue Name</label>
                        <input type="text" id="name" name="name" class="form-control" 
                               value="<?php echo htmlspecialchars($venue['name']); ?>" required>
                        <small class="form-help">Enter the name of the venue</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="location" class="form-label">Location</label>
                        <input type="text" id="location" name="location" class="form-control" 
                               value="<?php echo htmlspecialchars($venue['location']); ?>" 
                               placeholder="Building, floor, room number, etc.">
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="capacity" class="form-label">Capacity</label>
                            <input type="number" id="capacity" name="capacity" class="form-control" 
                                   value="<?php echo $venue['capacity']; ?>" 
                                   min="0" placeholder="Maximum number of people">
                        </div>
                        
                        <div class="form-group">
                            <label for="status" class="form-label">Status</label>
                            <select id="status" name="status" class="form-control">
                                <option value="available" <?php echo $venue['status'] === 'available' ? 'selected' : ''; ?>>Available</option>
                                <option value="maintenance" <?php echo $venue['status'] === 'maintenance' ? 'selected' : ''; ?>>Under Maintenance</option>
                                <option value="unavailable" <?php echo $venue['status'] === 'unavailable' ? 'selected' : ''; ?>>Unavailable</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="description" class="form-label">Description</label>
                        <textarea id="description" name="description" class="form-control" rows="4" 
                                  placeholder="Additional details about the venue, facilities, equipment, etc."><?php echo htmlspecialchars($venue['description']); ?></textarea>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="icon-check"></i>
                        Update Venue
                    </button>
                    <a href="view.php?id=<?php echo $venue['venue_id']; ?>" class="btn btn-outline">Cancel</a>
                </div>
            </form>
        </div>
    </main>
    
    <script src="../../assets/js/admin.js"></script>
    <script>
        // Form validation
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const nameInput = document.getElementById('name');
            
            form.addEventListener('submit', function(e) {
                let isValid = true;
                
                // Validate name
                if (!nameInput.value.trim()) {
                    showFieldError(nameInput, 'Venue name is required');
                    isValid = false;
                } else {
                    clearFieldError(nameInput);
                }
                
                if (!isValid) {
                    e.preventDefault();
                }
            });
            
            function showFieldError(field, message) {
                clearFieldError(field);
                field.classList.add('error');
                const errorDiv = document.createElement('div');
                errorDiv.className = 'field-error';
                errorDiv.textContent = message;
                field.parentNode.appendChild(errorDiv);
            }
            
            function clearFieldError(field) {
                field.classList.remove('error');
                const existingError = field.parentNode.querySelector('.field-error');
                if (existingError) {
                    existingError.remove();
                }
            }
        });
    </script>
</body>
</html>
