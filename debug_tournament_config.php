<?php
// Debug version to identify the exact issue
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Debug Tournament Config</h1>";

echo "<h2>Step 1: Basic PHP Test</h2>";
echo "<p>✅ PHP is working</p>";

echo "<h2>Step 2: File Includes Test</h2>";

try {
    echo "<p>Testing config.php include...</p>";
    if (file_exists('../../includes/config.php')) {
        echo "<p>✅ config.php file exists</p>";
        define('SCIMS_ACCESS', true);
        require_once '../../includes/config.php';
        echo "<p>✅ config.php loaded successfully</p>";
        echo "<p>APP_NAME: " . (defined('APP_NAME') ? APP_NAME : 'Not defined') . "</p>";
    } else {
        echo "<p>❌ config.php file not found</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Error loading config.php: " . $e->getMessage() . "</p>";
}

try {
    echo "<p>Testing functions.php include...</p>";
    if (file_exists('../../includes/functions.php')) {
        echo "<p>✅ functions.php file exists</p>";
        require_once '../../includes/functions.php';
        echo "<p>✅ functions.php loaded successfully</p>";
        
        // Test if fetchAll function exists
        if (function_exists('fetchAll')) {
            echo "<p>✅ fetchAll function is available</p>";
        } else {
            echo "<p>❌ fetchAll function not found</p>";
        }
    } else {
        echo "<p>❌ functions.php file not found</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Error loading functions.php: " . $e->getMessage() . "</p>";
}

echo "<h2>Step 3: Database Connection Test</h2>";

try {
    if (isset($pdo)) {
        echo "<p>✅ PDO connection exists</p>";
        
        // Test a simple query
        $testQuery = $pdo->query("SELECT 1 as test");
        if ($testQuery) {
            echo "<p>✅ Database connection working</p>";
        } else {
            echo "<p>❌ Database query failed</p>";
        }
    } else {
        echo "<p>❌ PDO connection not found</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Database error: " . $e->getMessage() . "</p>";
}

echo "<h2>Step 4: Mock Data Test</h2>";

// Create mock data similar to the tournament config
$eventSportId = 1;
$eventId = 1;

$eventSport = [
    'sport_name' => 'Basketball',
    'sport_category' => 'team',
    'scoring_type' => 'points',
    'event_name' => 'Annual Sports Festival 2024'
];

$participants = [
    [
        'participant_id' => 1,
        'participant_name' => 'Team Alpha',
        'dept_id' => 1,
        'dept_name' => 'Computer Science',
        'dept_abbr' => 'CS'
    ],
    [
        'participant_id' => 2,
        'participant_name' => 'Team Beta',
        'dept_id' => 2,
        'dept_name' => 'Information Technology',
        'dept_abbr' => 'IT'
    ]
];

echo "<p>✅ Mock data created</p>";
echo "<p>Event Sport: " . $eventSport['sport_name'] . "</p>";
echo "<p>Participants: " . count($participants) . "</p>";

echo "<h2>Step 5: Database Query Test</h2>";

if (isset($pdo) && function_exists('fetchAll')) {
    try {
        // Test the actual query from the tournament config
        echo "<p>Testing event_sports query...</p>";
        $testEventSport = fetchAll("
            SELECT es.*, s.name as sport_name, s.category as sport_category, s.scoring_type,
                   e.name as event_name, e.start_date, e.end_date
            FROM event_sports es
            INNER JOIN sports s ON es.sport_id = s.sport_id
            INNER JOIN events e ON es.event_id = e.event_id
            WHERE es.event_sport_id = ? AND es.event_id = ?
            LIMIT 1
        ", [$eventSportId, $eventId]);
        
        if ($testEventSport) {
            echo "<p>✅ Event sports query successful</p>";
            echo "<p>Found " . count($testEventSport) . " records</p>";
        } else {
            echo "<p>⚠️ Event sports query returned no results (this is expected if no data exists)</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Event sports query error: " . $e->getMessage() . "</p>";
    }
    
    try {
        echo "<p>Testing participants query...</p>";
        $testParticipants = fetchAll("
            SELECT DISTINCT mp.participant_id, mp.participant_name, mp.dept_id,
                   d.name as dept_name, d.abbreviation as dept_abbr
            FROM match_participants mp
            INNER JOIN departments d ON mp.dept_id = d.dept_id
            INNER JOIN matches m ON mp.match_id = m.match_id
            INNER JOIN event_sports es ON m.event_id = es.event_id AND m.sport_id = es.sport_id
            WHERE es.event_sport_id = ?
            ORDER BY mp.participant_name
            LIMIT 5
        ", [$eventSportId]);
        
        if ($testParticipants) {
            echo "<p>✅ Participants query successful</p>";
            echo "<p>Found " . count($testParticipants) . " participants</p>";
        } else {
            echo "<p>⚠️ Participants query returned no results (this is expected if no data exists)</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Participants query error: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p>❌ Cannot test database queries - PDO or fetchAll not available</p>";
}

echo "<h2>Step 6: Simple HTML Test</h2>";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-card { background: #f8f9fa; padding: 20px; margin: 10px 0; border-radius: 8px; border: 1px solid #dee2e6; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="test-card">
        <h3>HTML Rendering Test</h3>
        <p class="success">✅ HTML is rendering correctly</p>
        <p>Current time: <?php echo date('Y-m-d H:i:s'); ?></p>
        <p>PHP version: <?php echo PHP_VERSION; ?></p>
    </div>
    
    <div class="test-card">
        <h3>Tournament Format Test</h3>
        <?php
        $formats = [
            'single_elimination' => ['name' => 'Single Elimination', 'icon' => '🏆'],
            'double_elimination' => ['name' => 'Double Elimination', 'icon' => '🔄'],
            'round_robin' => ['name' => 'Round Robin', 'icon' => '🔁']
        ];
        
        foreach ($formats as $key => $format) {
            echo "<p>" . $format['icon'] . " " . $format['name'] . "</p>";
        }
        ?>
    </div>
    
    <div class="test-card">
        <h3>JavaScript Test</h3>
        <button onclick="testJS()">Test JavaScript</button>
        <p id="jsResult"></p>
    </div>
    
    <script>
        function testJS() {
            document.getElementById('jsResult').innerHTML = '<span style="color: #28a745;">✅ JavaScript is working</span>';
        }
        
        console.log('Debug page loaded successfully');
    </script>
</body>
</html>

<?php
echo "<h2>Debug Complete</h2>";
echo "<p>If you can see this message, PHP is working correctly.</p>";
?>
