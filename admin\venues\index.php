<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Venues Management - Main Page
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication
requireAuth();

// Handle actions
$action = $_GET['action'] ?? '';
$message = '';
$messageType = 'info';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('Invalid security token');
        }
        
        switch ($action) {
            case 'delete':
                $venueId = (int)($_POST['venue_id'] ?? 0);
                if ($venueId) {
                    // Check if venue has scheduled matches
                    $matchCount = fetchOne("SELECT COUNT(*) as count FROM matches WHERE venue_id = ? AND status IN ('scheduled', 'ongoing')", [$venueId])['count'];
                    if ($matchCount > 0) {
                        throw new Exception('Cannot delete venue with scheduled or ongoing matches');
                    }
                    
                    deleteRecord('venues', 'venue_id = :venue_id', ['venue_id' => $venueId]);
                    logActivity('venue_deleted', "Venue ID {$venueId} deleted");
                    $message = 'Venue deleted successfully';
                    $messageType = 'success';
                }
                break;
                
            case 'update_status':
                $venueId = (int)($_POST['venue_id'] ?? 0);
                $newStatus = sanitizeInput($_POST['new_status'] ?? '');
                
                if ($venueId && in_array($newStatus, ['available', 'maintenance', 'occupied'])) {
                    updateRecord('venues', ['status' => $newStatus], 'venue_id = :venue_id', ['venue_id' => $venueId]);
                    logActivity('venue_status_changed', "Venue ID {$venueId} status changed to {$newStatus}");
                    $message = 'Venue status updated successfully';
                    $messageType = 'success';
                }
                break;
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get venues with pagination
$page = (int)($_GET['page'] ?? 1);
$search = sanitizeInput($_GET['search'] ?? '');
$statusFilter = sanitizeInput($_GET['status'] ?? '');

$whereConditions = [];
$params = [];

if ($search) {
    $whereConditions[] = "(name LIKE ? OR location LIKE ? OR contact_person LIKE ?)";
    $params[] = "%{$search}%";
    $params[] = "%{$search}%";
    $params[] = "%{$search}%";
}

if ($statusFilter) {
    $whereConditions[] = "status = ?";
    $params[] = $statusFilter;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// Count total records
$totalRecords = fetchOne("SELECT COUNT(*) as count FROM venues {$whereClause}", $params)['count'];
$pagination = paginate($totalRecords, $page);

// Get venues with match statistics
$venues = fetchAll("
    SELECT v.*,
           (SELECT COUNT(*) FROM matches WHERE venue_id = v.venue_id) as total_matches,
           (SELECT COUNT(*) FROM matches WHERE venue_id = v.venue_id AND status = 'scheduled') as scheduled_matches,
           (SELECT COUNT(*) FROM matches WHERE venue_id = v.venue_id AND status = 'ongoing') as ongoing_matches,
           (SELECT COUNT(*) FROM matches WHERE venue_id = v.venue_id AND match_date = CURDATE()) as today_matches
    FROM venues v
    {$whereClause}
    ORDER BY v.name ASC
    LIMIT {$pagination['records_per_page']} OFFSET {$pagination['offset']}
", $params);

$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Venues Management - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>
    
    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1>Venues Management</h1>
                <nav class="breadcrumb">
                    <a href="../dashboard.php">Dashboard</a>
                    <span>/</span>
                    <span>Venues</span>
                </nav>
            </div>
            <div class="page-actions">
                <a href="create.php" class="btn btn-primary">
                    <i class="icon-plus"></i>
                    Add Venue
                </a>
                <a href="calendar.php" class="btn btn-secondary">
                    <i class="icon-calendar"></i>
                    View Calendar
                </a>
            </div>
        </div>
        
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <!-- Filters and Search -->
        <div class="filters-container">
            <form method="GET" class="filters-form">
                <div class="filter-group">
                    <input type="text" name="search" placeholder="Search venues..." 
                           value="<?php echo htmlspecialchars($search); ?>" class="form-control">
                </div>
                
                <div class="filter-group">
                    <select name="status" class="form-control">
                        <option value="">All Status</option>
                        <option value="available" <?php echo $statusFilter === 'available' ? 'selected' : ''; ?>>Available</option>
                        <option value="maintenance" <?php echo $statusFilter === 'maintenance' ? 'selected' : ''; ?>>Maintenance</option>
                        <option value="occupied" <?php echo $statusFilter === 'occupied' ? 'selected' : ''; ?>>Occupied</option>
                    </select>
                </div>
                
                <div class="filter-actions">
                    <button type="submit" class="btn btn-secondary">Filter</button>
                    <a href="index.php" class="btn btn-outline">Clear</a>
                </div>
            </form>
        </div>
        
        <!-- Venues Grid -->
        <div class="venues-container">
            <?php if (!empty($venues)): ?>
                <div class="venues-grid">
                    <?php foreach ($venues as $venue): ?>
                        <div class="venue-card">
                            <div class="venue-header">
                                <div class="venue-status">
                                    <span class="status-badge status-<?php echo $venue['status']; ?>">
                                        <?php echo ucfirst($venue['status']); ?>
                                    </span>
                                    <?php if ($venue['ongoing_matches'] > 0): ?>
                                        <span class="live-indicator">🔴 Live</span>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="venue-actions-quick">
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                                        <input type="hidden" name="action" value="update_status">
                                        <input type="hidden" name="venue_id" value="<?php echo $venue['venue_id']; ?>">
                                        
                                        <select name="new_status" onchange="this.form.submit()" class="status-select">
                                            <option value="available" <?php echo $venue['status'] === 'available' ? 'selected' : ''; ?>>Available</option>
                                            <option value="maintenance" <?php echo $venue['status'] === 'maintenance' ? 'selected' : ''; ?>>Maintenance</option>
                                            <option value="occupied" <?php echo $venue['status'] === 'occupied' ? 'selected' : ''; ?>>Occupied</option>
                                        </select>
                                    </form>
                                </div>
                            </div>
                            
                            <div class="venue-content">
                                <h3><?php echo htmlspecialchars($venue['name']); ?></h3>
                                
                                <div class="venue-location">
                                    <i class="icon-location"></i>
                                    <?php echo htmlspecialchars($venue['location'] ?? 'Location not specified'); ?>
                                </div>
                                
                                <div class="venue-capacity">
                                    <i class="icon-users"></i>
                                    Capacity: <?php echo number_format($venue['capacity']); ?>
                                </div>
                                
                                <?php if ($venue['facilities']): ?>
                                    <div class="venue-facilities">
                                        <strong>Facilities:</strong>
                                        <p><?php echo htmlspecialchars(substr($venue['facilities'], 0, 100)); ?>...</p>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="venue-stats">
                                    <div class="stat-row">
                                        <div class="stat-item">
                                            <span class="stat-value"><?php echo $venue['today_matches']; ?></span>
                                            <span class="stat-label">Today</span>
                                        </div>
                                        <div class="stat-item">
                                            <span class="stat-value"><?php echo $venue['scheduled_matches']; ?></span>
                                            <span class="stat-label">Scheduled</span>
                                        </div>
                                        <div class="stat-item">
                                            <span class="stat-value"><?php echo $venue['total_matches']; ?></span>
                                            <span class="stat-label">Total</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <?php if ($venue['contact_person']): ?>
                                    <div class="venue-contact">
                                        <strong>Contact:</strong> <?php echo htmlspecialchars($venue['contact_person']); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="venue-actions">
                                <a href="view.php?id=<?php echo $venue['venue_id']; ?>" 
                                   class="btn btn-xs btn-outline" title="View Details">
                                    <i class="icon-eye"></i>
                                </a>
                                <a href="edit.php?id=<?php echo $venue['venue_id']; ?>" 
                                   class="btn btn-xs btn-primary" title="Edit Venue">
                                    <i class="icon-edit"></i>
                                </a>
                                <a href="schedule.php?venue_id=<?php echo $venue['venue_id']; ?>" 
                                   class="btn btn-xs btn-secondary" title="View Schedule">
                                    <i class="icon-calendar"></i>
                                </a>
                                
                                <?php if ($venue['scheduled_matches'] == 0 && $venue['ongoing_matches'] == 0): ?>
                                    <button class="btn btn-xs btn-danger" 
                                            onclick="deleteVenue(<?php echo $venue['venue_id']; ?>, '<?php echo htmlspecialchars($venue['name']); ?>')"
                                            title="Delete Venue">
                                        <i class="icon-trash"></i>
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Pagination -->
                <?php echo generatePaginationHTML($pagination, 'index.php'); ?>
                
            <?php else: ?>
                <div class="no-data">
                    <h3>No venues found</h3>
                    <p>Start by adding your first venue.</p>
                    <a href="create.php" class="btn btn-primary">Add Venue</a>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Quick Stats Summary -->
        <?php if (!empty($venues)): ?>
            <div class="venues-summary">
                <div class="summary-cards">
                    <div class="summary-card">
                        <h4>Available</h4>
                        <span class="summary-count">
                            <?php echo count(array_filter($venues, fn($v) => $v['status'] === 'available')); ?>
                        </span>
                    </div>
                    <div class="summary-card">
                        <h4>In Use</h4>
                        <span class="summary-count">
                            <?php echo count(array_filter($venues, fn($v) => $v['ongoing_matches'] > 0)); ?>
                        </span>
                    </div>
                    <div class="summary-card">
                        <h4>Maintenance</h4>
                        <span class="summary-count">
                            <?php echo count(array_filter($venues, fn($v) => $v['status'] === 'maintenance')); ?>
                        </span>
                    </div>
                    <div class="summary-card">
                        <h4>Total Capacity</h4>
                        <span class="summary-count">
                            <?php echo number_format(array_sum(array_column($venues, 'capacity'))); ?>
                        </span>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </main>
    
    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal">
        <div class="modal-overlay">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Confirm Delete</h3>
                    <button class="modal-close" onclick="closeModal('deleteModal')">&times;</button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the venue "<span id="venueName"></span>"?</p>
                    <p class="text-warning">This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <form id="deleteForm" method="POST">
                        <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="venue_id" id="deleteVenueId">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('deleteModal')">Cancel</button>
                        <button type="submit" class="btn btn-danger">Delete Venue</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <script src="../../assets/js/admin.js"></script>
    <script>
        function deleteVenue(venueId, venueName) {
            document.getElementById('venueName').textContent = venueName;
            document.getElementById('deleteVenueId').value = venueId;
            openModal('deleteModal');
        }
        
        function openModal(modalId) {
            document.getElementById(modalId).classList.add('show');
        }
        
        function closeModal(modalId) {
            document.getElementById(modalId).classList.remove('show');
        }
        
        // Auto-refresh for live updates
        setInterval(function() {
            if (!document.hidden) {
                // Only refresh if there are ongoing matches
                const liveIndicators = document.querySelectorAll('.live-indicator');
                if (liveIndicators.length > 0) {
                    location.reload();
                }
            }
        }, 30000); // 30 seconds
    </script>
</body>
</html>
