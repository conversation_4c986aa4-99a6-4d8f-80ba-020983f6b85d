<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Venues Management - Calendar View
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';

// Require authentication
requireAuth();

// Get current month and year
$currentMonth = (int)($_GET['month'] ?? date('n'));
$currentYear = (int)($_GET['year'] ?? date('Y'));

// Validate month and year
if ($currentMonth < 1 || $currentMonth > 12) {
    $currentMonth = date('n');
}
if ($currentYear < 2020 || $currentYear > 2030) {
    $currentYear = date('Y');
}

// Calculate calendar dates
$firstDay = mktime(0, 0, 0, $currentMonth, 1, $currentYear);
$lastDay = mktime(0, 0, 0, $currentMonth + 1, 0, $currentYear);
$daysInMonth = date('t', $firstDay);
$startDayOfWeek = date('w', $firstDay);

// Get all venues
$venues = fetchAll("SELECT venue_id, name, status FROM venues ORDER BY name");

// Get matches for the current month
$startDate = date('Y-m-01', $firstDay);
$endDate = date('Y-m-t', $firstDay);

$matches = fetchAll("
    SELECT m.match_date, m.match_time, m.venue_id, m.status,
           s.name as sport_name, v.name as venue_name,
           COUNT(*) as match_count
    FROM matches m
    JOIN sports s ON m.sport_id = s.sport_id
    JOIN venues v ON m.venue_id = v.venue_id
    WHERE m.match_date BETWEEN ? AND ?
    GROUP BY m.match_date, m.venue_id
    ORDER BY m.match_date, m.match_time
", [$startDate, $endDate]);

// Group matches by date
$matchesByDate = [];
foreach ($matches as $match) {
    $date = $match['match_date'];
    if (!isset($matchesByDate[$date])) {
        $matchesByDate[$date] = [];
    }
    $matchesByDate[$date][] = $match;
}

// Navigation dates
$prevMonth = $currentMonth - 1;
$prevYear = $currentYear;
if ($prevMonth < 1) {
    $prevMonth = 12;
    $prevYear--;
}

$nextMonth = $currentMonth + 1;
$nextYear = $currentYear;
if ($nextMonth > 12) {
    $nextMonth = 1;
    $nextYear++;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Venues Calendar - <?php echo APP_NAME; ?></title>
    <link rel="stylesheet" href="../../assets/css/admin.css">
    <link rel="icon" type="image/x-icon" href="../../assets/images/favicon.ico">
    <style>
        .calendar-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            background: #e0e0e0;
            border: 1px solid #e0e0e0;
        }
        .calendar-day-header {
            background: #f5f5f5;
            padding: 10px;
            text-align: center;
            font-weight: bold;
            color: #666;
        }
        .calendar-day {
            background: white;
            min-height: 100px;
            padding: 8px;
            position: relative;
        }
        .calendar-day.other-month {
            background: #f9f9f9;
            color: #ccc;
        }
        .calendar-day.today {
            background: #e3f2fd;
        }
        .day-number {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .day-matches {
            font-size: 11px;
        }
        .match-indicator {
            display: block;
            padding: 2px 4px;
            margin: 1px 0;
            border-radius: 3px;
            background: #007cba;
            color: white;
            text-decoration: none;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .match-indicator.status-ongoing {
            background: #ff9800;
        }
        .match-indicator.status-completed {
            background: #4caf50;
        }
        .match-indicator.status-cancelled {
            background: #f44336;
        }
        .legend {
            display: flex;
            gap: 20px;
            margin-top: 20px;
            flex-wrap: wrap;
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 3px;
        }
    </style>
</head>
<body class="admin-page">
    <?php include '../includes/header.php'; ?>
    <?php include '../includes/sidebar.php'; ?>
    
    <main class="main-content">
        <div class="page-header">
            <div class="page-title">
                <h1>Venues Calendar</h1>
                <nav class="breadcrumb">
                    <a href="../dashboard.php">Dashboard</a>
                    <span>/</span>
                    <a href="index.php">Venues</a>
                    <span>/</span>
                    <span>Calendar</span>
                </nav>
            </div>
            <div class="page-actions">
                <a href="index.php" class="btn btn-outline">
                    <i class="icon-arrow-left"></i>
                    Back to Venues
                </a>
            </div>
        </div>
        
        <div class="calendar-container">
            <div class="calendar-header">
                <div class="calendar-navigation">
                    <a href="?month=<?php echo $prevMonth; ?>&year=<?php echo $prevYear; ?>" 
                       class="btn btn-sm btn-outline">
                        <i class="icon-chevron-left"></i> Previous
                    </a>
                    <h2><?php echo date('F Y', $firstDay); ?></h2>
                    <a href="?month=<?php echo $nextMonth; ?>&year=<?php echo $nextYear; ?>" 
                       class="btn btn-sm btn-outline">
                        Next <i class="icon-chevron-right"></i>
                    </a>
                </div>
                
                <div class="calendar-actions">
                    <a href="?month=<?php echo date('n'); ?>&year=<?php echo date('Y'); ?>" 
                       class="btn btn-sm btn-secondary">Today</a>
                </div>
            </div>
            
            <div class="calendar-grid">
                <!-- Day headers -->
                <div class="calendar-day-header">Sun</div>
                <div class="calendar-day-header">Mon</div>
                <div class="calendar-day-header">Tue</div>
                <div class="calendar-day-header">Wed</div>
                <div class="calendar-day-header">Thu</div>
                <div class="calendar-day-header">Fri</div>
                <div class="calendar-day-header">Sat</div>
                
                <?php
                // Add empty cells for days before the first day of the month
                for ($i = 0; $i < $startDayOfWeek; $i++) {
                    $prevMonthDay = date('j', mktime(0, 0, 0, $currentMonth, -$startDayOfWeek + $i + 1, $currentYear));
                    echo '<div class="calendar-day other-month">';
                    echo '<div class="day-number">' . $prevMonthDay . '</div>';
                    echo '</div>';
                }
                
                // Add days of the current month
                for ($day = 1; $day <= $daysInMonth; $day++) {
                    $currentDate = sprintf('%04d-%02d-%02d', $currentYear, $currentMonth, $day);
                    $isToday = $currentDate === date('Y-m-d');
                    
                    echo '<div class="calendar-day' . ($isToday ? ' today' : '') . '">';
                    echo '<div class="day-number">' . $day . '</div>';
                    
                    if (isset($matchesByDate[$currentDate])) {
                        echo '<div class="day-matches">';
                        foreach ($matchesByDate[$currentDate] as $match) {
                            $statusClass = 'status-' . $match['status'];
                            echo '<a href="schedule.php?venue_id=' . $match['venue_id'] . '&date=' . $currentDate . '" ';
                            echo 'class="match-indicator ' . $statusClass . '" ';
                            echo 'title="' . htmlspecialchars($match['venue_name']) . ' - ' . $match['match_count'] . ' matches">';
                            echo htmlspecialchars($match['venue_name']) . ' (' . $match['match_count'] . ')';
                            echo '</a>';
                        }
                        echo '</div>';
                    }
                    
                    echo '</div>';
                }
                
                // Add empty cells for days after the last day of the month
                $totalCells = $startDayOfWeek + $daysInMonth;
                $remainingCells = 42 - $totalCells; // 6 rows * 7 days = 42 cells
                if ($remainingCells > 7) $remainingCells -= 7; // Only show 5 or 6 rows
                
                for ($i = 1; $i <= $remainingCells; $i++) {
                    echo '<div class="calendar-day other-month">';
                    echo '<div class="day-number">' . $i . '</div>';
                    echo '</div>';
                }
                ?>
            </div>
            
            <!-- Legend -->
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background: #007cba;"></div>
                    <span>Scheduled</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #ff9800;"></div>
                    <span>Ongoing</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #4caf50;"></div>
                    <span>Completed</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #f44336;"></div>
                    <span>Cancelled</span>
                </div>
            </div>
        </div>
        
        <!-- Venues Summary -->
        <div class="venues-summary">
            <h3>Venues Overview</h3>
            <div class="venues-grid">
                <?php foreach ($venues as $venue): ?>
                    <div class="venue-summary-card">
                        <h4><?php echo htmlspecialchars($venue['name']); ?></h4>
                        <span class="status-badge status-<?php echo $venue['status']; ?>">
                            <?php echo ucfirst($venue['status']); ?>
                        </span>
                        <div class="venue-actions">
                            <a href="view.php?id=<?php echo $venue['venue_id']; ?>" class="btn btn-xs btn-outline">View</a>
                            <a href="schedule.php?venue_id=<?php echo $venue['venue_id']; ?>" class="btn btn-xs btn-primary">Schedule</a>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </main>
    
    <script src="../../assets/js/admin.js"></script>
</body>
</html>
