<?php
/**
 * Additional Helper Functions for SCIMS
 */

if (!defined('SCIMS_ACCESS')) {
    die('Direct access not permitted');
}

/**
 * Calculate department standings
 */
function calculateStandings($eventId) {
    try {
        $db = getDB();
        
        // Get all departments
        $departments = fetchAll("SELECT dept_id FROM departments WHERE status = 'active'");
        
        foreach ($departments as $dept) {
            $deptId = $dept['dept_id'];
            
            // Calculate total points from scores
            $pointsResult = fetchOne("
                SELECT COALESCE(SUM(s.points), 0) as total_points
                FROM scores s
                JOIN matches m ON s.match_id = m.match_id
                JOIN match_participants mp ON s.participant_id = mp.participant_id
                WHERE mp.dept_id = ? AND m.event_id = ? AND s.is_final = 1
            ", [$deptId, $eventId]);
            
            $totalPoints = $pointsResult['total_points'];
            
            // Calculate medals
            $medals = fetchOne("
                SELECT 
                    COUNT(CASE WHEN s.position = 1 THEN 1 END) as gold,
                    COUNT(CASE WHEN s.position = 2 THEN 1 END) as silver,
                    COUNT(CASE WHEN s.position = 3 THEN 1 END) as bronze
                FROM scores s
                JOIN matches m ON s.match_id = m.match_id
                JOIN match_participants mp ON s.participant_id = mp.participant_id
                WHERE mp.dept_id = ? AND m.event_id = ? AND s.is_final = 1 AND s.position <= 3
            ", [$deptId, $eventId]);
            
            // Calculate match statistics
            $matchStats = fetchOne("
                SELECT 
                    COUNT(DISTINCT m.match_id) as matches_played,
                    COUNT(CASE WHEN m.winner_id = mp.participant_id THEN 1 END) as matches_won
                FROM matches m
                JOIN match_participants mp ON m.match_id = mp.match_id
                WHERE mp.dept_id = ? AND m.event_id = ? AND m.status = 'completed'
            ", [$deptId, $eventId]);
            
            $matchesPlayed = $matchStats['matches_played'];
            $matchesWon = $matchStats['matches_won'];
            $matchesLost = $matchesPlayed - $matchesWon;
            
            // Update or insert standings
            $existingStanding = fetchOne("
                SELECT standing_id FROM department_standings 
                WHERE dept_id = ? AND event_id = ?
            ", [$deptId, $eventId]);
            
            $standingData = [
                'total_points' => $totalPoints,
                'medals_gold' => $medals['gold'],
                'medals_silver' => $medals['silver'],
                'medals_bronze' => $medals['bronze'],
                'matches_played' => $matchesPlayed,
                'matches_won' => $matchesWon,
                'matches_lost' => $matchesLost,
                'matches_drawn' => 0 // Can be calculated if draws are supported
            ];
            
            if ($existingStanding) {
                updateRecord('department_standings', $standingData, 
                    'dept_id = :dept_id AND event_id = :event_id', 
                    ['dept_id' => $deptId, 'event_id' => $eventId]);
            } else {
                $standingData['dept_id'] = $deptId;
                $standingData['event_id'] = $eventId;
                insertRecord('department_standings', $standingData);
            }
        }
        
        // Update rank positions
        $standings = fetchAll("
            SELECT standing_id FROM department_standings 
            WHERE event_id = ? 
            ORDER BY total_points DESC, medals_gold DESC, medals_silver DESC, medals_bronze DESC
        ", [$eventId]);
        
        foreach ($standings as $index => $standing) {
            updateRecord('department_standings', 
                ['rank_position' => $index + 1], 
                'standing_id = :standing_id', 
                ['standing_id' => $standing['standing_id']]);
        }
        
        return true;
        
    } catch (Exception $e) {
        error_log("Error calculating standings: " . $e->getMessage());
        return false;
    }
}

/**
 * Get department color by ID
 */
function getDepartmentColor($deptId) {
    $dept = fetchOne("SELECT color_code FROM departments WHERE dept_id = ?", [$deptId]);
    return $dept['color_code'] ?? '#cccccc';
}

/**
 * Generate match schedule for an event
 */
function generateMatchSchedule($eventId, $sportId, $roundType = 'round_robin') {
    try {
        // Get participating departments for this sport in the event
        $participants = fetchAll("
            SELECT DISTINCT mp.dept_id, d.name, d.abbreviation
            FROM match_participants mp
            JOIN departments d ON mp.dept_id = d.dept_id
            JOIN matches m ON mp.match_id = m.match_id
            WHERE m.event_id = ? AND m.sport_id = ?
        ", [$eventId, $sportId]);
        
        if (count($participants) < 2) {
            throw new Exception('Need at least 2 participants to generate schedule');
        }
        
        $matches = [];
        
        if ($roundType === 'round_robin') {
            // Generate round robin matches
            for ($i = 0; $i < count($participants); $i++) {
                for ($j = $i + 1; $j < count($participants); $j++) {
                    $matches[] = [
                        'participant1' => $participants[$i],
                        'participant2' => $participants[$j]
                    ];
                }
            }
        } elseif ($roundType === 'elimination') {
            // Generate elimination bracket
            // This is a simplified version - you might want to implement proper bracket generation
            $shuffled = $participants;
            shuffle($shuffled);
            
            for ($i = 0; $i < count($shuffled); $i += 2) {
                if (isset($shuffled[$i + 1])) {
                    $matches[] = [
                        'participant1' => $shuffled[$i],
                        'participant2' => $shuffled[$i + 1]
                    ];
                }
            }
        }
        
        return $matches;
        
    } catch (Exception $e) {
        error_log("Error generating match schedule: " . $e->getMessage());
        return [];
    }
}

/**
 * Send notification (placeholder for future implementation)
 */
function sendNotification($type, $message, $recipients = []) {
    // This is a placeholder for future notification system
    // Could be email, SMS, push notifications, etc.
    logActivity('notification_sent', "Type: {$type}, Message: {$message}");
    return true;
}

/**
 * Backup database (simplified version)
 */
function backupDatabase() {
    try {
        $backupDir = dirname(__DIR__) . '/backups';
        if (!is_dir($backupDir)) {
            mkdir($backupDir, 0755, true);
        }
        
        $filename = 'scims_backup_' . date('Y-m-d_H-i-s') . '.sql';
        $filepath = $backupDir . '/' . $filename;
        
        // This is a simplified backup - in production you'd use mysqldump
        $tables = ['admin_users', 'events', 'departments', 'sports', 'venues', 'matches', 'match_participants', 'scores', 'department_standings'];
        
        $backup = "-- SCIMS Database Backup\n";
        $backup .= "-- Generated on " . date('Y-m-d H:i:s') . "\n\n";
        
        foreach ($tables as $table) {
            if (tableExists($table)) {
                $backup .= "-- Table: {$table}\n";
                $backup .= "DROP TABLE IF EXISTS `{$table}`;\n";
                
                // Get table structure (simplified)
                $createTable = fetchOne("SHOW CREATE TABLE `{$table}`");
                if ($createTable) {
                    $backup .= $createTable['Create Table'] . ";\n\n";
                }
                
                // Get table data
                $rows = fetchAll("SELECT * FROM `{$table}`");
                if (!empty($rows)) {
                    $backup .= "INSERT INTO `{$table}` VALUES\n";
                    $values = [];
                    foreach ($rows as $row) {
                        $values[] = "('" . implode("','", array_map('addslashes', $row)) . "')";
                    }
                    $backup .= implode(",\n", $values) . ";\n\n";
                }
            }
        }
        
        file_put_contents($filepath, $backup);
        
        logActivity('database_backup', "Backup created: {$filename}");
        return $filename;
        
    } catch (Exception $e) {
        error_log("Error creating backup: " . $e->getMessage());
        return false;
    }
}

?>
