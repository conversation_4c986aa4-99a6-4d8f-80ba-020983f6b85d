<?php
/**
 * Samar College Intramurals Management System (SCIMS)
 * Public Schedule Page
 * 
 * @version 1.0
 * <AUTHOR> Development Team
 */

define('SCIMS_ACCESS', true);
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Get filter parameters
$dateFilter = $_GET['date'] ?? '';
$sportFilter = $_GET['sport'] ?? '';
$deptFilter = $_GET['dept'] ?? '';
$statusFilter = $_GET['status'] ?? '';

// Build WHERE clause
$whereConditions = [];
$params = [];

if ($dateFilter) {
    $whereConditions[] = "m.match_date = ?";
    $params[] = $dateFilter;
}

if ($sportFilter) {
    $whereConditions[] = "s.sport_id = ?";
    $params[] = $sportFilter;
}

if ($deptFilter) {
    $whereConditions[] = "EXISTS (SELECT 1 FROM match_participants mp2 WHERE mp2.match_id = m.match_id AND mp2.dept_id = ?)";
    $params[] = $deptFilter;
}

if ($statusFilter) {
    $whereConditions[] = "m.status = ?";
    $params[] = $statusFilter;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// Get matches
$matches = fetchAll("
    SELECT m.match_id, m.match_date, m.match_time, m.status, m.round_type,
           s.name as sport_name, s.category as sport_category,
           v.name as venue_name, v.location as venue_location,
           GROUP_CONCAT(
               CONCAT(d.abbreviation, ': ', mp.participant_name)
               ORDER BY mp.participant_id SEPARATOR ' vs '
           ) as participants,
           GROUP_CONCAT(d.color_code ORDER BY mp.participant_id SEPARATOR ',') as dept_colors
    FROM matches m
    JOIN sports s ON m.sport_id = s.sport_id
    LEFT JOIN venues v ON m.venue_id = v.venue_id
    LEFT JOIN match_participants mp ON m.match_id = mp.match_id
    LEFT JOIN departments d ON mp.dept_id = d.dept_id
    {$whereClause}
    GROUP BY m.match_id
    ORDER BY m.match_date ASC, m.match_time ASC
", $params);

// Get filter options
$sports = fetchAll("SELECT sport_id, name FROM sports WHERE status = 'active' ORDER BY name");
$departments = fetchAll("SELECT dept_id, name, abbreviation FROM departments WHERE status = 'active' ORDER BY name");
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Match Schedule - <?php echo APP_NAME; ?></title>
    <meta name="description" content="View the complete match schedule for Samar College intramural sports events.">
    
    <link rel="stylesheet" href="../assets/css/index.css">
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body>
    <!-- Header -->
    <header class="site-header">
        <div class="container">
            <div class="header-content">
                <a href="index.php" class="site-logo">
                    <img src="../assets/images/logo.png" alt="SCIMS Logo" onerror="this.style.display='none'">
                    <h1>SCIMS</h1>
                </a>
                
                <nav class="main-nav">
                    <ul class="nav-menu">
                        <li class="nav-item"><a href="index.php">Home</a></li>
                        <li class="nav-item"><a href="schedule.php" class="active">Schedule</a></li>
                        <li class="nav-item"><a href="standings.php">Standings</a></li>
                        <li class="nav-item"><a href="results.php">Results</a></li>
                    </ul>
                    
                    <button class="mobile-menu-toggle" aria-label="Toggle mobile menu">
                        ☰
                    </button>
                </nav>
            </div>
        </div>
    </header>
    
    <!-- Main Content -->
    <main>
        <section class="section">
            <div class="container">
                <div class="section-header">
                    <h1>Match Schedule</h1>
                    <p>Complete schedule of all intramural matches</p>
                </div>
                
                <!-- Filters -->
                <div class="filters-container">
                    <form method="GET" class="filters-form">
                        <div class="filter-group">
                            <label for="date">Date:</label>
                            <input type="date" id="date" name="date" value="<?php echo htmlspecialchars($dateFilter); ?>">
                        </div>
                        
                        <div class="filter-group">
                            <label for="sport">Sport:</label>
                            <select id="sport" name="sport">
                                <option value="">All Sports</option>
                                <?php foreach ($sports as $sport): ?>
                                    <option value="<?php echo $sport['sport_id']; ?>" 
                                            <?php echo $sportFilter == $sport['sport_id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($sport['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="dept">Department:</label>
                            <select id="dept" name="dept">
                                <option value="">All Departments</option>
                                <?php foreach ($departments as $dept): ?>
                                    <option value="<?php echo $dept['dept_id']; ?>" 
                                            <?php echo $deptFilter == $dept['dept_id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($dept['abbreviation']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="status">Status:</label>
                            <select id="status" name="status">
                                <option value="">All Status</option>
                                <option value="scheduled" <?php echo $statusFilter === 'scheduled' ? 'selected' : ''; ?>>Scheduled</option>
                                <option value="ongoing" <?php echo $statusFilter === 'ongoing' ? 'selected' : ''; ?>>Ongoing</option>
                                <option value="completed" <?php echo $statusFilter === 'completed' ? 'selected' : ''; ?>>Completed</option>
                            </select>
                        </div>
                        
                        <div class="filter-actions">
                            <button type="submit" class="btn btn-primary">Filter</button>
                            <a href="schedule.php" class="btn btn-secondary">Clear</a>
                        </div>
                    </form>
                </div>
                
                <!-- Schedule Table -->
                <div class="table-container">
                    <?php if (!empty($matches)): ?>
                        <table class="schedule-table" data-live-update="/api/schedule-updates.php">
                            <thead>
                                <tr>
                                    <th>Date & Time</th>
                                    <th>Sport</th>
                                    <th>Participants</th>
                                    <th>Venue</th>
                                    <th>Round</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($matches as $match): ?>
                                    <tr class="match-row" data-match-id="<?php echo $match['match_id']; ?>">
                                        <td class="match-datetime">
                                            <div class="date"><?php echo formatDate($match['match_date']); ?></div>
                                            <div class="time"><?php echo formatTime($match['match_time']); ?></div>
                                        </td>
                                        <td class="sport-info">
                                            <div class="sport-name"><?php echo htmlspecialchars($match['sport_name']); ?></div>
                                            <div class="sport-category"><?php echo ucfirst($match['sport_category']); ?></div>
                                        </td>
                                        <td class="participants">
                                            <?php 
                                            $participants = explode(' vs ', $match['participants'] ?? 'TBD');
                                            $colors = explode(',', $match['dept_colors'] ?? '');
                                            
                                            foreach ($participants as $index => $participant) {
                                                $color = $colors[$index] ?? '#666';
                                                echo '<div class="participant">';
                                                echo '<span class="dept-color" style="background-color: ' . htmlspecialchars($color) . '"></span>';
                                                echo htmlspecialchars($participant);
                                                echo '</div>';
                                                
                                                if ($index < count($participants) - 1) {
                                                    echo '<div class="vs">vs</div>';
                                                }
                                            }
                                            ?>
                                        </td>
                                        <td class="venue-info">
                                            <?php if ($match['venue_name']): ?>
                                                <div class="venue-name"><?php echo htmlspecialchars($match['venue_name']); ?></div>
                                                <?php if ($match['venue_location']): ?>
                                                    <div class="venue-location"><?php echo htmlspecialchars($match['venue_location']); ?></div>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="text-muted">TBD</span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="round-type">
                                            <span class="round-badge round-<?php echo $match['round_type']; ?>">
                                                <?php echo ucfirst(str_replace('_', ' ', $match['round_type'])); ?>
                                            </span>
                                        </td>
                                        <td class="match-status">
                                            <span class="status-badge status-<?php echo $match['status']; ?>">
                                                <?php echo ucfirst($match['status']); ?>
                                                <?php if ($match['status'] === 'ongoing'): ?>
                                                    <span class="live-indicator">🔴</span>
                                                <?php endif; ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php else: ?>
                        <div class="no-data">
                            <h3>No matches found</h3>
                            <p>No matches match your current filter criteria.</p>
                            <a href="schedule.php" class="btn btn-primary">View All Matches</a>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Quick Stats -->
                <div class="schedule-stats">
                    <div class="stat-card">
                        <h3><?php echo count(array_filter($matches, fn($m) => $m['status'] === 'scheduled')); ?></h3>
                        <p>Scheduled</p>
                    </div>
                    <div class="stat-card">
                        <h3><?php echo count(array_filter($matches, fn($m) => $m['status'] === 'ongoing')); ?></h3>
                        <p>Ongoing</p>
                    </div>
                    <div class="stat-card">
                        <h3><?php echo count(array_filter($matches, fn($m) => $m['status'] === 'completed')); ?></h3>
                        <p>Completed</p>
                    </div>
                    <div class="stat-card">
                        <h3><?php echo count($matches); ?></h3>
                        <p>Total Matches</p>
                    </div>
                </div>
            </div>
        </section>
    </main>
    
    <!-- Footer -->
    <footer class="site-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>SCIMS</h3>
                    <p>Samar College Intramurals Management System</p>
                </div>
                
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.php">Home</a></li>
                        <li><a href="schedule.php">Schedule</a></li>
                        <li><a href="standings.php">Standings</a></li>
                        <li><a href="results.php">Results</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; <?php echo date('Y'); ?> Samar College. All rights reserved.</p>
            </div>
        </div>
    </footer>
    
    <script src="../assets/js/main.js"></script>
</body>
</html>
