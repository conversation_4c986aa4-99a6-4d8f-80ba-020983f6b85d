# Samar College Intramurals Management System (SCIMS)

A comprehensive web-based sports event management system designed for educational institutions to manage intramural competitions, track scores, and maintain department standings.

## 🏆 Features

### Core Functionality
- **Multi-Sport Event Management** - Support for 10+ different sport types
- **Real-time Scoring System** - AJAX-powered live updates every 30 seconds
- **Tournament Management** - Single/double elimination and round-robin formats
- **Department Ranking System** - Configurable point system with real-time leaderboards
- **Venue Management** - Schedule and track venue usage
- **Official Assignment** - Manage referees and match officials

### Admin Portal
- **Secure Authentication** - Multi-level access control with session management
- **Event Creation Wizard** - Step-by-step event setup process
- **Bulk Operations** - Schedule entire tournament brackets at once
- **Score Entry System** - Double confirmation for final scores
- **Organizer Access** - Delegated scoring with unique tokens
- **Reports & Analytics** - Export functionality in PDF and Excel formats

### Public Interface
- **Live Score Ticker** - Real-time match updates
- **Responsive Design** - Mobile-first approach supporting 320px to 1920px
- **Progressive Web App** - Offline viewing capabilities
- **Search Functionality** - Find matches, departments, and results
- **Social Sharing** - Share match results and standings

## 🛠️ Technical Specifications

### Requirements
- **PHP** 8.0+ with PDO MySQL extension
- **MySQL** 8.0+ or MariaDB 10.3+
- **Web Server** Apache 2.4+ or Nginx 1.18+
- **Browser Support** Chrome 90+, Firefox 88+, Safari 14+, Edge 90+

### Technology Stack
- **Backend**: PHP 8.0+ with strict typing
- **Database**: MySQL 8.0+ with optimized indexing
- **Frontend**: HTML5, CSS3 (Grid/Flexbox), Vanilla JavaScript ES6+
- **Security**: CSRF protection, password hashing, session validation
- **Performance**: Caching, lazy loading, optimized queries

## 📁 Project Structure

```
ims/
├── admin/                  # Admin panel
│   ├── index.php          # Admin login
│   ├── dashboard.php      # Main dashboard
│   ├── events/            # Event management
│   ├── departments/       # Department management
│   ├── matches/           # Match management
│   └── reports/           # Reports and analytics
├── public/                # Public interface
│   ├── index.php          # Homepage
│   ├── schedule.php       # Match schedule
│   ├── standings.php      # Department standings
│   └── results.php        # Match results
├── assets/                # Static assets
│   ├── css/               # Stylesheets
│   ├── js/                # JavaScript files
│   └── images/            # Images and uploads
├── includes/              # Core PHP files
│   ├── config.php         # Database configuration
│   ├── functions.php      # Common functions
│   └── auth.php           # Authentication functions
├── sql/                   # Database files
│   └── database_schema.sql # Database schema
├── setup.php              # Installation script
└── README.md              # This file
```

## 🚀 Installation

### Method 1: Automated Setup (Recommended)

1. **Download and Extract**
   ```bash
   # Extract SCIMS files to your web directory
   # For XAMPP: C:\xampp\htdocs\IMS\
   # For WAMP: C:\wamp64\www\IMS\
   # For Linux: /var/www/html/IMS/
   ```

2. **Run Setup Script**
   - Navigate to `http://localhost/IMS/setup.php`
   - Follow the 5-step installation wizard:
     - Step 1: Database Configuration
     - Step 2: Database Schema Import
     - Step 3: Admin User Creation
     - Step 4: Application Settings
     - Step 5: Installation Complete

3. **Security**
   - Delete `setup.php` after installation
   - Verify `.htaccess` file is created for security

### Method 2: Manual Installation

1. **Database Setup**
   ```sql
   CREATE DATABASE IMS_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

2. **Import Schema**
   ```bash
   mysql -u root -p IMS_db < sql/database_schema.sql
   ```

3. **Configure Database**
   - Copy `includes/config.php.example` to `includes/config.php`
   - Update database credentials

4. **Set Permissions**
   ```bash
   chmod 755 assets/images/uploads/
   chmod 755 logs/
   chmod 755 backups/
   ```

## 🔧 Configuration

### Database Configuration
```php
// includes/config.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'IMS_db');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### Application Settings
```php
define('APP_URL', 'http://localhost/IMS');
define('SESSION_TIMEOUT', 1800); // 30 minutes
define('MAX_LOGIN_ATTEMPTS', 5);
define('UPLOAD_MAX_SIZE', 2097152); // 2MB
```

## 👤 Default Admin Account

After installation, use these credentials to access the admin panel:

- **URL**: `http://localhost/IMS/admin/`
- **Username**: `admin`
- **Password**: `Admin123!`

**⚠️ Important**: Change the default password immediately after first login.

## 📖 Usage Guide

### Setting Up Your First Event

1. **Login to Admin Panel**
   - Navigate to `/admin/`
   - Use default credentials or your created admin account

2. **Create Event**
   - Go to Events → Create New Event
   - Fill in event details (name, dates, description)
   - Configure point system (1st: 15pts, 2nd: 12pts, etc.)

3. **Add Departments**
   - Go to Departments → Add Department
   - Enter department details and assign colors

4. **Configure Sports**
   - Go to Sports → Manage Sports
   - Enable sports for your event
   - Set participant limits and scoring types

5. **Set Up Venues**
   - Go to Venues → Add Venue
   - Configure capacity and facilities

6. **Schedule Matches**
   - Go to Matches → Create Schedule
   - Use bulk scheduling for tournaments
   - Assign venues and officials

### Managing Live Events

1. **Score Entry**
   - Navigate to ongoing matches
   - Enter scores with double confirmation
   - Scores update automatically in standings

2. **Live Updates**
   - Public interface updates every 30 seconds
   - Live ticker shows ongoing match scores
   - Standings recalculate automatically

3. **Reports**
   - Generate PDF/Excel reports
   - Export match results and standings
   - Create certificates and awards lists

## 🔒 Security Features

### Authentication
- Password strength requirements (8+ chars, mixed case, numbers, symbols)
- Account lockout after 5 failed attempts
- Session timeout and IP validation
- CSRF token protection

### Data Protection
- SQL injection prevention with prepared statements
- XSS protection with input sanitization
- File upload validation and size limits
- Secure password hashing with PHP's password_hash()

### Access Control
- Role-based permissions (Super Admin, Admin, Organizer)
- Session security with IP and user agent validation
- Organizer tokens with 24-hour expiry
- Activity logging for audit trails

## 🎨 Customization

### Theming
- CSS custom properties for easy color changes
- Responsive design with mobile-first approach
- Department colors for visual identification
- Custom logos and branding support

### Point System
- Configurable scoring for each sport type
- Flexible medal point distribution
- Tie-breaking rules customization
- Performance-based seeding options

## 📊 API Endpoints

### Public API
- `GET /api/live-scores.php` - Current match scores
- `GET /api/standings.php` - Department standings
- `GET /api/schedule.php` - Match schedule
- `GET /api/search.php` - Search functionality

### Admin API
- `POST /api/score-update.php` - Update match scores
- `GET /api/dashboard-stats.php` - Dashboard statistics
- `POST /api/bulk-schedule.php` - Bulk match scheduling

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Verify MySQL service is running
   - Check database credentials in config.php
   - Ensure database exists and user has proper permissions

2. **File Upload Errors**
   - Check PHP upload_max_filesize setting
   - Verify directory permissions (755 for uploads/)
   - Ensure file types are allowed

3. **Session Timeout Issues**
   - Adjust SESSION_TIMEOUT in config.php
   - Check PHP session.gc_maxlifetime setting
   - Verify session directory permissions

4. **Live Updates Not Working**
   - Check JavaScript console for errors
   - Verify AJAX endpoints are accessible
   - Ensure proper CORS headers if needed

### Performance Optimization

1. **Database**
   - Enable MySQL query cache
   - Add indexes for frequently searched columns
   - Use EXPLAIN to optimize slow queries

2. **Web Server**
   - Enable gzip compression
   - Set proper cache headers for static assets
   - Use CDN for image delivery

3. **PHP**
   - Enable OPcache for better performance
   - Increase memory_limit if needed
   - Use APCu for user cache if available

## 📝 Development

### Contributing
1. Fork the repository
2. Create a feature branch
3. Follow PSR-12 coding standards
4. Add tests for new functionality
5. Submit a pull request

### Testing
```bash
# Run PHP unit tests
phpunit tests/

# Run JavaScript tests
npm test

# Check code style
phpcs --standard=PSR12 includes/ admin/ public/
```

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Support

For support and questions:
- **Email**: <EMAIL>
- **Documentation**: https://docs.scims.dev
- **Issues**: https://github.com/scims/scims/issues

## 🙏 Acknowledgments

- Samar College for project requirements and testing
- PHP community for excellent documentation
- Contributors and beta testers

---

**Version**: 1.0.0  
**Last Updated**: 2024  
**Developed by**: SCIMS Development Team
